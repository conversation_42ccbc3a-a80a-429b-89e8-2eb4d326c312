# FamEduConnect Enterprise Functionality Testing Script
# This script runs comprehensive tests, health checks, and DR tests

param(
    [string]$Environment = "production",
    [switch]$SkipDRTests,
    [switch]$Verbose
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "================================`n$Message`n================================" -ForegroundColor Blue
}

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Message = ""
    )
    
    if ($Success) {
        Write-Host "✓ $TestName" -ForegroundColor Green
        if ($Message) { Write-Host "  $Message" -ForegroundColor Gray }
    } else {
        Write-Host "✗ $TestName" -ForegroundColor Red
        if ($Message) { Write-Host "  $Message" -ForegroundColor Red }
    }
}

# Function to run command and capture output
function Invoke-CommandWithOutput {
    param(
        [string]$Command,
        [string]$Description
    )
    
    Write-Host "Running: $Description" -ForegroundColor Cyan
    if ($Verbose) {
        Write-Host "Command: $Command" -ForegroundColor Gray
    }
    
    try {
        $result = Invoke-Expression $Command 2>&1
        $success = $LASTEXITCODE -eq 0
        Write-TestResult $Description $success
        if ($Verbose -and $result) {
            Write-Host $result -ForegroundColor Gray
        }
        return $success
    } catch {
        Write-TestResult $Description $false $_.Exception.Message
        return $false
    }
}

# Function to test Kubernetes connectivity
function Test-KubernetesConnectivity {
    Write-Header "Testing Kubernetes Connectivity"
    
    $tests = @(
        @{ Command = "kubectl cluster-info"; Description = "Cluster Information" },
        @{ Command = "kubectl get nodes"; Description = "Node Status" },
        @{ Command = "kubectl get namespaces"; Description = "Namespace List" }
    )
    
    $successCount = 0
    foreach ($test in $tests) {
        if (Invoke-CommandWithOutput $test.Command $test.Description) {
            $successCount++
        }
    }
    
    return $successCount -eq $tests.Count
}

# Function to test namespace and resources
function Test-NamespaceResources {
    Write-Header "Testing Namespace Resources"
    
    $tests = @(
        @{ Command = "kubectl get pods -n fameduconnect"; Description = "Pod Status" },
        @{ Command = "kubectl get services -n fameduconnect"; Description = "Service Status" },
        @{ Command = "kubectl get secrets -n fameduconnect"; Description = "Secrets Status" },
        @{ Command = "kubectl get configmaps -n fameduconnect"; Description = "ConfigMaps Status" },
        @{ Command = "kubectl get deployments -n fameduconnect"; Description = "Deployment Status" }
    )
    
    $successCount = 0
    foreach ($test in $tests) {
        if (Invoke-CommandWithOutput $test.Command $test.Description) {
            $successCount++
        }
    }
    
    return $successCount -eq $tests.Count
}

# Function to test database connectivity
function Test-DatabaseConnectivity {
    Write-Header "Testing Database Connectivity"
    
    # Get a pod name for database testing
    $podName = kubectl get pods -n fameduconnect -l app=fameduconnect-backend -o jsonpath='{.items[0].metadata.name}' 2>$null
    
    if (-not $podName) {
        Write-Error "No backend pod found for database testing"
        return $false
    }
    
    $tests = @(
        @{ Command = "kubectl exec -n fameduconnect $podName -- pg_isready -h postgres-primary -p 5432"; Description = "PostgreSQL Connectivity" },
        @{ Command = "kubectl exec -n fameduconnect $podName -- redis-cli -h redis-cluster -p 6379 ping"; Description = "Redis Connectivity" }
    )
    
    $successCount = 0
    foreach ($test in $tests) {
        if (Invoke-CommandWithOutput $test.Command $test.Description) {
            $successCount++
        }
    }
    
    return $successCount -eq $tests.Count
}

# Function to test application health
function Test-ApplicationHealth {
    Write-Header "Testing Application Health"
    
    # Get service URLs
    $apiService = kubectl get svc -n fameduconnect fameduconnect-backend -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>$null
    if (-not $apiService) {
        $apiService = "localhost"
    }
    
    $tests = @(
        @{ Command = "curl -f http://$apiService`:5555/health"; Description = "Backend Health Check" },
        @{ Command = "curl -f http://$apiService`:5555/api/test"; Description = "API Test Endpoint" }
    )
    
    $successCount = 0
    foreach ($test in $tests) {
        if (Invoke-CommandWithOutput $test.Command $test.Description) {
            $successCount++
        }
    }
    
    return $successCount -eq $tests.Count
}

# Function to test monitoring
function Test-Monitoring {
    Write-Header "Testing Monitoring Systems"
    
    $tests = @(
        @{ Command = "kubectl get pods -n fameduconnect -l app=elasticsearch"; Description = "Elasticsearch Status" },
        @{ Command = "kubectl get pods -n fameduconnect -l app=grafana"; Description = "Grafana Status" },
        @{ Command = "kubectl get pods -n fameduconnect -l app=prometheus"; Description = "Prometheus Status" },
        @{ Command = "kubectl get pods -n fameduconnect -l app=fluentd"; Description = "Fluentd Status" }
    )
    
    $successCount = 0
    foreach ($test in $tests) {
        if (Invoke-CommandWithOutput $test.Command $test.Description) {
            $successCount++
        }
    }
    
    return $successCount -eq $tests.Count
}

# Function to test backup and DR
function Test-BackupAndDR {
    Write-Header "Testing Backup and Disaster Recovery"
    
    $tests = @(
        @{ Command = "kubectl get cronjobs -n fameduconnect"; Description = "Backup CronJobs Status" },
        @{ Command = "kubectl get pvc -n fameduconnect"; Description = "Persistent Volume Claims" },
        @{ Command = "kubectl get jobs -n fameduconnect -l app=backup"; Description = "Backup Jobs Status" }
    )
    
    $successCount = 0
    foreach ($test in $tests) {
        if (Invoke-CommandWithOutput $test.Command $test.Description) {
            $successCount++
        }
    }
    
    return $successCount -eq $tests.Count
}

# Function to test security
function Test-Security {
    Write-Header "Testing Security Configuration"
    
    $tests = @(
        @{ Command = "kubectl get networkpolicies -n fameduconnect"; Description = "Network Policies" },
        @{ Command = "kubectl get rbac -n fameduconnect"; Description = "RBAC Configuration" },
        @{ Command = "kubectl get secrets -n fameduconnect --field-selector type=kubernetes.io/tls"; Description = "TLS Secrets" }
    )
    
    $successCount = 0
    foreach ($test in $tests) {
        if (Invoke-CommandWithOutput $test.Command $test.Description) {
            $successCount++
        }
    }
    
    return $successCount -eq $tests.Count
}

# Function to test auto-scaling
function Test-AutoScaling {
    Write-Header "Testing Auto-Scaling Configuration"
    
    $tests = @(
        @{ Command = "kubectl get hpa -n fameduconnect"; Description = "Horizontal Pod Autoscalers" },
        @{ Command = "kubectl get deployments -n fameduconnect -o jsonpath='{.items[*].spec.replicas}'"; Description = "Deployment Replicas" }
    )
    
    $successCount = 0
    foreach ($test in $tests) {
        if (Invoke-CommandWithOutput $test.Command $test.Description) {
            $successCount++
        }
    }
    
    return $successCount -eq $tests.Count
}

# Function to run load tests
function Test-LoadTesting {
    Write-Header "Running Load Tests"
    
    # Get service URLs
    $apiService = kubectl get svc -n fameduconnect fameduconnect-backend -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>$null
    if (-not $apiService) {
        $apiService = "localhost"
    }
    
    Write-Status "Running basic load test on API endpoint..."
    
    # Simple load test using curl
    $loadTestCommand = "for (`$i=1; `$i -le 10; `$i++) { curl -s -o /dev/null -w '%{http_code}' http://$apiService`:5555/health; echo ''; }"
    
    try {
        $results = Invoke-Expression $loadTestCommand
        $successCount = ($results | Where-Object { $_ -eq "200" }).Count
        $successRate = ($successCount / 10) * 100
        
        Write-TestResult "Load Test (10 requests)" ($successRate -ge 90) "Success rate: $successRate%"
        return $successRate -ge 90
    } catch {
        Write-TestResult "Load Test" $false $_.Exception.Message
        return $false
    }
}

# Function to generate test report
function New-TestReport {
    param(
        [hashtable]$Results
    )
    
    $reportPath = "enterprise/test-reports/functionality-test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').md"
    New-Item -ItemType Directory -Path "enterprise/test-reports" -Force | Out-Null
    
    $report = @"
# FamEduConnect Enterprise Functionality Test Report
Generated on: $(Get-Date)

## Test Summary

| Test Category | Status | Details |
|---------------|--------|---------|
"@
    
    foreach ($category in $Results.Keys) {
        $status = if ($Results[$category]) { "✅ PASS" } else { "❌ FAIL" }
        $report += "`n| $category | $status | |"
    }
    
    $report += @"

## Detailed Results

### Environment Information
- Environment: $Environment
- Test Date: $(Get-Date)
- Kubernetes Version: $(kubectl version --short 2>$null)

### Recommendations
"@
    
    $failedTests = $Results.Keys | Where-Object { -not $Results[$_] }
    if ($failedTests) {
        $report += "`n**Failed Tests:**`n"
        foreach ($test in $failedTests) {
            $report += "- $test`n"
        }
        $report += "`n**Action Required:** Review and fix the failed tests before proceeding to production.`n"
    } else {
        $report += "`n**All tests passed successfully!** The system is ready for production.`n"
    }
    
    Set-Content $reportPath $report
    Write-Status "Test report generated: $reportPath"
    return $reportPath
}

# Main testing function
function Main {
    Write-Header "FamEduConnect Enterprise Functionality Testing"
    
    # Check if running from the correct directory
    if (-not (Test-Path "enterprise/ENTERPRISE_DEPLOYMENT_GUIDE.md")) {
        Write-Error "Please run this script from the FamEduConnect_Full_Codebase directory"
        exit 1
    }
    
    Write-Status "Starting functionality tests for environment: $Environment"
    
    # Initialize results
    $testResults = @{}
    
    # Run all tests
    $testResults["Kubernetes Connectivity"] = Test-KubernetesConnectivity
    $testResults["Namespace Resources"] = Test-NamespaceResources
    $testResults["Database Connectivity"] = Test-DatabaseConnectivity
    $testResults["Application Health"] = Test-ApplicationHealth
    $testResults["Monitoring Systems"] = Test-Monitoring
    $testResults["Auto-Scaling"] = Test-AutoScaling
    $testResults["Security Configuration"] = Test-Security
    
    if (-not $SkipDRTests) {
        $testResults["Backup and DR"] = Test-BackupAndDR
    }
    
    $testResults["Load Testing"] = Test-LoadTesting
    
    # Generate report
    $reportPath = New-TestReport $testResults
    
    # Summary
    Write-Header "Test Summary"
    
    $passedTests = ($testResults.Values | Where-Object { $_ }).Count
    $totalTests = $testResults.Count
    $passRate = ($passedTests / $totalTests) * 100
    
    Write-Status "Tests completed: $passedTests/$totalTests passed ($([math]::Round($passRate, 1))%)"
    
    if ($passRate -eq 100) {
        Write-Status "🎉 All tests passed! The system is ready for production."
    } elseif ($passRate -ge 80) {
        Write-Warning "⚠️  Most tests passed, but some issues need attention."
    } else {
        Write-Error "❌ Multiple test failures detected. Review the report and fix issues."
    }
    
    Write-Status "Detailed report available at: $reportPath"
    
    # Exit with appropriate code
    if ($passRate -eq 100) {
        exit 0
    } else {
        exit 1
    }
}

# Run main function
Main 