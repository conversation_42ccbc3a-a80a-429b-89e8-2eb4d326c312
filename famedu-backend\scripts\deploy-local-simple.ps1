# Deploying FamEduConnect Backend for Local Development (Simplified)

Write-Host "Deploying FamEduConnect Backend for Local Development (Simplified)..." -ForegroundColor Green

# Check if .env.dev exists
if (-not (Test-Path ".env.dev")) {
    Write-Host "Error: .env.dev file not found. Please run Terraform first to generate it." -ForegroundColor Red
    exit 1
}

# Copy .env.dev to .env for the application
Write-Host "Setting up environment variables..." -ForegroundColor Yellow
Copy-Item ".env.dev" ".env" -Force

# Generate Prisma client
Write-Host "Generating Prisma client..." -ForegroundColor Yellow
npx prisma generate

# Check if PostgreSQL is available locally
Write-Host "Checking PostgreSQL availability..." -ForegroundColor Yellow
try {
    $psqlVersion = psql --version 2>$null
    if ($psqlVersion) {
        Write-Host "PostgreSQL client found. You can manually create the database:" -ForegroundColor Green
        Write-Host "psql -U famedu_user -d postgres -c 'CREATE DATABASE fameduconnect_dev;'" -ForegroundColor Cyan
    }
} catch {
    Write-Host "PostgreSQL client not found. Please install PostgreSQL or use Docker." -ForegroundColor Yellow
}

# Check if Docker is available
Write-Host "Checking Docker availability..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version 2>$null
    if ($dockerVersion) {
        Write-Host "Docker found! You can start services with:" -ForegroundColor Green
        Write-Host "docker-compose -f docker-compose.dev.yml up -d" -ForegroundColor Cyan
    } else {
        Write-Host "Docker not found. Please install Docker Desktop and restart your system." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Docker not found. Please install Docker Desktop and restart your system." -ForegroundColor Yellow
}

# Start the NestJS application
Write-Host "Starting NestJS application..." -ForegroundColor Yellow
Write-Host "Note: Make sure PostgreSQL, Redis, and RabbitMQ are running locally or via Docker" -ForegroundColor Cyan
Write-Host "You can start them manually or use: docker-compose -f docker-compose.dev.yml up -d" -ForegroundColor Cyan
Write-Host ""

npm run start:dev

Write-Host "Local deployment complete!" -ForegroundColor Green
Write-Host "Services should be available at:" -ForegroundColor Green
Write-Host "- NestJS Backend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "- Health Check: http://localhost:3000/health" -ForegroundColor Cyan 