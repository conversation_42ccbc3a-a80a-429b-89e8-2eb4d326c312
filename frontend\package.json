{"name": "fameduconnect-frontend", "version": "1.0.0", "description": "FamEduConnect Frontend Application", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.5.0", "babel-plugin-transform-remove-console": "^6.9.4", "date-fns": "^2.30.0", "emoji-picker-react": "^4.5.2", "framer-motion": "^10.16.4", "i18next": "^23.5.1", "image-webpack-loader": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "react-i18next": "^13.2.2", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "react-webcam": "^7.1.1", "recharts": "^2.8.0", "simple-peer": "^9.11.1", "socket.io-client": "^4.7.2", "tailwindcss": "^3.3.3", "webpack-bundle-analyzer": "^4.10.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "start:optimized": "craco start", "build:optimized": "craco build", "analyze": "ANALYZE_BUNDLE=true npm run build:optimized"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "autoprefixer": "^10.4.15", "postcss": "^8.4.29"}, "proxy": "http://localhost:3002"}