@echo off
echo ========================================
echo FamEduConnect - Universal Startup Script
echo ========================================
echo.

echo [1/4] Checking prerequisites...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm not found. Please install npm first.
    pause
    exit /b 1
)

echo [2/4] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies.
    pause
    exit /b 1
)

echo [3/4] Starting servers...
echo.
echo Starting backend on port 3002...
start "Backend" cmd /k "cd backend && npm run dev"

echo Starting frontend on port 3000...
start "Frontend" cmd /k "cd frontend && npm start"

echo Starting admin on port 3001...
start "Admin" cmd /k "cd admin && npm start"

echo [4/4] Waiting for servers to start...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo 🚀 FamEduConnect is starting up!
echo ========================================
echo.
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend:  http://localhost:3002/api
echo 👨‍💼 Admin:    http://localhost:3001
echo.
echo 📋 Test Credentials:
echo    Admin:    <EMAIL> / password123
echo    Teacher:  <EMAIL> / password123
echo    Parent:   <EMAIL> / password123
echo    Student:  <EMAIL> / password123
echo.
echo ⏳ Please wait 30-60 seconds for all servers to fully start...
echo.
pause 