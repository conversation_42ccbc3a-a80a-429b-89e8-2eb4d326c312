version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: fameduconnect
      POSTGRES_USER: fameduconnect_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - fameduconnect

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    networks:
      - fameduconnect

  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - fameduconnect

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./frontend/build:/usr/share/nginx/html/app
      - ./admin/build:/usr/share/nginx/html/admin
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - fameduconnect

volumes:
  postgres_data:

networks:
  fameduconnect:
    driver: bridge