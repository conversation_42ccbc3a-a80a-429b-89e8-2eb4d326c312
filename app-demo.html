<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FamEduConnect - Parent Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #2d3748;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .language-toggle {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
        }
        
        /* Sidebar */
        .container {
            display: flex;
            min-height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 250px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 2rem 0;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 2rem;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background: #667eea;
            color: white;
        }
        
        .nav-icon {
            font-size: 1.2rem;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            padding: 2rem;
        }
        
        .dashboard-header {
            margin-bottom: 2rem;
        }
        
        .dashboard-title {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .dashboard-subtitle {
            color: #718096;
        }
        
        /* Cards Grid */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .card-icon {
            font-size: 2rem;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .card-content {
            color: #718096;
        }
        
        /* Messages Section */
        .messages-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .message-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .message-content {
            flex: 1;
        }
        
        .message-sender {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .message-text {
            color: #718096;
            font-size: 0.9rem;
        }
        
        .message-time {
            color: #a0aec0;
            font-size: 0.8rem;
        }
        
        /* Buttons */
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #48bb78;
        }
        
        .btn-secondary:hover {
            background: #38a169;
        }
        
        /* Video Call Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
        }
        
        .video-container {
            background: #000;
            border-radius: 8px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-bottom: 1rem;
        }
        
        .video-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                display: flex;
                overflow-x: auto;
                padding: 1rem 0;
            }
            
            .nav-item {
                white-space: nowrap;
                padding: 0.5rem 1rem;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="logo">🎓 FamEduConnect</div>
        <div class="user-info">
            <select class="language-toggle" onchange="changeLanguage()">
                <option value="en">🇺🇸 English</option>
                <option value="ar">🇸🇦 العربية</option>
                <option value="es">🇪🇸 Español</option>
            </select>
            <div>👤 Sarah Johnson (Parent)</div>
        </div>
    </header>

    <div class="container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <a href="#" class="nav-item active" onclick="showSection('dashboard')">
                <span class="nav-icon">📊</span>
                <span>Dashboard</span>
            </a>
            <a href="#" class="nav-item" onclick="showSection('messages')">
                <span class="nav-icon">💬</span>
                <span>Messages</span>
            </a>
            <a href="#" class="nav-item" onclick="showSection('grades')">
                <span class="nav-icon">📚</span>
                <span>Grades</span>
            </a>
            <a href="#" class="nav-item" onclick="showSection('homework')">
                <span class="nav-icon">📝</span>
                <span>Homework</span>
            </a>
            <a href="#" class="nav-item" onclick="showSection('calendar')">
                <span class="nav-icon">📅</span>
                <span>Calendar</span>
            </a>
            <a href="#" class="nav-item" onclick="showSection('video')">
                <span class="nav-icon">📹</span>
                <span>Video Calls</span>
            </a>
            <a href="#" class="nav-item" onclick="showSection('settings')">
                <span class="nav-icon">⚙️</span>
                <span>Settings</span>
            </a>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="section">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Welcome back, Sarah!</h1>
                    <p class="dashboard-subtitle">Here's what's happening with Emma's education today</p>
                </div>

                <div class="cards-grid">
                    <div class="card">
                        <div class="card-header">
                            <span class="card-icon">📚</span>
                            <h3 class="card-title">Recent Grades</h3>
                        </div>
                        <div class="card-content">
                            <p><strong>Math:</strong> A- (92%)</p>
                            <p><strong>Science:</strong> B+ (87%)</p>
                            <p><strong>English:</strong> A (95%)</p>
                            <button class="btn" style="margin-top: 1rem;" onclick="showSection('grades')">View All Grades</button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <span class="card-icon">📝</span>
                            <h3 class="card-title">Upcoming Homework</h3>
                        </div>
                        <div class="card-content">
                            <p><strong>Math:</strong> Chapter 5 Problems (Due: Tomorrow)</p>
                            <p><strong>Science:</strong> Lab Report (Due: Friday)</p>
                            <p><strong>English:</strong> Essay Draft (Due: Monday)</p>
                            <button class="btn btn-secondary" style="margin-top: 1rem;" onclick="showSection('homework')">View All Tasks</button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <span class="card-icon">📅</span>
                            <h3 class="card-title">This Week's Events</h3>
                        </div>
                        <div class="card-content">
                            <p><strong>Today:</strong> Parent-Teacher Conference (3:00 PM)</p>
                            <p><strong>Thursday:</strong> Science Fair Presentation</p>
                            <p><strong>Friday:</strong> Math Quiz</p>
                            <button class="btn" style="margin-top: 1rem;" onclick="showSection('calendar')">View Calendar</button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <span class="card-icon">💬</span>
                            <h3 class="card-title">Recent Messages</h3>
                        </div>
                        <div class="card-content">
                            <p><strong>Ms. Rodriguez:</strong> Great progress in math!</p>
                            <p><strong>Mr. Thompson:</strong> Science project reminder</p>
                            <p><strong>Mrs. Davis:</strong> Reading assignment update</p>
                            <button class="btn" style="margin-top: 1rem;" onclick="showSection('messages')">View All Messages</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages Section -->
            <div id="messages" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Messages</h1>
                    <p class="dashboard-subtitle">Communicate with Emma's teachers</p>
                </div>

                <div class="messages-section">
                    <div class="message-item">
                        <div class="message-avatar">MR</div>
                        <div class="message-content">
                            <div class="message-sender">Ms. Rodriguez - Math Teacher</div>
                            <div class="message-text">Emma showed excellent improvement in algebra this week. Keep up the great work!</div>
                        </div>
                        <div class="message-time">2 hours ago</div>
                    </div>

                    <div class="message-item">
                        <div class="message-avatar">MT</div>
                        <div class="message-content">
                            <div class="message-sender">Mr. Thompson - Science Teacher</div>
                            <div class="message-text">Reminder: Science fair project is due this Thursday. Emma's volcano experiment looks promising!</div>
                        </div>
                        <div class="message-time">1 day ago</div>
                    </div>

                    <div class="message-item">
                        <div class="message-avatar">MD</div>
                        <div class="message-content">
                            <div class="message-sender">Mrs. Davis - English Teacher</div>
                            <div class="message-text">Emma's creative writing has improved significantly. I've attached her latest story for you to read.</div>
                        </div>
                        <div class="message-time">2 days ago</div>
                    </div>
                </div>

                <button class="btn" style="margin-top: 1rem;" onclick="openVideoCall()">📹 Start Video Call</button>
            </div>

            <!-- Other sections would be here -->
            <div id="grades" class="section" style="display: none;">
                <h1 class="dashboard-title">Emma's Grades</h1>
                <p>Detailed grade tracking coming soon...</p>
            </div>

            <div id="homework" class="section" style="display: none;">
                <h1 class="dashboard-title">Homework Tracker</h1>
                <p>Assignment management coming soon...</p>
            </div>

            <div id="calendar" class="section" style="display: none;">
                <h1 class="dashboard-title">School Calendar</h1>
                <p>Event calendar coming soon...</p>
            </div>

            <div id="video" class="section" style="display: none;">
                <h1 class="dashboard-title">Video Calls</h1>
                <p>Video conferencing coming soon...</p>
            </div>

            <div id="settings" class="section" style="display: none;">
                <h1 class="dashboard-title">Settings</h1>
                <p>Account settings coming soon...</p>
            </div>
        </main>
    </div>

    <!-- Video Call Modal -->
    <div id="videoModal" class="modal">
        <div class="modal-content">
            <h2>Video Call with Ms. Rodriguez</h2>
            <div class="video-container">
                <div>📹 Video call would appear here<br>With live translation and captions</div>
            </div>
            <div class="video-controls">
                <button class="btn" onclick="toggleMute()">🎤 Mute</button>
                <button class="btn" onclick="toggleCamera()">📹 Camera</button>
                <button class="btn btn-secondary" onclick="toggleTranslation()">🌍 Translate</button>
                <button class="btn" onclick="closeVideoCall()" style="background: #e53e3e;">End Call</button>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });
            
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).style.display = 'block';
            
            // Add active class to clicked nav item
            event.target.closest('.nav-item').classList.add('active');
        }

        function openVideoCall() {
            document.getElementById('videoModal').style.display = 'block';
        }

        function closeVideoCall() {
            document.getElementById('videoModal').style.display = 'none';
        }

        function changeLanguage() {
            // Language switching functionality would be implemented here
            console.log('Language changed');
        }

        function toggleMute() {
            console.log('Microphone toggled');
            // In real implementation, this would toggle microphone
        }

        function toggleCamera() {
            console.log('Camera toggled');
            // In real implementation, this would toggle camera
        }

        function toggleTranslation() {
            console.log('Translation toggled');
            // In real implementation, this would toggle live translation
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('videoModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>