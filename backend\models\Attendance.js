module.exports = (sequelize, DataTypes) => {
  const Attendance = sequelize.define('Attendance', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    studentId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Students',
        key: 'id'
      }
    },
    classId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Classes',
        key: 'id'
      }
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('present', 'absent', 'late', 'excused', 'partial'),
      allowNull: false
    },
    arrivalTime: {
      type: DataTypes.TIME,
      allowNull: true
    },
    departureTime: {
      type: DataTypes.TIME,
      allowNull: true
    },
    minutesLate: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    reason: {
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    recordedBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    parentNotified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    notificationSentAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    isExcused: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    excusedBy: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    excusedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    excuseReason: {
      type: DataTypes.STRING,
      allowNull: true
    },
    followUpRequired: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    followUpCompleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    metadata: {
      type: DataTypes.JSON,
      defaultValue: {}
    }
  }, {
    indexes: [
      { fields: ['studentId'] },
      { fields: ['classId'] },
      { fields: ['date'] },
      { fields: ['status'] },
      { fields: ['recordedBy'] },
      { fields: ['parentNotified'] },
      { unique: true, fields: ['studentId', 'classId', 'date'] }
    ]
  });

  Attendance.associate = function(models) {
    Attendance.belongsTo(models.Student, {
      foreignKey: 'studentId',
      as: 'student'
    });
    Attendance.belongsTo(models.Class, {
      foreignKey: 'classId',
      as: 'class'
    });
    Attendance.belongsTo(models.User, {
      foreignKey: 'recordedBy',
      as: 'recorder'
    });
    Attendance.belongsTo(models.User, {
      foreignKey: 'excusedBy',
      as: 'excuser'
    });
  };

  return Attendance;
};