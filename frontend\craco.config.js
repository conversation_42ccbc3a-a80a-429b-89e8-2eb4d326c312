const path = require('path');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = {
  webpack: {
    configure: (webpackConfig, { env, paths }) => {
      // Production optimizations
      if (env === 'production') {
        // Enable bundle splitting
        webpackConfig.optimization = {
          ...webpackConfig.optimization,
          splitChunks: {
            chunks: 'all',
            cacheGroups: {
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name: 'vendors',
                chunks: 'all',
                priority: 10,
              },
              common: {
                name: 'common',
                minChunks: 2,
                chunks: 'all',
                priority: 5,
                reuseExistingChunk: true,
              },
              react: {
                test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
                name: 'react',
                chunks: 'all',
                priority: 20,
              },
            },
          },
          runtimeChunk: {
            name: 'runtime',
          },
        };

        // Add bundle analyzer in production if enabled
        if (process.env.ANALYZE_BUNDLE === 'true') {
          webpackConfig.plugins.push(
            new BundleAnalyzerPlugin({
              analyzerMode: 'static',
              openAnalyzer: false,
              reportFilename: 'bundle-report.html',
            })
          );
        }
      }

      // Add performance hints
      webpackConfig.performance = {
        maxAssetSize: 512000, // 500kb
        maxEntrypointSize: 512000, // 500kb
        hints: env === 'production' ? 'warning' : false,
      };

      // Optimize images
      webpackConfig.module.rules.push({
        test: /\.(png|jpe?g|gif|svg)$/i,
        use: [
          {
            loader: 'file-loader',
            options: {
              name: 'static/media/[name].[hash:8].[ext]',
            },
          },
          {
            loader: 'image-webpack-loader',
            options: {
              mozjpeg: {
                progressive: true,
                quality: 65,
              },
              optipng: {
                enabled: false,
              },
              pngquant: {
                quality: [0.65, 0.90],
                speed: 4,
              },
              gifsicle: {
                interlaced: false,
              },
              webp: {
                quality: 75,
              },
            },
          },
        ],
      });

      return webpackConfig;
    },
  },
  devServer: {
    // Development server optimizations
    compress: true,
    hot: true,
    open: false,
    overlay: {
      warnings: false,
      errors: true,
    },
  },
  babel: {
    plugins: [
      // Add lazy loading support
      '@babel/plugin-syntax-dynamic-import',
      // Production optimizations
      ...(process.env.NODE_ENV === 'production'
        ? [
            [
              'babel-plugin-transform-remove-console',
              {
                exclude: ['error', 'warn'],
              },
            ],
          ]
        : []),
    ],
  },
  eslint: {
    enable: true,
    mode: 'extends',
  },
};
