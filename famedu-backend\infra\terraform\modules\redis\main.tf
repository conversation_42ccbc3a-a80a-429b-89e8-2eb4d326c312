# ElastiCache Subnet Group
resource "aws_elasticache_subnet_group" "main" {
  name       = "famedu-${var.environment}-redis-subnet-group"
  subnet_ids = var.subnet_ids

  tags = {
    Name = "famedu-${var.environment}-redis-subnet-group"
  }
}

# ElastiCache Parameter Group
resource "aws_elasticache_parameter_group" "main" {
  family = "redis7"
  name   = "famedu-${var.environment}-redis-parameter-group"

  parameter {
    name  = "maxmemory-policy"
    value = "allkeys-lru"
  }

  parameter {
    name  = "notify-keyspace-events"
    value = "Ex"
  }

  tags = {
    Name = "famedu-${var.environment}-redis-parameter-group"
  }
}

# ElastiCache Replication Group
resource "aws_elasticache_replication_group" "main" {
  replication_group_id = "famedu-${var.environment}-redis"
  description         = "FamEduConnect Redis cluster for ${var.environment}"

  node_type                  = var.redis_node_type
  port                       = 6379
  parameter_group_name       = aws_elasticache_parameter_group.main.name
  subnet_group_name          = aws_elasticache_subnet_group.main.name
  security_group_ids         = var.security_groups

  num_cache_clusters = var.environment == "production" ? 2 : 1
  automatic_failover_enabled = var.environment == "production"

  at_rest_encryption_enabled = true
  transit_encryption_enabled = true

  tags = {
    Name = "famedu-${var.environment}-redis"
  }
} 