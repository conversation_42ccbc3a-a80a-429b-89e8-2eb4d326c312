# Mobile App Documentation

## React Native Setup

### Prerequisites
- Node.js 18+
- Expo CLI
- Android Studio (for Android)
- Xcode (for iOS, macOS only)

### Installation

```bash
cd mobile
npm install
```

### Development

```bash
# Start Expo development server
npm start

# Run on Android
npm run android

# Run on iOS
npm run ios
```

### Building for Production

#### Android
```bash
# Build APK
expo build:android

# Build AAB (recommended for Play Store)
expo build:android -t app-bundle
```

#### iOS
```bash
# Build for App Store
expo build:ios
```

### Features

#### Authentication
- Login/Register screens
- JWT token management
- Biometric authentication (planned)

#### Navigation
- Tab-based navigation
- Stack navigation for detailed views
- Deep linking support

#### Real-time Features
- Socket.IO integration
- Push notifications
- Background sync

#### Offline Support
- Local data caching
- Offline message queue
- Sync when online

### Push Notifications

#### Setup
1. Configure Firebase (Android) / APNs (iOS)
2. Add configuration to `app.json`
3. Handle notification permissions

#### Implementation
```javascript
import * as Notifications from 'expo-notifications';

// Request permissions
const { status } = await Notifications.requestPermissionsAsync();

// Handle received notifications
Notifications.addNotificationReceivedListener(notification => {
  // Handle notification
});
```

### State Management

The mobile app uses Redux Toolkit for state management:

- `authSlice`: User authentication
- `messagesSlice`: Chat functionality
- `classesSlice`: Class management

### API Integration

All API calls go through the centralized `api.js` service:

```javascript
import api from '../services/api';

// Authenticated requests automatically include JWT token
const response = await api.get('/classes');
```

### Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

### Deployment

#### App Store (iOS)
1. Build with `expo build:ios`
2. Download IPA file
3. Upload to App Store Connect
4. Submit for review

#### Google Play Store (Android)
1. Build with `expo build:android -t app-bundle`
2. Download AAB file
3. Upload to Google Play Console
4. Submit for review

### Performance Optimization

- Image optimization with `expo-image`
- Lazy loading of screens
- Efficient list rendering with `FlatList`
- Memory management for large datasets