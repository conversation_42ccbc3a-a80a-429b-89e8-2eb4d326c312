const axios = require('axios');
const { exec } = require('child_process');

// Connection resilience test configuration
const CONFIG = {
  BACKEND_URL: 'http://localhost:3002',
  FRONTEND_URL: 'http://localhost:3000',
  TEST_DURATION: 120000, // 2 minutes
  CHECK_INTERVAL: 2000, // Check every 2 seconds
  TIMEOUT: 5000
};

const results = {
  backendChecks: 0,
  backendFailures: 0,
  frontendChecks: 0,
  frontendFailures: 0,
  recoveryTests: 0,
  recoverySuccess: 0,
  startTime: Date.now()
};

// Test backend connection
async function testBackendConnection() {
  try {
    const response = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, {
      timeout: CONFIG.TIMEOUT
    });
    
    results.backendChecks++;
    if (response.status === 200) {
      return true;
    } else {
      results.backendFailures++;
      return false;
    }
  } catch (error) {
    results.backendChecks++;
    results.backendFailures++;
    return false;
  }
}

// Test frontend connection
async function testFrontendConnection() {
  try {
    const response = await axios.get(`${CONFIG.FRONTEND_URL}`, {
      timeout: CONFIG.TIMEOUT
    });
    
    results.frontendChecks++;
    if (response.status === 200) {
      return true;
    } else {
      results.frontendFailures++;
      return false;
    }
  } catch (error) {
    results.frontendChecks++;
    results.frontendFailures++;
    return false;
  }
}

// Simulate service restart
async function simulateServiceRestart() {
  console.log('🔄 Simulating service restart...');
  
  try {
    // Kill backend process
    exec('taskkill /f /im node.exe', async (error) => {
      if (!error) {
        console.log('⏸️  Backend stopped');
        
        // Wait 5 seconds
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Start backend again
        exec('cd backend && start /B node server.js', (error) => {
          if (!error) {
            console.log('▶️  Backend restarted');
            
            // Test recovery
            setTimeout(async () => {
              const recovered = await testBackendConnection();
              results.recoveryTests++;
              
              if (recovered) {
                console.log('✅ Backend recovered successfully');
                results.recoverySuccess++;
              } else {
                console.log('❌ Backend failed to recover');
              }
            }, 10000);
          }
        });
      }
    });
  } catch (error) {
    console.log('❌ Failed to simulate restart:', error.message);
  }
}

// Continuous connection monitoring
async function monitorConnections() {
  console.log('🔍 Starting continuous connection monitoring...');
  console.log(`⏱️  Duration: ${CONFIG.TEST_DURATION / 1000}s`);
  console.log(`📡 Check interval: ${CONFIG.CHECK_INTERVAL / 1000}s`);
  console.log('');
  
  const interval = setInterval(async () => {
    const backendStatus = await testBackendConnection();
    const frontendStatus = await testFrontendConnection();
    
    const timestamp = new Date().toLocaleTimeString();
    
    if (backendStatus && frontendStatus) {
      console.log(`[${timestamp}] ✅ Both services healthy`);
    } else if (backendStatus) {
      console.log(`[${timestamp}] ⚠️  Backend OK, Frontend DOWN`);
    } else if (frontendStatus) {
      console.log(`[${timestamp}] ⚠️  Frontend OK, Backend DOWN`);
    } else {
      console.log(`[${timestamp}] ❌ Both services DOWN`);
    }
  }, CONFIG.CHECK_INTERVAL);
  
  // Stop monitoring after test duration
  setTimeout(() => {
    clearInterval(interval);
    generateResilienceReport();
  }, CONFIG.TEST_DURATION);
}

// Generate resilience report
function generateResilienceReport() {
  const totalTime = Date.now() - results.startTime;
  const backendUptime = ((results.backendChecks - results.backendFailures) / results.backendChecks) * 100;
  const frontendUptime = ((results.frontendChecks - results.frontendFailures) / results.frontendChecks) * 100;
  const recoveryRate = (results.recoverySuccess / results.recoveryTests) * 100;
  
  console.log('\n' + '='.repeat(60));
  console.log('🛡️  CONNECTION RESILIENCE TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`⏱️  Test Duration: ${Math.round(totalTime / 1000)}s`);
  console.log(`📡 Check Interval: ${CONFIG.CHECK_INTERVAL / 1000}s`);
  console.log('');
  
  console.log('🔧 Backend Resilience:');
  console.log(`   📊 Total Checks: ${results.backendChecks}`);
  console.log(`   ✅ Successful: ${results.backendChecks - results.backendFailures}`);
  console.log(`   ❌ Failures: ${results.backendFailures}`);
  console.log(`   📈 Uptime: ${Math.round(backendUptime)}%`);
  console.log('');
  
  console.log('📱 Frontend Resilience:');
  console.log(`   📊 Total Checks: ${results.frontendChecks}`);
  console.log(`   ✅ Successful: ${results.frontendChecks - results.frontendFailures}`);
  console.log(`   ❌ Failures: ${results.frontendFailures}`);
  console.log(`   📈 Uptime: ${Math.round(frontendUptime)}%`);
  console.log('');
  
  console.log('🔄 Recovery Tests:');
  console.log(`   📊 Total Tests: ${results.recoveryTests}`);
  console.log(`   ✅ Successful Recoveries: ${results.recoverySuccess}`);
  console.log(`   📈 Recovery Rate: ${Math.round(recoveryRate)}%`);
  console.log('');
  
  // Generate recommendations
  console.log('💡 Recommendations:');
  
  if (backendUptime >= 99) {
    console.log('   ✅ Backend is highly resilient');
  } else if (backendUptime >= 95) {
    console.log('   ⚠️  Backend resilience is good but could be improved');
  } else {
    console.log('   ❌ Backend needs better error handling and recovery');
  }
  
  if (frontendUptime >= 99) {
    console.log('   ✅ Frontend is highly resilient');
  } else if (frontendUptime >= 95) {
    console.log('   ⚠️  Frontend resilience is good but could be improved');
  } else {
    console.log('   ❌ Frontend needs better error handling and recovery');
  }
  
  if (recoveryRate >= 90) {
    console.log('   ✅ System recovery is excellent');
  } else if (recoveryRate >= 70) {
    console.log('   ⚠️  System recovery is good but could be improved');
  } else {
    console.log('   ❌ System recovery needs significant improvement');
  }
  
  console.log('\n' + '='.repeat(60));
  
  process.exit(0);
}

// Main resilience test
async function runResilienceTest() {
  console.log('🛡️  Starting Connection Resilience Test...');
  console.log('');
  
  // Start monitoring
  monitorConnections();
  
  // Simulate restart after 30 seconds
  setTimeout(() => {
    simulateServiceRestart();
  }, 30000);
}

runResilienceTest().catch(console.error); 