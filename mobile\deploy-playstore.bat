@echo off
echo ========================================
echo FamEduConnect - Play Store Deployment
echo ========================================
echo.

echo Checking prerequisites...
echo.

REM Check if EAS CLI is installed
eas --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: EAS CLI not found!
    echo Please install it first: npm install -g @expo/eas-cli
    pause
    exit /b 1
)

echo [✓] EAS CLI found
echo.

echo Current configuration:
echo - App Name: FamEduConnect
echo - Package: com.fameduconnect.app
echo - Version: 1.0.0
echo.

echo What would you like to do?
echo.
echo 1. Build for Play Store (AAB)
echo 2. Submit to Play Store
echo 3. Build and Submit
echo 4. Check build status
echo 5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto build
if "%choice%"=="2" goto submit
if "%choice%"=="3" goto build_and_submit
if "%choice%"=="4" goto status
if "%choice%"=="5" goto exit
goto invalid

:build
echo.
echo [1/1] Building Android App Bundle for Play Store...
echo This may take 10-15 minutes...
echo.
eas build --platform android --profile production
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)
echo.
echo ✅ Build completed successfully!
echo Check your Expo dashboard for the download link.
goto end

:submit
echo.
echo [1/1] Submitting to Play Store...
echo Make sure you have google-service-account.json configured!
echo.
eas submit --platform android --profile production
if %errorlevel% neq 0 (
    echo ERROR: Submission failed!
    echo Make sure your google-service-account.json is configured.
    pause
    exit /b 1
)
echo.
echo ✅ Submission completed!
echo Check Google Play Console for review status.
goto end

:build_and_submit
echo.
echo [1/2] Building Android App Bundle...
echo This may take 10-15 minutes...
echo.
eas build --platform android --profile production
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)
echo.
echo [2/2] Submitting to Play Store...
eas submit --platform android --profile production
if %errorlevel% neq 0 (
    echo ERROR: Submission failed!
    pause
    exit /b 1
)
echo.
echo ✅ Build and submission completed!
goto end

:status
echo.
echo Checking build status...
eas build:list
goto end

:invalid
echo Invalid choice. Please try again.
pause
goto start

:end
echo.
echo ========================================
echo Next Steps:
echo 1. Check your Expo dashboard for build status
echo 2. Complete your Play Store listing
echo 3. Add screenshots and descriptions
echo 4. Submit for review
echo ========================================
echo.

:exit
pause