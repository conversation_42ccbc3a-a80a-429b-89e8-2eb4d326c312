import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, LanguageIcon } from '@heroicons/react/24/outline';
import { useDispatch } from 'react-redux';
import { translateMessage } from '../../store/slices/messagesSlice';

const TranslationModal = ({ isOpen, onClose, messages, targetLanguage }) => {
  const [selectedLanguage, setSelectedLanguage] = useState(targetLanguage);
  const [translating, setTranslating] = useState(false);
  const dispatch = useDispatch();

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
    { code: 'de', name: 'German' },
    { code: 'zh', name: 'Chinese' },
    { code: 'ar', name: 'Arabic' },
    { code: 'hi', name: 'Hindi' },
    { code: 'pt', name: 'Portuguese' },
    { code: 'ru', name: 'Russian' },
    { code: 'ja', name: 'Japanese' }
  ];

  const handleTranslate = async (messageId) => {
    setTranslating(true);
    try {
      await dispatch(translateMessage({ messageId, targetLanguage: selectedLanguage }));
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      setTranslating(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2">
              <LanguageIcon className="h-5 w-5 text-indigo-600" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Message Translation
              </h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Language Selector */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Translate to:
            </label>
            <select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
            >
              {languages.map((lang) => (
                <option key={lang.code} value={lang.code}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>

          {/* Messages */}
          <div className="p-4 max-h-96 overflow-y-auto">
            {messages.length === 0 ? (
              <div className="text-center py-8">
                <LanguageIcon className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  No messages to translate
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {message.sender?.firstName} {message.sender?.lastName}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          Original ({message.originalLanguage || 'en'})
                        </span>
                      </div>
                      <button
                        onClick={() => handleTranslate(message.id)}
                        disabled={translating}
                        className="text-xs text-indigo-600 hover:text-indigo-500 disabled:opacity-50"
                      >
                        {translating ? 'Translating...' : 'Translate'}
                      </button>
                    </div>
                    
                    <p className="text-sm text-gray-800 dark:text-gray-200 mb-3">
                      {message.content}
                    </p>
                    
                    {message.translations && message.translations[selectedLanguage] && (
                      <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <LanguageIcon className="h-4 w-4 text-green-600" />
                          <span className="text-xs font-medium text-green-600">
                            Translation ({selectedLanguage})
                          </span>
                        </div>
                        <p className="text-sm text-gray-800 dark:text-gray-200">
                          {message.translations[selectedLanguage].translatedText}
                        </p>
                        {message.translations[selectedLanguage].confidence && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Confidence: {Math.round(message.translations[selectedLanguage].confidence * 100)}%
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
            <div className="flex items-center justify-between">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Translations are powered by Google Translate
              </p>
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md transition-colors"
              >
                Done
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default TranslationModal;