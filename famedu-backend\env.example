# Database
DATABASE_URL="postgresql://username:password@localhost:5432/fameduconnect?schema=public"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRATION="24h"

# Redis
REDIS_URL="redis://localhost:6379"

# RabbitMQ
RABBITMQ_URL="amqp://localhost:5672"

# Server
PORT=3000
NODE_ENV=development

# CORS
CORS_ORIGIN="http://localhost:3000,http://localhost:3001"

# Email (optional for development)
SMTP_HOST="smtp.mailtrap.io"
SMTP_PORT=2525
SMTP_USER="your-mailtrap-user"
SMTP_PASS="your-mailtrap-pass"
SMTP_FROM="<EMAIL>"

# File Upload
MAX_FILE_SIZE=10485760 # 10MB

# Security
BCRYPT_ROUNDS=12

# Sentry Error Tracking
SENTRY_DSN="https://<EMAIL>/project-id"

# Monitoring
ENABLE_METRICS=true 