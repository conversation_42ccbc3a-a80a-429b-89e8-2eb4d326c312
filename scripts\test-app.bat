@echo off
REM FamEduConnect Application Testing Script for Windows
REM This script sets up and runs comprehensive tests for the entire application

echo 🚀 Starting FamEduConnect Application Testing
echo ==============================================

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    exit /b 1
)
echo ✅ Node.js is installed

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    exit /b 1
)
echo ✅ npm is installed

REM Setup backend
echo 📦 Setting up backend dependencies...
cd FamEduConnect_Full_Codebase\backend
if not exist node_modules (
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install backend dependencies
        exit /b 1
    )
)
echo ✅ Backend dependencies installed

REM Setup frontend
echo 📦 Setting up frontend dependencies...
cd ..\frontend
if not exist node_modules (
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install frontend dependencies
        exit /b 1
    )
)
echo ✅ Frontend dependencies installed

REM Run backend tests
echo 🧪 Running backend tests...
cd ..\backend
npm test
if %errorlevel% neq 0 (
    echo ❌ Backend tests failed
    exit /b 1
)
echo ✅ Backend tests passed

REM Run frontend tests
echo 🧪 Running frontend tests...
cd ..\frontend
npm test -- --watchAll=false
if %errorlevel% neq 0 (
    echo ❌ Frontend tests failed
    exit /b 1
)
echo ✅ Frontend tests passed

REM Start backend server for integration tests
echo 🚀 Starting backend server for integration tests...
cd ..\backend
start /B npm start
timeout /t 5 /nobreak >nul

REM Run integration tests
echo 🧪 Running integration tests...
npm run test:integration
if %errorlevel% neq 0 (
    echo ❌ Integration tests failed
    taskkill /F /IM node.exe >nul 2>&1
    exit /b 1
)
echo ✅ Integration tests passed

REM Run end-to-end tests
echo 🧪 Running end-to-end tests...
npm run test:e2e
if %errorlevel% neq 0 (
    echo ❌ End-to-end tests failed
    taskkill /F /IM node.exe >nul 2>&1
    exit /b 1
)
echo ✅ End-to-end tests passed

REM Stop backend server
echo 🛑 Stopping backend server...
taskkill /F /IM node.exe >nul 2>&1

REM Generate test report
echo 📊 Generating test report...
echo ==============================================
echo 🎉 Testing completed successfully!
echo.
echo 📋 Test Summary:
echo - Backend tests: ✅ PASSED
echo - Frontend tests: ✅ PASSED
echo - Integration tests: ✅ PASSED
echo - End-to-end tests: ✅ PASSED
echo.
echo 🚀 Your application is ready for use!
echo ==============================================

pause 