# Quick Test Start Guide for FamEduConnect

## 🚀 Quick Testing Options

### Option 1: PowerShell (Recommended for Windows)
```powershell
# Navigate to the project directory
cd FamEduConnect_Full_Codebase

# Run the PowerShell testing script
.\scripts\test-app.ps1
```

### Option 2: Batch File
```cmd
# Navigate to the project directory
cd FamEduConnect_Full_Codebase

# Run the batch testing script
scripts\test-app.bat
```

### Option 3: Manual Testing
```bash
# Backend tests
cd backend
npm install
npm test

# Frontend tests
cd ../frontend
npm install
npm test -- --watchAll=false
```

## 🧪 What the Tests Cover

### Backend Tests
- ✅ Authentication (register, login, profile)
- ✅ User management
- ✅ Message handling
- ✅ Class management
- ✅ Error handling
- ✅ Rate limiting

### Frontend Tests
- ✅ Component rendering
- ✅ User interactions
- ✅ State management
- ✅ Navigation

### Integration Tests
- ✅ API endpoints
- ✅ Database operations
- ✅ Cross-component communication

### End-to-End Tests
- ✅ Complete user journeys
- ✅ Role-based access
- ✅ Cross-user communication
- ✅ Data persistence

## 🎯 Manual Testing Scenarios

### 1. User Registration & Onboarding
1. Go to `/register`
2. Create a new account
3. Complete onboarding or click "Skip for now"
4. Verify you land on the correct role-based dashboard

### 2. Role-Based Navigation
- **Parent**: Should see parent dashboard with children info
- **Teacher**: Should see teacher dashboard with class info
- **Student**: Should see student dashboard with assignments
- **Admin**: Should see admin dashboard with system stats

### 3. AI Assistant
1. Start onboarding or access from dashboard
2. Ask questions via text or voice
3. Test voice commands: "next", "back", "complete", "skip"
4. Verify navigation works correctly

### 4. Settings & Onboarding Return
1. Click "Settings" button on dashboard
2. Verify you can return to complete onboarding
3. Test "Complete Onboarding" button functionality

## 🔧 Troubleshooting

### Common Issues
- **Node.js not found**: Install Node.js from https://nodejs.org/
- **npm not found**: Install npm with Node.js
- **Permission errors**: Run PowerShell as Administrator
- **Port conflicts**: Check if port 3000/5000 is available

### Test Failures
- Check console output for specific error messages
- Verify database connection
- Ensure all dependencies are installed
- Check environment variables

## 📊 Test Results

After running tests, you should see:
```
🎉 Testing completed successfully!

📋 Test Summary:
- Backend tests: ✅ PASSED
- Frontend tests: ✅ PASSED
- Integration tests: ✅ PASSED
- End-to-end tests: ✅ PASSED

🚀 Your application is ready for use!
```

## 🎮 Start the Application

After successful testing:

```bash
# Start backend
cd backend
npm start

# Start frontend (in new terminal)
cd frontend
npm start
```

Visit `http://localhost:3000` to use the application! 