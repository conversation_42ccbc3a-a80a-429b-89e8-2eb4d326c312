module.exports = (sequelize, DataTypes) => {
  const Notification = sequelize.define('Notification', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 200]
      }
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM(
        'attendance', 'performance', 'message', 'video_call', 'announcement', 
        'emergency', 'reminder', 'system', 'behavior', 'assignment'
      ),
      allowNull: false
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      defaultValue: 'normal'
    },
    isRead: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    readAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    deliveryMethod: {
      type: DataTypes.JSON,
      defaultValue: {
        push: true,
        email: false,
        sms: false
      }
    },
    isDelivered: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    deliveredAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    deliveryAttempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    scheduledFor: {
      type: DataTypes.DATE,
      allowNull: true
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    actionRequired: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    actionCompleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    actionUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    relatedEntityType: {
      type: DataTypes.ENUM('student', 'class', 'message', 'video_call', 'attendance', 'performance'),
      allowNull: true
    },
    relatedEntityId: {
      type: DataTypes.UUID,
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    language: {
      type: DataTypes.STRING,
      defaultValue: 'en'
    },
    isTranslated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    originalLanguage: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    indexes: [
      { fields: ['userId'] },
      { fields: ['type'] },
      { fields: ['priority'] },
      { fields: ['isRead'] },
      { fields: ['isDelivered'] },
      { fields: ['scheduledFor'] },
      { fields: ['expiresAt'] },
      { fields: ['actionRequired'] },
      { fields: ['relatedEntityType', 'relatedEntityId'] },
      { fields: ['createdAt'] }
    ]
  });

  Notification.associate = function(models) {
    Notification.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return Notification;
};