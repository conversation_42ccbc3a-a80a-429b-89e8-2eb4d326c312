import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { messageAPI } from '../../services/api';
import { toast } from 'react-hot-toast';

// Async thunks
export const fetchMessages = createAsyncThunk(
  'messages/fetchMessages',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await messageAPI.getMessages(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch messages');
    }
  }
);

export const sendMessage = createAsyncThunk(
  'messages/sendMessage',
  async (messageData, { rejectWithValue }) => {
    try {
      let response;
      
      if (messageData.attachments && messageData.attachments.length > 0) {
        const formData = new FormData();
        Object.keys(messageData).forEach(key => {
          if (key === 'attachments') {
            messageData.attachments.forEach(file => {
              formData.append('attachments', file);
            });
          } else {
            formData.append(key, messageData[key]);
          }
        });
        response = await messageAPI.sendMessageWithFiles(formData);
      } else {
        response = await messageAPI.sendMessage(messageData);
      }
      
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to send message';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const markAsRead = createAsyncThunk(
  'messages/markAsRead',
  async (messageId, { rejectWithValue }) => {
    try {
      await messageAPI.markAsRead(messageId);
      return messageId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark as read');
    }
  }
);

export const translateMessage = createAsyncThunk(
  'messages/translateMessage',
  async ({ messageId, targetLanguage }, { rejectWithValue }) => {
    try {
      const response = await messageAPI.getTranslations(messageId, targetLanguage);
      return { messageId, translation: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Translation failed');
    }
  }
);

export const deleteMessage = createAsyncThunk(
  'messages/deleteMessage',
  async (messageId, { rejectWithValue }) => {
    try {
      await messageAPI.deleteMessage(messageId);
      return messageId;
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to delete message';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

const initialState = {
  conversations: [],
  messages: [],
  selectedConversation: null,
  translations: {},
  loading: false,
  sending: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0
  }
};

const messagesSlice = createSlice({
  name: 'messages',
  initialState,
  reducers: {
    selectConversation: (state, action) => {
      state.selectedConversation = action.payload;
      state.messages = []; // Clear messages when switching conversations
    },
    addMessage: (state, action) => {
      const message = action.payload;
      const existingIndex = state.messages.findIndex(m => m.id === message.id);
      
      if (existingIndex === -1) {
        state.messages.push(message);
        
        // Update conversation's last message
        const conversation = state.conversations.find(c => 
          (c.type === 'direct' && c.participantId === message.senderId) ||
          (c.type === 'class' && c.classId === message.classId)
        );
        
        if (conversation) {
          conversation.lastMessage = message;
          conversation.updatedAt = message.createdAt;
        }
      }
    },
    updateMessage: (state, action) => {
      const { messageId, updates } = action.payload;
      const messageIndex = state.messages.findIndex(m => m.id === messageId);
      
      if (messageIndex !== -1) {
        state.messages[messageIndex] = { ...state.messages[messageIndex], ...updates };
      }
    },
    removeMessage: (state, action) => {
      const messageId = action.payload;
      state.messages = state.messages.filter(m => m.id !== messageId);
    },
    updateMessageStatus: (state, action) => {
      const { messageId, status } = action.payload;
      const messageIndex = state.messages.findIndex(m => m.id === messageId);
      
      if (messageIndex !== -1) {
        state.messages[messageIndex].status = status;
      }
    },
    setTypingStatus: (state, action) => {
      const { conversationId, userId, isTyping } = action.payload;
      // Handle typing indicators
    },
    clearMessages: (state) => {
      state.messages = [];
    },
    setError: (state, action) => {
      state.error = typeof action.payload === 'string' ? action.payload : 'Message error occurred';
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Messages
      .addCase(fetchMessages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.loading = false;
        state.messages = action.payload.messages || [];
        state.conversations = action.payload.conversations || [];
        state.pagination = {
          currentPage: action.payload.currentPage || 1,
          totalPages: action.payload.totalPages || 1,
          totalCount: action.payload.totalCount || 0
        };
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.loading = false;
        state.error = typeof action.payload === 'string' ? action.payload : 'Failed to fetch messages';
      })
      
      // Send Message
      .addCase(sendMessage.pending, (state) => {
        state.sending = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.sending = false;
        
        // Add the sent message to the list
        const message = action.payload;
        const existingIndex = state.messages.findIndex(m => m.id === message.id);
        
        if (existingIndex === -1) {
          state.messages.push(message);
        }
        
        // Update conversation
        const conversation = state.conversations.find(c => 
          (c.type === 'direct' && c.participantId === message.recipientId) ||
          (c.type === 'class' && c.classId === message.classId)
        );
        
        if (conversation) {
          conversation.lastMessage = message;
          conversation.updatedAt = message.createdAt;
        }
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.sending = false;
        state.error = typeof action.payload === 'string' ? action.payload : 'Failed to send message';
      })
      
      // Mark as Read
      .addCase(markAsRead.fulfilled, (state, action) => {
        const messageId = action.payload;
        const messageIndex = state.messages.findIndex(m => m.id === messageId);
        
        if (messageIndex !== -1) {
          state.messages[messageIndex].isRead = true;
          state.messages[messageIndex].readAt = new Date().toISOString();
        }
      })
      
      // Translate Message
      .addCase(translateMessage.fulfilled, (state, action) => {
        const { messageId, translation } = action.payload;
        state.translations[messageId] = translation;
      })
      
      // Delete Message
      .addCase(deleteMessage.fulfilled, (state, action) => {
        const messageId = action.payload;
        const messageIndex = state.messages.findIndex(m => m.id === messageId);
        
        if (messageIndex !== -1) {
          state.messages[messageIndex].isDeleted = true;
          state.messages[messageIndex].content = 'This message has been deleted';
        }
      });
  }
});

export const {
  selectConversation,
  addMessage,
  updateMessage,
  removeMessage,
  updateMessageStatus,
  setTypingStatus,
  clearMessages,
  setError,
  clearError
} = messagesSlice.actions;

export default messagesSlice.reducer;