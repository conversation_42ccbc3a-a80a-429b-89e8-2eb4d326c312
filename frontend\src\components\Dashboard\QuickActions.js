import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  DocumentTextIcon,
  CalendarIcon,
  UserGroupIcon,
  AcademicCapIcon,
  BellIcon
} from '@heroicons/react/24/outline';

const QuickActions = ({ userRole }) => {
  const getActionsForRole = () => {
    const baseActions = [
      {
        name: 'New Message',
        icon: ChatBubbleLeftRightIcon,
        href: '/messages',
        color: 'bg-blue-500 hover:bg-blue-600'
      },
      {
        name: 'Start Video Call',
        icon: VideoCameraIcon,
        href: '/video-call',
        color: 'bg-green-500 hover:bg-green-600'
      }
    ];

    if (userRole === 'parent') {
      return [
        ...baseActions,
        {
          name: 'View Children',
          icon: AcademicCapIcon,
          href: '/students',
          color: 'bg-purple-500 hover:bg-purple-600'
        },
        {
          name: 'Schedule Meeting',
          icon: CalendarIcon,
          href: '/calendar',
          color: 'bg-orange-500 hover:bg-orange-600'
        }
      ];
    }

    if (userRole === 'teacher') {
      return [
        ...baseActions,
        {
          name: 'My Classes',
          icon: UserGroupIcon,
          href: '/classes',
          color: 'bg-indigo-500 hover:bg-indigo-600'
        },
        {
          name: 'Add Student',
          icon: PlusIcon,
          href: '/students/new',
          color: 'bg-green-500 hover:bg-green-600'
        },
        {
          name: 'Create Report',
          icon: DocumentTextIcon,
          href: '/reports/new',
          color: 'bg-yellow-500 hover:bg-yellow-600'
        }
      ];
    }

    if (userRole === 'admin') {
      return [
        ...baseActions,
        {
          name: 'User Management',
          icon: UserGroupIcon,
          href: '/admin/users',
          color: 'bg-red-500 hover:bg-red-600'
        },
        {
          name: 'System Settings',
          icon: DocumentTextIcon,
          href: '/admin/settings',
          color: 'bg-gray-500 hover:bg-gray-600'
        },
        {
          name: 'Send Announcement',
          icon: BellIcon,
          href: '/admin/announcements',
          color: 'bg-purple-500 hover:bg-purple-600'
        }
      ];
    }

    return baseActions;
  };

  const actions = getActionsForRole();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
        Quick Actions
      </h3>
      
      <div className="grid grid-cols-1 gap-3">
        {actions.map((action, index) => (
          <motion.div
            key={action.name}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Link
              to={action.href}
              className={`
                flex items-center space-x-3 p-3 rounded-lg text-white transition-colors duration-200
                ${action.color}
              `}
            >
              <action.icon className="h-5 w-5" />
              <span className="text-sm font-medium">{action.name}</span>
            </Link>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default QuickActions;