const express = require('express');
const { User, Student, Class, Message, VideoCall, Attendance, Performance, Notification } = require('../models');
const { body, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');
const { authorize } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

// All admin routes require admin role
router.use(authMiddleware);
router.use(authorize(['admin']));

// Dashboard analytics
router.get('/dashboard', async (req, res) => {
  try {
    const { timeframe = '30' } = req.query; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeframe));

    // Get overall statistics
    const stats = await Promise.all([
      User.count({ where: { isActive: true } }),
      Student.count({ where: { isActive: true } }),
      Class.count({ where: { isActive: true } }),
      Message.count({
        where: {
          createdAt: { [Op.gte]: startDate }
        }
      }),
      VideoCall.count({
        where: {
          createdAt: { [Op.gte]: startDate }
        }
      })
    ]);

    // User distribution by role
    const usersByRole = await User.findAll({
      where: { isActive: true },
      attributes: [
        'role',
        [require('sequelize').fn('COUNT', require('sequelize').col('role')), 'count']
      ],
      group: ['role'],
      raw: true
    });

    // Recent activity
    const recentMessages = await Message.count({
      where: {
        createdAt: { [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }
    });

    const recentLogins = await User.count({
      where: {
        lastLogin: { [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }
    });

    // System health metrics
    const systemHealth = {
      activeUsers: stats[0],
      totalStudents: stats[1],
      totalClasses: stats[2],
      messagesLast30Days: stats[3],
      videoCallsLast30Days: stats[4],
      usersByRole: usersByRole.reduce((acc, item) => {
        acc[item.role] = parseInt(item.count);
        return acc;
      }, {}),
      recentActivity: {
        messagesLast24h: recentMessages,
        loginsLast24h: recentLogins
      }
    };

    res.json(systemHealth);
  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// User management
router.get('/users', async (req, res) => {
  try {
    const { page = 1, limit = 20, role, search, status } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = {};

    if (role) {
      whereClause.role = role;
    }

    if (status) {
      whereClause.isActive = status === 'active';
    }

    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const users = await User.findAndCountAll({
      where: whereClause,
      attributes: { exclude: ['password', 'twoFactorSecret', 'encryptionKey'] },
      include: [
        {
          model: Student,
          as: 'children',
          attributes: ['id', 'firstName', 'lastName'],
          required: false
        },
        {
          model: Class,
          as: 'teachingClasses',
          attributes: ['id', 'className', 'classCode'],
          required: false
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      users: users.rows,
      totalCount: users.count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(users.count / limit)
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update user status
router.patch('/users/:userId/status', [
  body('isActive').isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { userId } = req.params;
    const { isActive } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    await user.update({ isActive });

    res.json({ message: `User ${isActive ? 'activated' : 'deactivated'} successfully` });
  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// System logs
router.get('/logs', async (req, res) => {
  try {
    const { page = 1, limit = 50, level, startDate, endDate } = req.query;
    const offset = (page - 1) * limit;

    // This would typically come from a logging system
    // For now, we'll return recent activities from various tables
    
    const activities = [];

    // Recent user registrations
    const recentUsers = await User.findAll({
      where: {
        createdAt: startDate && endDate ? {
          [Op.between]: [startDate, endDate]
        } : {
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      },
      attributes: ['id', 'firstName', 'lastName', 'email', 'role', 'createdAt'],
      order: [['createdAt', 'DESC']],
      limit: 20
    });

    recentUsers.forEach(user => {
      activities.push({
        id: `user_${user.id}`,
        type: 'user_registration',
        level: 'info',
        message: `New ${user.role} registered: ${user.firstName} ${user.lastName}`,
        timestamp: user.createdAt,
        metadata: { userId: user.id, email: user.email }
      });
    });

    // Recent video calls
    const recentCalls = await VideoCall.findAll({
      where: {
        createdAt: {
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        }
      },
      include: [
        {
          model: User,
          as: 'host',
          attributes: ['firstName', 'lastName']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: 20
    });

    recentCalls.forEach(call => {
      activities.push({
        id: `call_${call.id}`,
        type: 'video_call',
        level: 'info',
        message: `Video call "${call.title}" created by ${call.host.firstName} ${call.host.lastName}`,
        timestamp: call.createdAt,
        metadata: { callId: call.id, hostId: call.hostId }
      });
    });

    // Sort all activities by timestamp
    activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply pagination
    const paginatedActivities = activities.slice(offset, offset + parseInt(limit));

    res.json({
      logs: paginatedActivities,
      totalCount: activities.length,
      currentPage: parseInt(page),
      totalPages: Math.ceil(activities.length / limit)
    });
  } catch (error) {
    console.error('Get logs error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// System settings
router.get('/settings', async (req, res) => {
  try {
    // In a real application, these would come from a settings table
    const settings = {
      general: {
        siteName: 'FamEduConnect',
        siteDescription: 'Connecting families and education',
        maintenanceMode: false,
        registrationEnabled: true,
        maxFileSize: 10485760, // 10MB
        supportedLanguages: ['en', 'es', 'fr', 'de', 'zh', 'ar', 'hi', 'pt', 'ru', 'ja']
      },
      security: {
        passwordMinLength: 8,
        sessionTimeout: 24, // hours
        maxLoginAttempts: 5,
        lockoutDuration: 120, // minutes
        twoFactorRequired: false,
        biometricEnabled: true
      },
      communication: {
        emailNotifications: true,
        smsNotifications: true,
        pushNotifications: true,
        autoTranslation: true,
        moderationEnabled: true
      },
      videoCall: {
        maxParticipants: 50,
        recordingEnabled: true,
        screenShareEnabled: true,
        chatEnabled: true,
        qualitySettings: 'auto'
      }
    };

    res.json(settings);
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update system settings
router.patch('/settings', async (req, res) => {
  try {
    const { category, settings } = req.body;

    // In a real application, you would update the settings in the database
    // For now, we'll just return success
    
    res.json({ 
      message: 'Settings updated successfully',
      category,
      settings
    });
  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Analytics and reports
router.get('/analytics/usage', async (req, res) => {
  try {
    const { period = '30' } = req.query; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Daily active users
    const dailyActiveUsers = await User.findAll({
      where: {
        lastLogin: { [Op.gte]: startDate }
      },
      attributes: [
        [require('sequelize').fn('DATE', require('sequelize').col('lastLogin')), 'date'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count']
      ],
      group: [require('sequelize').fn('DATE', require('sequelize').col('lastLogin'))],
      order: [[require('sequelize').fn('DATE', require('sequelize').col('lastLogin')), 'ASC']],
      raw: true
    });

    // Message volume
    const messageVolume = await Message.findAll({
      where: {
        createdAt: { [Op.gte]: startDate }
      },
      attributes: [
        [require('sequelize').fn('DATE', require('sequelize').col('createdAt')), 'date'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count']
      ],
      group: [require('sequelize').fn('DATE', require('sequelize').col('createdAt'))],
      order: [[require('sequelize').fn('DATE', require('sequelize').col('createdAt')), 'ASC']],
      raw: true
    });

    // Video call usage
    const videoCallUsage = await VideoCall.findAll({
      where: {
        createdAt: { [Op.gte]: startDate }
      },
      attributes: [
        [require('sequelize').fn('DATE', require('sequelize').col('createdAt')), 'date'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count']
      ],
      group: [require('sequelize').fn('DATE', require('sequelize').col('createdAt'))],
      order: [[require('sequelize').fn('DATE', require('sequelize').col('createdAt')), 'ASC']],
      raw: true
    });

    res.json({
      dailyActiveUsers,
      messageVolume,
      videoCallUsage,
      period: parseInt(period)
    });
  } catch (error) {
    console.error('Get usage analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Performance metrics
router.get('/analytics/performance', async (req, res) => {
  try {
    // Average attendance rate across all students
    const avgAttendance = await Student.findOne({
      attributes: [
        [require('sequelize').fn('AVG', require('sequelize').col('attendanceRate')), 'average']
      ],
      raw: true
    });

    // Average performance score
    const avgPerformance = await Student.findOne({
      attributes: [
        [require('sequelize').fn('AVG', require('sequelize').col('performanceScore')), 'average']
      ],
      raw: true
    });

    // Performance by grade
    const performanceByGrade = await Student.findAll({
      attributes: [
        'grade',
        [require('sequelize').fn('AVG', require('sequelize').col('attendanceRate')), 'avgAttendance'],
        [require('sequelize').fn('AVG', require('sequelize').col('performanceScore')), 'avgPerformance'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'studentCount']
      ],
      group: ['grade'],
      order: ['grade'],
      raw: true
    });

    res.json({
      overall: {
        averageAttendance: parseFloat(avgAttendance.average || 0).toFixed(2),
        averagePerformance: parseFloat(avgPerformance.average || 0).toFixed(2)
      },
      byGrade: performanceByGrade.map(item => ({
        grade: item.grade,
        averageAttendance: parseFloat(item.avgAttendance || 0).toFixed(2),
        averagePerformance: parseFloat(item.avgPerformance || 0).toFixed(2),
        studentCount: parseInt(item.studentCount)
      }))
    });
  } catch (error) {
    console.error('Get performance analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Send system-wide notification
router.post('/notifications/broadcast', [
  body('title').trim().isLength({ min: 1, max: 200 }),
  body('message').trim().isLength({ min: 1 }),
  body('type').isIn(['announcement', 'emergency', 'system', 'maintenance']),
  body('priority').optional().isIn(['low', 'normal', 'high', 'urgent'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { title, message, type, priority = 'normal', targetRoles } = req.body;

    // Get target users
    let whereClause = { isActive: true };
    if (targetRoles && targetRoles.length > 0) {
      whereClause.role = { [Op.in]: targetRoles };
    }

    const targetUsers = await User.findAll({
      where: whereClause,
      attributes: ['id']
    });

    // Create notifications for all target users
    const notifications = targetUsers.map(user => ({
      userId: user.id,
      title,
      message,
      type,
      priority,
      deliveryMethod: {
        push: true,
        email: priority === 'urgent' || priority === 'high',
        sms: priority === 'urgent'
      }
    }));

    await Notification.bulkCreate(notifications);

    // Emit real-time notification via Socket.IO
    const io = req.app.get('io');
    if (io) {
      if (targetRoles && targetRoles.length > 0) {
        targetRoles.forEach(role => {
          io.to(`role_${role}`).emit('system_notification', {
            title,
            message,
            type,
            priority,
            timestamp: new Date()
          });
        });
      } else {
        io.emit('system_notification', {
          title,
          message,
          type,
          priority,
          timestamp: new Date()
        });
      }
    }

    res.json({ 
      message: 'Notification sent successfully',
      recipientCount: targetUsers.length
    });
  } catch (error) {
    console.error('Broadcast notification error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Export data
router.get('/export/:type', async (req, res) => {
  try {
    const { type } = req.params;
    const { format = 'json', startDate, endDate } = req.query;

    let data = [];
    let filename = '';

    switch (type) {
      case 'users':
        data = await User.findAll({
          attributes: { exclude: ['password', 'twoFactorSecret', 'encryptionKey'] },
          where: startDate && endDate ? {
            createdAt: { [Op.between]: [startDate, endDate] }
          } : {}
        });
        filename = `users_export_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'students':
        data = await Student.findAll({
          include: [
            {
              model: User,
              as: 'parent',
              attributes: ['firstName', 'lastName', 'email']
            },
            {
              model: Class,
              as: 'class',
              attributes: ['className', 'classCode', 'grade']
            }
          ],
          where: startDate && endDate ? {
            createdAt: { [Op.between]: [startDate, endDate] }
          } : {}
        });
        filename = `students_export_${new Date().toISOString().split('T')[0]}`;
        break;

      case 'attendance':
        data = await Attendance.findAll({
          include: [
            {
              model: Student,
              as: 'student',
              attributes: ['firstName', 'lastName', 'studentId']
            },
            {
              model: Class,
              as: 'class',
              attributes: ['className', 'classCode']
            }
          ],
          where: startDate && endDate ? {
            date: { [Op.between]: [startDate, endDate] }
          } : {}
        });
        filename = `attendance_export_${new Date().toISOString().split('T')[0]}`;
        break;

      default:
        return res.status(400).json({ message: 'Invalid export type' });
    }

    if (format === 'csv') {
      // Convert to CSV format
      const csv = convertToCSV(data);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      res.send(csv);
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
      res.json(data);
    }
  } catch (error) {
    console.error('Export data error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function to convert JSON to CSV
function convertToCSV(data) {
  if (data.length === 0) return '';
  
  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
      }).join(',')
    )
  ].join('\n');
  
  return csvContent;
}

module.exports = router;