apiVersion: v1
kind: Namespace
metadata:
  name: fameduconnect-database
  labels:
    name: fameduconnect-database
    environment: production
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: fameduconnect-database
data:
  POSTGRES_DB: fameduconnect_prod
  POSTGRES_USER: fameduconnect
  POSTGRES_PASSWORD: "fameduconnect_prod_password"
  POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
  POSTGRES_SHARED_PRELOAD_LIBRARIES: "pg_stat_statements,auto_explain"
  POSTGRES_MAX_CONNECTIONS: "200"
  POSTGRES_SHARED_BUFFERS: "256MB"
  POSTGRES_EFFECTIVE_CACHE_SIZE: "1GB"
  POSTGRES_WORK_MEM: "4MB"
  POSTGRES_MAINTENANCE_WORK_MEM: "64MB"
  POSTGRES_CHECKPOINT_SEGMENTS: "32"
  POSTGRES_CHECKPOINT_COMPLETION_TARGET: "0.9"
  POSTGRES_WAL_BUFFERS: "16MB"
  POSTGRES_DEFAULT_STATISTICS_TARGET: "100"
  POSTGRES_RANDOM_PAGE_COST: "1.1"
  POSTGRES_EFFECTIVE_IO_CONCURRENCY: "200"
  POSTGRES_WORK_MEM: "4MB"
  POSTGRES_MAINTENANCE_WORK_MEM: "64MB"
  POSTGRES_AUTOVACUUM_MAX_WORKERS: "3"
  POSTGRES_AUTOVACUUM_NAPTIME: "1min"
  POSTGRES_LOG_STATEMENT: "all"
  POSTGRES_LOG_DESTINATION: "stderr"
  POSTGRES_LOGGING_COLLECTOR: "on"
  POSTGRES_LOG_DIRECTORY: "log"
  POSTGRES_LOG_FILENAME: "postgresql-%Y-%m-%d_%H%M%S.log"
  POSTGRES_LOG_ROTATION_AGE: "1d"
  POSTGRES_LOG_ROTATION_SIZE: "100MB"
  POSTGRES_LOG_MIN_MESSAGES: "warning"
  POSTGRES_LOG_LINE_PREFIX: "%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h "
  POSTGRES_LOG_CHECKPOINTS: "on"
  POSTGRES_LOG_CONNECTIONS: "on"
  POSTGRES_LOG_DISCONNECTIONS: "on"
  POSTGRES_LOG_DURATION: "on"
  POSTGRES_LOG_LOCK_WAITS: "on"
  POSTGRES_LOG_TEMP_FILES: "on"
  POSTGRES_LOG_AUTOVACUUM_MIN_DURATION: "0"
  POSTGRES_LOG_ERROR_VERBOSITY: "verbose"
  POSTGRES_LOG_LOG_ON_ERROR: "on"
  POSTGRES_LOG_VERBOSITY: "verbose"
---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secrets
  namespace: fameduconnect-database
type: Opaque
data:
  DB_PASSWORD: "${BASE64_ENCODED_DB_PASSWORD}"
  DB_ROOT_PASSWORD: "${BASE64_ENCODED_DB_ROOT_PASSWORD}"
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: fameduconnect-postgres
  namespace: fameduconnect-database
  labels:
    app: fameduconnect-postgres
    version: v1.0.0
spec:
  serviceName: fameduconnect-postgres
  replicas: 3
  selector:
    matchLabels:
      app: fameduconnect-postgres
  template:
    metadata:
      labels:
        app: fameduconnect-postgres
        version: v1.0.0
    spec:
      serviceAccountName: fameduconnect-db-sa
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
          name: postgres
        envFrom:
        - configMapRef:
            name: postgres-config
        - secretRef:
            name: postgres-secrets
        env:
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        - name: POSTGRES_HOST_AUTH_METHOD
          value: "scram-sha-256"
        - name: POSTGRES_PASSWORD_FILE
          value: /run/secrets/postgres/password
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - fameduconnect
            - -d
            - fameduconnect_prod
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - fameduconnect
            - -d
            - fameduconnect_prod
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 999
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        - name: postgres-secrets
          mountPath: /run/secrets/postgres
          readOnly: true
        - name: postgres-logs
          mountPath: /var/lib/postgresql/log
      volumes:
      - name: postgres-config
        configMap:
          name: postgres-config
      - name: postgres-secrets
        secret:
          secretName: postgres-secrets
      - name: postgres-logs
        persistentVolumeClaim:
          claimName: fameduconnect-logs-pvc
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 100Gi
---
apiVersion: v1
kind: Service
metadata:
  name: fameduconnect-postgres
  namespace: fameduconnect-database
  labels:
    app: fameduconnect-postgres
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: fameduconnect-postgres
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fameduconnect-db-sa
  namespace: fameduconnect-database
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: fameduconnect-db-role
  namespace: fameduconnect-database
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["statefulsets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: fameduconnect-db-role-binding
  namespace: fameduconnect-database
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: fameduconnect-db-role
subjects:
- kind: ServiceAccount
  name: fameduconnect-db-sa
  namespace: fameduconnect-database
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: fameduconnect-db-backup
  namespace: fameduconnect-database
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: fameduconnect-db-sa
          containers:
          - name: backup
            image: postgres:15-alpine
            command:
            - /bin/sh
            - -c
            - |
              set -e
              DATE=$(date +%Y%m%d_%H%M%S)
              BACKUP_FILE="/backup/fameduconnect_prod_${DATE}.sql"
              pg_dump -h fameduconnect-postgres -U fameduconnect -d fameduconnect_prod > $BACKUP_FILE
              gzip $BACKUP_FILE
              echo "Backup completed: ${BACKUP_FILE}.gz"
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secrets
                  key: DB_PASSWORD
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: fameduconnect-backup-pvc
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: fameduconnect-backup-pvc
  namespace: fameduconnect-database
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: fast-ssd
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: fameduconnect-postgres-pdb
  namespace: fameduconnect-database
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: fameduconnect-postgres 