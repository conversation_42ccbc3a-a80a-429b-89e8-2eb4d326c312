# FamEduConnect App Store Screenshot Guide

## Overview

This guide provides detailed instructions for creating high-quality screenshots for App Store and Google Play Store submissions. Each screenshot should showcase a key feature of FamEduConnect while maintaining visual consistency and brand identity.

## Screenshot Requirements

### Apple App Store

| Device | Size (pixels) | Format | Quantity |
|--------|---------------|--------|----------|
| iPhone 6.5" | 1284 x 2778 | PNG or JPG | 3-10 |
| iPhone 5.5" | 1242 x 2208 | PNG or JPG | 3-10 |
| iPad Pro 12.9" | 2048 x 2732 | PNG or JPG | 3-10 |
| iPad Pro 11" | 1668 x 2388 | PNG or JPG | 3-10 |

### Google Play Store

| Type | Size (pixels) | Format | Quantity |
|------|---------------|--------|----------|
| Phone | 1080 x 1920 | PNG or JPG | 2-8 |
| 7-inch Tablet | 1200 x 1920 | PNG or JPG | 2-8 |
| 10-inch Tablet | 1920 x 1200 | PNG or JPG | 2-8 |

## Screenshot Scenarios

Create the following scenarios for each device type:

### 1. Home Dashboard

**Setup:**
- Log in as a parent user
- Navigate to the main dashboard
- Ensure multiple notification cards are visible
- Show upcoming events in the calendar widget

**Caption:**
"Stay connected with your educational community"

**Key Elements to Highlight:**
- Clean, organized dashboard layout
- Activity feed with recent updates
- Calendar with upcoming events
- Quick access buttons for key features

### 2. Messaging Interface

**Setup:**
- Open a conversation between a parent and teacher
- Show a mix of text messages and a shared document
- Include read receipts and typing indicator
- Display translation feature if possible

**Caption:**
"Communicate securely with teachers and parents"

**Key Elements to Highlight:**
- Clean messaging interface
- File attachment preview
- Translation button
- Encryption indicator

### 3. Video Call Screen

**Setup:**
- Set up a mock video call between teacher and parent
- Show active call with good lighting and professional appearance
- Display call controls and features
- Include caption/translation feature if possible

**Caption:**
"Face-to-face conversations with built-in translation"

**Key Elements to Highlight:**
- High-quality video display
- Call controls (mute, camera, end call)
- Screen sharing option
- Live caption/translation feature

### 4. File Sharing

**Setup:**
- Navigate to the document sharing section
- Show a variety of educational documents
- Display document preview with annotation tools
- Include sharing options and permissions

**Caption:**
"Share and collaborate on important documents"

**Key Elements to Highlight:**
- Document organization
- Preview functionality
- Annotation tools
- Sharing permissions interface

### 5. Academic Progress

**Setup:**
- Open a student's academic progress page
- Show grades, attendance, and assignment completion
- Display progress charts and graphs
- Include teacher comments if possible

**Caption:**
"Track grades, attendance, and assignments in real-time"

**Key Elements to Highlight:**
- Visual grade representation
- Attendance statistics
- Assignment completion tracking
- Progress trends over time

### 6. Calendar View

**Setup:**
- Open the calendar view
- Show a mix of classes, assignments, and events
- Display different color coding for event types
- Include event details for a selected item

**Caption:**
"Never miss important school events or deadlines"

**Key Elements to Highlight:**
- Month view with color-coded events
- Event details popup
- Add/edit event functionality
- Calendar sharing options

### 7. Multilingual Support

**Setup:**
- Show the language selection interface
- Display content in multiple languages
- Include the translation feature in action
- Show RTL support for Arabic if possible

**Caption:**
"Break down language barriers with automatic translation"

**Key Elements to Highlight:**
- Language selection dropdown
- Translated interface elements
- Message translation in action
- RTL layout support

### 8. Notification Center

**Setup:**
- Open the notification center
- Show various notification types (messages, assignments, events)
- Display notification settings
- Include read/unread status indicators

**Caption:**
"Stay informed with customizable alerts"

**Key Elements to Highlight:**
- Organized notification categories
- Clear notification content
- Action buttons on notifications
- Notification preference settings

## Visual Guidelines

### Branding
- Use FamEduConnect color palette:
  - Primary Blue: #2563EB
  - Secondary Teal: #0D9488
  - Accent Orange: #F97316
  - Background Light: #F8FAFC
  - Text Dark: #1E293B

### Typography
- Use system fonts for authenticity:
  - iOS: SF Pro
  - Android: Roboto

### Device Frames
- Include device frames for context
- Use modern device models:
  - iPhone 14 Pro for iOS
  - Google Pixel 7 for Android
  - iPad Pro for tablet

### Status Bar
- Show full signal strength
- Show full battery
- Set time to 9:41 for iOS (Apple standard)
- Set time to 9:41 for Android (for consistency)

### User Data
- Use diverse fictional names and photos
- Include appropriate school-related content
- Avoid any copyrighted materials
- Use appropriate placeholder content for academic data

## Production Process

1. **Setup Test Environment:**
   - Use development build with mock data
   - Configure all scenarios with test accounts
   - Prepare all UI elements and states

2. **Capture Raw Screenshots:**
   - Use device simulators/emulators for exact dimensions
   - Capture each scenario on each required device
   - Save raw screenshots in PNG format

3. **Post-Processing:**
   - Add device frames if not captured with real devices
   - Apply any necessary color corrections
   - Add captions using brand typography
   - Ensure consistent visual style across all screenshots

4. **Quality Assurance:**
   - Verify all screenshots meet store requirements
   - Check for any personal or sensitive information
   - Ensure text is legible and UI elements are clear
   - Confirm brand guidelines are followed

5. **File Preparation:**
   - Name files according to sequence and device:
     - `01_dashboard_iphone65.png`
     - `01_dashboard_ipad129.png`
     - etc.
   - Organize in folders by platform and device type
   - Verify file formats and dimensions

## Submission Checklist

- [ ] All required device sizes captured
- [ ] All 8 scenarios represented
- [ ] Captions finalized and proofread
- [ ] Files named correctly and organized
- [ ] Screenshots reviewed for quality and accuracy
- [ ] No personal or sensitive information included
- [ ] Brand guidelines followed consistently
- [ ] Files optimized for size without quality loss

## Timeline

- **Screenshot Planning:** July 24, 2025
- **Test Environment Setup:** July 25, 2025
- **Raw Screenshot Capture:** July 26-27, 2025
- **Post-Processing:** July 28-29, 2025
- **Review and Approval:** July 30, 2025
- **Final Submission Prep:** July 31, 2025

## Responsible Team Members

- **UI/UX Lead:** Sarah Rodriguez
- **Mobile Development:** Priya Patel
- **Content Review:** Na'imah Barnes
- **Final Approval:** Michael Chen

---

© 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc. – NPO: 2023-001341848  
Creative Director: Na'imah Barnes