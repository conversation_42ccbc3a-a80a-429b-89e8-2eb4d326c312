import React from 'react';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { logout } from '../../store/slices/authSlice';
import { motion, AnimatePresence } from 'framer-motion';
import {
  HomeIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  AcademicCapIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  UserIcon,
  ChartBarIcon,
  BellIcon,
  CalendarIcon,
  DocumentTextIcon,
  PhoneIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';

const Sidebar = ({ isOpen, isMobile, onClose }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const { unreadMessages, unreadNotifications } = useSelector((state) => state.notifications);

  const getNavigationItems = () => {
    const baseItems = [
      {
        name: 'Dashboard',
        href: user?.role === 'admin' ? '/admin' : 
             user?.role === 'teacher' ? '/dashboard/teacher' :
             user?.role === 'student' ? '/dashboard/student' : '/dashboard/parent',
        icon: HomeIcon,
        badge: null
      },
      {
        name: 'Messages',
        href: '/messages',
        icon: ChatBubbleLeftRightIcon,
        badge: unreadMessages > 0 ? unreadMessages : null
      },
      {
        name: 'Video Calls',
        href: '/video-call',
        icon: VideoCameraIcon,
        badge: null
      }
    ];

    // Role-specific navigation items
    if (user?.role === 'parent') {
      baseItems.push(
        {
          name: 'My Children',
          href: '/students',
          icon: AcademicCapIcon,
          badge: null
        },
        {
          name: 'Classes',
          href: '/classes',
          icon: UserGroupIcon,
          badge: null
        }
      );
    } else if (user?.role === 'teacher') {
      baseItems.push(
        {
          name: 'My Classes',
          href: '/classes',
          icon: UserGroupIcon,
          badge: null
        },
        {
          name: 'Students',
          href: '/students',
          icon: AcademicCapIcon,
          badge: null
        }
      );
    } else if (user?.role === 'admin') {
      baseItems.push(
        {
          name: 'Admin Dashboard',
          href: '/admin',
          icon: ChartBarIcon,
          badge: null
        },
        {
          name: 'Users',
          href: '/admin/users',
          icon: UserGroupIcon,
          badge: null
        },
        {
          name: 'Classes',
          href: '/admin/classes',
          icon: AcademicCapIcon,
          badge: null
        },
        {
          name: 'Reports',
          href: '/admin/reports',
          icon: DocumentTextIcon,
          badge: null
        },
        {
          name: 'Admin Settings',
          href: '/admin/settings',
          icon: Cog6ToothIcon,
          badge: null
        }
      );
    }

    // Common bottom items (only show if not admin, since admin has its own settings)
    if (user?.role !== 'admin') {
      baseItems.push(
        {
          name: 'Profile',
          href: '/profile',
          icon: UserIcon,
          badge: null
        },
        {
          name: 'Settings',
          href: '/settings',
          icon: Cog6ToothIcon,
          badge: null
        }
      );
    }

    return baseItems;
  };

  const navigationItems = getNavigationItems();

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <AnimatePresence>
      {(isOpen || !isMobile) && (
        <motion.div
          initial={isMobile ? 'closed' : 'open'}
          animate="open"
          exit="closed"
          variants={sidebarVariants}
          className={`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out ${
            isMobile ? '' : 'relative'
          }`}
        >
          {/* Sidebar Header */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-bold">F</span>
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                  FamEduConnect
                </h1>
                <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                  {user?.role || 'User'}
                </p>
              </div>
            </div>
            
            {isMobile && (
              <button
                onClick={onClose}
                className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
              >
                <span className="sr-only">Close sidebar</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>

          {/* User Info */}
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center overflow-hidden">
                {user?.profilePicture ? (
                  <img
                    src={user.profilePicture}
                    alt={`${user.firstName} ${user.lastName}`}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-gray-600 dark:text-gray-300 text-sm font-medium">
                    {user?.firstName?.[0]}{user?.lastName?.[0]}
                  </span>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {user?.email}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
            {navigationItems.map((item) => {
              const isActive = location.pathname === item.href || 
                             (item.href !== '/dashboard' && location.pathname.startsWith(item.href));
              
              return (
                <NavLink
                  key={item.name}
                  to={item.href}
                  onClick={isMobile ? onClose : undefined}
                  className={({ isActive: linkActive }) => {
                    const active = isActive || linkActive;
                    return `group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 ${
                      active
                        ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-200'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                    }`;
                  }}
                >
                  <item.icon
                    className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-150 ${
                      isActive
                        ? 'text-indigo-500 dark:text-indigo-300'
                        : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400'
                    }`}
                  />
                  <span className="flex-1">{item.name}</span>
                  {item.badge && (
                    <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                      {item.badge > 99 ? '99+' : item.badge}
                    </span>
                  )}
                </NavLink>
              );
            })}
          </nav>

          {/* Emergency Contact */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <button 
              onClick={() => {
                // Emergency contact functionality
                window.open('tel:911', '_self');
              }}
              className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors duration-150"
            >
              <PhoneIcon className="h-4 w-4 mr-2" />
              Emergency Contact
            </button>
          </div>

          {/* Logout Button */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <button 
              onClick={async () => {
                await dispatch(logout());
                navigate('/login');
              }}
              className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors duration-150"
            >
              <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
              Logout
            </button>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                FamEduConnect v1.0.0
              </p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Secure • Encrypted • FERPA Compliant
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Sidebar;