const axios = require('axios');

// Test configuration
const config = {
  backendUrl: 'http://localhost:5555/api',
  frontendUrl: 'http://localhost:3000',
  testUser: {
    email: '<EMAIL>',
    password: 'AdminDemo2025!'
  }
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testBackendHealth() {
  try {
    log('🔍 Testing backend health...', 'blue');
    const response = await axios.get(`${config.backendUrl}/test`);
    log(`✅ Backend is healthy: ${response.data.message}`, 'green');
    return true;
  } catch (error) {
    log(`❌ Backend health check failed: ${error.message}`, 'red');
    return false;
  }
}

async function testFrontendHealth() {
  try {
    log('🔍 Testing frontend health...', 'blue');
    const response = await axios.get(config.frontendUrl);
    if (response.status === 200) {
      log('✅ Frontend is responding', 'green');
      return true;
    } else {
      log(`❌ Frontend returned status: ${response.status}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Frontend health check failed: ${error.message}`, 'red');
    return false;
  }
}

async function testAuthentication() {
  try {
    log('🔍 Testing authentication...', 'blue');
    
    // Test login
    const loginResponse = await axios.post(`${config.backendUrl}/auth/login`, {
      email: config.testUser.email,
      password: config.testUser.password
    });
    
    if (loginResponse.data.token) {
      log('✅ Authentication successful', 'green');
      log(`   Token: ${loginResponse.data.token.substring(0, 20)}...`, 'yellow');
      return loginResponse.data.token;
    } else {
      log('❌ Authentication failed - no token received', 'red');
      return null;
    }
  } catch (error) {
    log(`❌ Authentication failed: ${error.response?.data?.message || error.message}`, 'red');
    return null;
  }
}

async function testUserProfile(token) {
  try {
    log('🔍 Testing user profile API...', 'blue');
    
    const response = await axios.get(`${config.backendUrl}/users/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.data.user) {
      log('✅ User profile API working', 'green');
      log(`   User: ${response.data.user.firstName} ${response.data.user.lastName}`, 'yellow');
      return true;
    } else {
      log('❌ User profile API failed - no user data', 'red');
      return false;
    }
  } catch (error) {
    log(`❌ User profile API failed: ${error.response?.data?.message || error.message}`, 'red');
    return false;
  }
}

async function testMessagesAPI(token) {
  try {
    log('🔍 Testing messages API...', 'blue');
    
    const response = await axios.get(`${config.backendUrl}/messages`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    log('✅ Messages API working', 'green');
    log(`   Messages count: ${response.data.messages?.length || 0}`, 'yellow');
    return true;
  } catch (error) {
    log(`❌ Messages API failed: ${error.response?.data?.message || error.message}`, 'red');
    return false;
  }
}

async function testStudentsAPI(token) {
  try {
    log('🔍 Testing students API...', 'blue');
    
    const response = await axios.get(`${config.backendUrl}/students`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    log('✅ Students API working', 'green');
    log(`   Students count: ${response.data.students?.length || 0}`, 'yellow');
    return true;
  } catch (error) {
    log(`❌ Students API failed: ${error.response?.data?.message || error.message}`, 'red');
    return false;
  }
}

async function testClassesAPI(token) {
  try {
    log('🔍 Testing classes API...', 'blue');
    
    const response = await axios.get(`${config.backendUrl}/classes`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    log('✅ Classes API working', 'green');
    log(`   Classes count: ${response.data.classes?.length || 0}`, 'yellow');
    return true;
  } catch (error) {
    log(`❌ Classes API failed: ${error.response?.data?.message || error.message}`, 'red');
    return false;
  }
}

async function runEndToEndTest() {
  log('🚀 Starting FamEduConnect End-to-End Test', 'blue');
  log('=' * 50, 'blue');
  
  const results = {
    backendHealth: false,
    frontendHealth: false,
    authentication: false,
    userProfile: false,
    messagesAPI: false,
    studentsAPI: false,
    classesAPI: false
  };
  
  // Test 1: Backend Health
  results.backendHealth = await testBackendHealth();
  
  // Test 2: Frontend Health
  results.frontendHealth = await testFrontendHealth();
  
  // Test 3: Authentication
  const token = await testAuthentication();
  results.authentication = !!token;
  
  if (token) {
    // Test 4: User Profile
    results.userProfile = await testUserProfile(token);
    
    // Test 5: Messages API
    results.messagesAPI = await testMessagesAPI(token);
    
    // Test 6: Students API
    results.studentsAPI = await testStudentsAPI(token);
    
    // Test 7: Classes API
    results.classesAPI = await testClassesAPI(token);
  }
  
  // Summary
  log('\n📊 Test Results Summary:', 'blue');
  log('=' * 30, 'blue');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    const color = passed ? 'green' : 'red';
    log(`${status} ${test}`, color);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  log(`\n🎯 Overall Result: ${passedTests}/${totalTests} tests passed`, passedTests === totalTests ? 'green' : 'red');
  
  if (passedTests === totalTests) {
    log('\n🎉 All tests passed! FamEduConnect is fully functional.', 'green');
    log('🌐 You can now access the application at: http://localhost:3000', 'green');
    log('🔑 Login with: <EMAIL> / AdminDemo2025!', 'green');
  } else {
    log('\n⚠️  Some tests failed. Please check the errors above.', 'yellow');
  }
  
  return results;
}

// Run the test
runEndToEndTest().catch(error => {
  log(`❌ Test runner failed: ${error.message}`, 'red');
  process.exit(1);
}); 