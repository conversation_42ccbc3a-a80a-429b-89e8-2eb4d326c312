import React, { Component } from 'react';
import PropTypes from 'prop-types';
import SafeErrorDisplay from '../UI/SafeErrorDisplay';

/**
 * Base Component using Class-based architecture
 * Provides common functionality for all components
 */
class BaseComponent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      error: null,
      mounted: false
    };
  }

  componentDidMount() {
    this.setState({ mounted: true });
    this.onMount();
  }

  componentWillUnmount() {
    this.setState({ mounted: false });
    this.onUnmount();
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ error: error.message || 'An error occurred' });
    this.onError(error, errorInfo);
  }

  // Override these methods in child components
  onMount() {}
  onUnmount() {}
  onError(error, errorInfo) {
    console.error('Component Error:', error, errorInfo);
  }

  setLoading = (loading) => {
    if (this.state.mounted) {
      this.setState({ loading });
    }
  }

  setError = (error) => {
    if (this.state.mounted) {
      const errorMessage = typeof error === 'string' ? error : 'An error occurred';
      this.setState({ error: errorMessage });
    }
  }

  clearError = () => {
    if (this.state.mounted) {
      this.setState({ error: null });
    }
  }

  render() {
    const { children, fallback } = this.props;
    const { loading, error } = this.state;

    if (error) {
      return (
        <div className="error-boundary p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-red-800 font-medium">Something went wrong</h3>
          <SafeErrorDisplay error={error} className="mt-1" />
          <button 
            onClick={this.clearError}
            className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      );
    }

    if (loading && fallback) {
      return fallback;
    }

    return children;
  }
}

BaseComponent.propTypes = {
  children: PropTypes.node,
  fallback: PropTypes.node
};

export default BaseComponent;