import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { SunIcon, CloudIcon, MapPinIcon } from '@heroicons/react/24/outline';

const WeatherWidget = () => {
  const [weather, setWeather] = useState({
    temperature: 22,
    condition: 'sunny',
    location: 'School District',
    humidity: 65,
    windSpeed: 8
  });

  const getWeatherIcon = (condition) => {
    switch (condition) {
      case 'sunny':
        return <SunIcon className="h-8 w-8 text-yellow-500" />;
      case 'cloudy':
        return <CloudIcon className="h-8 w-8 text-gray-500" />;
      default:
        return <SunIcon className="h-8 w-8 text-yellow-500" />;
    }
  };

  const getWeatherBg = (condition) => {
    switch (condition) {
      case 'sunny':
        return 'bg-gradient-to-br from-yellow-400 to-orange-500';
      case 'cloudy':
        return 'bg-gradient-to-br from-gray-400 to-gray-600';
      default:
        return 'bg-gradient-to-br from-blue-400 to-blue-600';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className={`p-4 rounded-lg text-white ${getWeatherBg(weather.condition)}`}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2 mb-1">
            <MapPinIcon className="h-4 w-4" />
            <span className="text-sm opacity-90">{weather.location}</span>
          </div>
          <div className="text-2xl font-bold">{weather.temperature}°C</div>
          <div className="text-sm opacity-90 capitalize">{weather.condition}</div>
        </div>
        <div className="text-right">
          {getWeatherIcon(weather.condition)}
          <div className="text-xs opacity-75 mt-1">
            <div>Humidity: {weather.humidity}%</div>
            <div>Wind: {weather.windSpeed} km/h</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default WeatherWidget;