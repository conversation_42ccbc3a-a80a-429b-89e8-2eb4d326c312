import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  VideoCameraIcon, 
  MicrophoneIcon, 
  Cog6ToothIcon,
  UserGroupIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../UI/LoadingSpinner';

const WaitingRoom = ({ callId }) => {
  const [isReady, setIsReady] = useState(false);
  const [devicePermissions, setDevicePermissions] = useState({
    camera: false,
    microphone: false
  });
  const [previewStream, setPreviewStream] = useState(null);

  useEffect(() => {
    requestPermissions();
    return () => {
      if (previewStream) {
        previewStream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const requestPermissions = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      setPreviewStream(stream);
      setDevicePermissions({
        camera: true,
        microphone: true
      });
      setIsReady(true);
    } catch (error) {
      console.error('Permission error:', error);
      // Try audio only
      try {
        const audioStream = await navigator.mediaDevices.getUserMedia({
          audio: true
        });
        setPreviewStream(audioStream);
        setDevicePermissions({
          camera: false,
          microphone: true
        });
        setIsReady(true);
      } catch (audioError) {
        console.error('Audio permission error:', audioError);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* Video Preview */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="relative"
          >
            <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
              {previewStream && devicePermissions.camera ? (
                <video
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-cover"
                  ref={(video) => {
                    if (video && previewStream) {
                      video.srcObject = previewStream;
                    }
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
                      <VideoCameraIcon className="h-10 w-10 text-gray-400" />
                    </div>
                    <p className="text-gray-400">Camera not available</p>
                  </div>
                </div>
              )}
            </div>
            
            {/* Preview Controls */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-4">
              <button className="p-3 bg-gray-700 hover:bg-gray-600 rounded-full transition-colors">
                <MicrophoneIcon className="h-5 w-5" />
              </button>
              <button className="p-3 bg-gray-700 hover:bg-gray-600 rounded-full transition-colors">
                <VideoCameraIcon className="h-5 w-5" />
              </button>
              <button className="p-3 bg-gray-700 hover:bg-gray-600 rounded-full transition-colors">
                <Cog6ToothIcon className="h-5 w-5" />
              </button>
            </div>
          </motion.div>

          {/* Waiting Room Info */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="space-y-6"
          >
            <div>
              <h1 className="text-3xl font-bold mb-2">Getting ready to join</h1>
              <p className="text-gray-400">
                We're preparing your connection to the video call
              </p>
            </div>

            {/* Device Status */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Device Check</h3>
              
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  devicePermissions.camera ? 'bg-green-500' : 'bg-red-500'
                }`} />
                <VideoCameraIcon className="h-5 w-5 text-gray-400" />
                <span className="text-sm">
                  Camera {devicePermissions.camera ? 'Ready' : 'Not Available'}
                </span>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  devicePermissions.microphone ? 'bg-green-500' : 'bg-red-500'
                }`} />
                <MicrophoneIcon className="h-5 w-5 text-gray-400" />
                <span className="text-sm">
                  Microphone {devicePermissions.microphone ? 'Ready' : 'Not Available'}
                </span>
              </div>
            </div>

            {/* Call Info */}
            <div className="bg-gray-800 rounded-lg p-4 space-y-3">
              <div className="flex items-center space-x-2">
                <UserGroupIcon className="h-5 w-5 text-gray-400" />
                <span className="text-sm">Call ID: {callId}</span>
              </div>
              <div className="flex items-center space-x-2">
                <ClockIcon className="h-5 w-5 text-gray-400" />
                <span className="text-sm">Scheduled for now</span>
              </div>
            </div>

            {/* Join Button */}
            <div className="space-y-3">
              {!isReady ? (
                <div className="flex items-center justify-center space-x-3 py-3">
                  <LoadingSpinner size="sm" color="white" />
                  <span>Checking devices...</span>
                </div>
              ) : (
                <button
                  className="w-full py-3 bg-indigo-600 hover:bg-indigo-700 rounded-lg font-medium transition-colors"
                  onClick={() => window.location.reload()}
                >
                  Join Call
                </button>
              )}
              
              <p className="text-xs text-gray-500 text-center">
                By joining, you agree to our terms of service and privacy policy
              </p>
            </div>

            {/* Tips */}
            <div className="bg-blue-900/20 border border-blue-800 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-blue-300 mb-2">Tips for a better call:</h4>
              <ul className="text-xs text-blue-200 space-y-1">
                <li>• Use headphones to prevent echo</li>
                <li>• Ensure good lighting for video</li>
                <li>• Find a quiet environment</li>
                <li>• Check your internet connection</li>
              </ul>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default WaitingRoom;