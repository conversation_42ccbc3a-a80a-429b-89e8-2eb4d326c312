# 🚀 FamEduConnect NestJS Backend

A modern, scalable backend API for the FamEduConnect education platform built with NestJS, Prisma, and PostgreSQL.

## 🏗️ Architecture

- **Framework**: NestJS (Node.js)
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with Passport
- **Caching**: Redis
- **Message Queue**: RabbitMQ
- **Validation**: Class-validator
- **Documentation**: OpenAPI/Swagger

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Docker & Docker Compose
- PostgreSQL (or use Docker)

### 1. Clone & Setup

```bash
cd famedu-backend
npm install
```

### 2. Environment Configuration

```bash
# Copy environment file
cp env.example .env

# Edit .env with your configuration
DATABASE_URL="postgresql://postgres:password@localhost:5432/fameduconnect?schema=public"
JWT_SECRET="your-super-secret-jwt-key"
```

### 3. Start Local Services

```bash
# Start PostgreSQL, Redis, RabbitMQ
docker-compose up -d
```

### 4. Database Setup

```bash
# Generate Prisma client
npm run prisma:generate

# Run database migrations
npm run prisma:migrate

# (Optional) Seed database
npm run db:seed
```

### 5. Start Development Server

```bash
npm run start:dev
```

The API will be available at: http://localhost:3000

## 📚 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login

### Health Check
- `GET /api/health` - API health status

## 🗄️ Database Schema

The application uses Prisma with the following main entities:

- **Users** - Authentication and user management
- **Students** - Student profiles linked to parents
- **Classes** - Class management for teachers
- **Messages** - Real-time messaging system
- **VideoCalls** - Video call scheduling and management
- **Attendance** - Student attendance tracking
- **Performance** - Student performance records
- **Notifications** - System notifications

## 🔧 Development

### Available Scripts

```bash
# Development
npm run start:dev          # Start in development mode
npm run start:debug        # Start with debugger

# Building
npm run build             # Build for production
npm run start:prod        # Start production server

# Database
npm run prisma:generate   # Generate Prisma client
npm run prisma:migrate    # Run migrations
npm run prisma:studio     # Open Prisma Studio
npm run db:push          # Push schema changes
npm run db:seed          # Seed database

# Testing
npm run test             # Run unit tests
npm run test:watch       # Run tests in watch mode
npm run test:cov         # Run tests with coverage
npm run test:e2e         # Run end-to-end tests

# Code Quality
npm run lint             # Run ESLint
npm run format           # Format code with Prettier
```

### Project Structure

```
src/
├── auth/                 # Authentication module
│   ├── dto/             # Data transfer objects
│   ├── auth.controller.ts
│   ├── auth.service.ts
│   └── auth.module.ts
├── users/               # User management
├── messages/            # Messaging system
├── classes/             # Class management
├── students/            # Student management
├── prisma/              # Database service
│   ├── prisma.service.ts
│   └── prisma.module.ts
├── app.controller.ts    # Main controller
├── app.service.ts       # Main service
├── app.module.ts        # Root module
└── main.ts             # Application entry point
```

## 🔐 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Input validation with class-validator
- CORS configuration
- Rate limiting (to be implemented)
- Helmet security headers (to be implemented)

## 🧪 Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## 📦 Docker Deployment

```bash
# Build image
docker build -t famedu-backend .

# Run container
docker run -p 3000:3000 famedu-backend
```

## 🔄 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | - |
| `JWT_SECRET` | JWT signing secret | - |
| `JWT_EXPIRATION` | JWT token expiration | `24h` |
| `REDIS_URL` | Redis connection string | `redis://localhost:6379` |
| `RABBITMQ_URL` | RabbitMQ connection string | `amqp://localhost:5672` |
| `PORT` | Server port | `3000` |
| `NODE_ENV` | Environment | `development` |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository. 