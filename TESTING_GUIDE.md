# FamEduConnect Testing Guide

## Overview

This guide provides comprehensive instructions for testing the FamEduConnect application from beginning to end, including unit tests, integration tests, end-to-end tests, and manual testing procedures.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Test Types](#test-types)
3. [Running Tests](#running-tests)
4. [Manual Testing Scenarios](#manual-testing-scenarios)
5. [Test Data Setup](#test-data-setup)
6. [Troubleshooting](#troubleshooting)

## Prerequisites

Before running tests, ensure you have:

- Node.js (v16 or higher)
- npm or yarn
- Database (SQLite for testing, PostgreSQL for production)
- Git

## Test Types

### 1. Unit Tests
- **Location**: `backend/tests/`
- **Purpose**: Test individual functions and components
- **Coverage**: Models, utilities, middleware

### 2. Integration Tests
- **Location**: `backend/tests/integration.test.js`
- **Purpose**: Test API endpoints and database interactions
- **Coverage**: Authentication, CRUD operations, business logic

### 3. End-to-End Tests
- **Location**: `backend/tests/e2e.test.js`
- **Purpose**: Test complete user journeys
- **Coverage**: Full application flow from registration to dashboard

### 4. Frontend Tests
- **Location**: `frontend/src/tests/`
- **Purpose**: Test React components and user interactions
- **Coverage**: Components, pages, Redux store

## Running Tests

### Backend Tests

```bash
# Navigate to backend directory
cd FamEduConnect_Full_Codebase/backend

# Install dependencies
npm install

# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- integration.test.js

# Run tests in watch mode
npm run test:watch
```

### Frontend Tests

```bash
# Navigate to frontend directory
cd FamEduConnect_Full_Codebase/frontend

# Install dependencies
npm install

# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

### Complete Test Suite

```bash
# Run all tests (backend + frontend)
cd FamEduConnect_Full_Codebase
npm run test:all
```

## Manual Testing Scenarios

### 1. User Registration and Onboarding

#### Test Case: Complete Parent Onboarding
1. **Start**: Navigate to `/register`
2. **Register**: Create new parent account
3. **Onboarding**: Complete all onboarding steps
4. **Verify**: User lands on parent dashboard
5. **Settings**: Access settings to complete onboarding later

**Expected Results**:
- Registration successful
- Onboarding flow works correctly
- Role-based dashboard routing works
- Settings button available for incomplete onboarding

#### Test Case: Skip Onboarding
1. **Start**: Navigate to `/register`
2. **Register**: Create new account
3. **Skip**: Click "Skip for now" during onboarding
4. **Verify**: User lands on appropriate dashboard (parent/student)

**Expected Results**:
- User can skip onboarding
- Default role assignment works
- Dashboard accessible without complete onboarding

### 2. Role-Based Dashboard Testing

#### Test Case: Parent Dashboard
1. **Login**: As parent user
2. **Navigate**: To `/dashboard/parent`
3. **Verify**: Parent-specific features available
4. **Test**: Complete onboarding button
5. **Test**: Settings access

**Expected Results**:
- Parent dashboard displays correctly
- Children information visible
- Messages and events accessible
- Complete onboarding button functional

#### Test Case: Teacher Dashboard
1. **Login**: As teacher user
2. **Navigate**: To `/dashboard/teacher`
3. **Verify**: Teacher-specific features available
4. **Test**: Class management features
5. **Test**: Student progress tracking

**Expected Results**:
- Teacher dashboard displays correctly
- Class management tools available
- Student information accessible
- Assignment creation functional

#### Test Case: Student Dashboard
1. **Login**: As student user
2. **Navigate**: To `/dashboard/student`
3. **Verify**: Student-specific features available
4. **Test**: Assignment viewing
5. **Test**: Grade checking

**Expected Results**:
- Student dashboard displays correctly
- Assignments and grades visible
- Class schedule accessible
- Learning resources available

#### Test Case: Admin Dashboard
1. **Login**: As admin user
2. **Navigate**: To `/admin`
3. **Verify**: Admin-specific features available
4. **Test**: User management
5. **Test**: System analytics

**Expected Results**:
- Admin dashboard displays correctly
- User management tools available
- System analytics visible
- Administrative controls functional

### 3. AI Assistant Testing

#### Test Case: Voice Recognition
1. **Open**: AI assistant in onboarding
2. **Click**: Microphone button
3. **Speak**: "Help me with onboarding"
4. **Verify**: Voice input captured
5. **Test**: AI response generation

**Expected Results**:
- Voice recognition works
- AI responds appropriately
- Navigation commands work via voice

#### Test Case: Text Input
1. **Open**: AI assistant
2. **Type**: "What is my role?"
3. **Send**: Message
4. **Verify**: AI response
5. **Test**: Navigation commands

**Expected Results**:
- Text input works
- AI provides helpful responses
- Navigation commands functional

#### Test Case: Auto-Scroll
1. **Open**: Onboarding page
2. **Click**: Various form elements
3. **Verify**: Auto-scroll behavior
4. **Test**: Smooth scrolling

**Expected Results**:
- Auto-scroll works on element clicks
- Smooth scrolling animation
- Content remains visible

### 4. Cross-User Communication

#### Test Case: Teacher-Parent Communication
1. **Login**: As teacher
2. **Send**: Message to parent
3. **Login**: As parent
4. **Verify**: Message received
5. **Reply**: To teacher

**Expected Results**:
- Messages sent successfully
- Real-time delivery
- Message history preserved
- Reply functionality works

#### Test Case: Student-Teacher Communication
1. **Login**: As student
2. **Send**: Question to teacher
3. **Login**: As teacher
4. **Verify**: Question received
5. **Reply**: With answer

**Expected Results**:
- Student can contact teachers
- Teachers receive student messages
- Communication flow works
- Message threading functional

### 5. Error Handling

#### Test Case: Invalid Login
1. **Navigate**: To login page
2. **Enter**: Invalid credentials
3. **Submit**: Form
4. **Verify**: Error message
5. **Test**: Error recovery

**Expected Results**:
- Clear error messages
- Form validation works
- User can retry login
- No system crashes

#### Test Case: Network Issues
1. **Disconnect**: Internet connection
2. **Attempt**: API calls
3. **Verify**: Error handling
4. **Reconnect**: Internet
5. **Test**: Recovery

**Expected Results**:
- Graceful error handling
- User-friendly messages
- Automatic retry mechanisms
- Data preservation

## Test Data Setup

### Database Seeding

```bash
# Create test data
cd FamEduConnect_Full_Codebase/backend
npm run seed:test
```

### Test Users

The following test users are automatically created:

- **Parent**: `<EMAIL>` / `password123`
- **Teacher**: `<EMAIL>` / `password123`
- **Student**: `<EMAIL>` / `password123`
- **Admin**: `<EMAIL>` / `password123`

### Test Classes and Messages

- Sample classes for each subject
- Test messages between users
- Sample assignments and grades

## Performance Testing

### Load Testing

```bash
# Install artillery
npm install -g artillery

# Run load test
artillery run load-test.yml
```

### Stress Testing

```bash
# Run stress test
artillery run stress-test.yml
```

## Security Testing

### Authentication Tests
- Token validation
- Role-based access control
- Session management
- Password security

### Input Validation Tests
- SQL injection prevention
- XSS protection
- CSRF protection
- File upload security

## Accessibility Testing

### Screen Reader Compatibility
- ARIA labels
- Keyboard navigation
- Focus management
- Color contrast

### Mobile Responsiveness
- Touch targets
- Responsive design
- Performance on mobile devices

## Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check database configuration
cat backend/.env

# Reset test database
npm run db:reset:test
```

#### Test Timeout Issues
```bash
# Increase timeout
npm test -- --timeout=10000
```

#### Coverage Issues
```bash
# Generate coverage report
npm run test:coverage

# Check coverage thresholds
cat jest.config.js
```

### Debug Mode

```bash
# Run tests in debug mode
DEBUG=* npm test

# Run specific test with debugging
DEBUG=* npm test -- --testNamePattern="should register user"
```

## Continuous Integration

### GitHub Actions

The application includes GitHub Actions workflows for:
- Automated testing on pull requests
- Coverage reporting
- Security scanning
- Deployment testing

### Local CI Simulation

```bash
# Run CI checks locally
npm run ci:check

# Run pre-commit hooks
npm run pre-commit
```

## Reporting

### Test Reports

Test results are generated in:
- `backend/coverage/` - Coverage reports
- `backend/test-results/` - Test output
- `frontend/coverage/` - Frontend coverage

### Coverage Thresholds

- **Statements**: 80%
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%

## Best Practices

1. **Write tests first** (TDD approach)
2. **Keep tests independent**
3. **Use descriptive test names**
4. **Mock external dependencies**
5. **Test error conditions**
6. **Maintain test data**
7. **Regular test maintenance**

## Support

For testing issues or questions:
1. Check the troubleshooting section
2. Review test logs
3. Consult the development team
4. Create an issue in the repository

---

**Last Updated**: December 2024
**Version**: 1.0.0 