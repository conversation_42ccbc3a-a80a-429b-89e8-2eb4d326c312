apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fameduconnect-backend-hpa
  namespace: fameduconnect
  labels:
    app: fameduconnect-backend
    version: v1.0.0
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fameduconnect-backend
  minReplicas: 3
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Object
    object:
      metric:
        name: requests-per-second
      describedObject:
        apiVersion: networking.k8s.io/v1
        kind: Ingress
        name: fameduconnect-ingress
      target:
        type: Value
        value: 1000
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 4
        periodSeconds: 15
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Min
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fameduconnect-frontend-hpa
  namespace: fameduconnect
  labels:
    app: fameduconnect-frontend
    version: v1.0.0
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fameduconnect-frontend
  minReplicas: 2
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fameduconnect-admin-hpa
  namespace: fameduconnect
  labels:
    app: fameduconnect-admin
    version: v1.0.0
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fameduconnect-admin
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 50
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 60 