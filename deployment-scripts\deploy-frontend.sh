#!/bin/bash
# FamEduConnect Frontend Deployment Script
# This script deploys the frontend application to Vercel

# Set error handling
set -e

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_DIR=$(pwd)
FRONTEND_DIR="$PROJECT_DIR/frontend"
ENV_CONFIG_DIR="$PROJECT_DIR/env-configs"
VERCEL_CONFIG="$ENV_CONFIG_DIR/vercel.json"
DEPLOYMENT_LOG="$PROJECT_DIR/deployment-logs/frontend-deployment-$(date +%Y%m%d-%H%M%S).log"

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/deployment-logs"

# Log function
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message" | tee -a "$DEPLOYMENT_LOG"
}

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
  log "ERROR: Vercel CLI not found. Please install it with: npm install -g vercel"
  exit 1
fi

# Check if user is logged in to Vercel
vercel whoami &> /dev/null || {
  log "You are not logged in to Vercel. Please login first."
  vercel login
}

# Check if frontend directory exists
if [ ! -d "$FRONTEND_DIR" ]; then
  log "ERROR: Frontend directory not found at $FRONTEND_DIR"
  exit 1
fi

# Check if Vercel config exists
if [ ! -f "$VERCEL_CONFIG" ]; then
  log "ERROR: Vercel configuration not found at $VERCEL_CONFIG"
  exit 1
fi

# Load environment variables
log "Loading environment variables for $ENVIRONMENT environment"
if [ -f "$ENV_CONFIG_DIR/frontend.env" ]; then
  source "$ENV_CONFIG_DIR/frontend.env"
  log "Environment variables loaded successfully"
else
  log "WARNING: Environment file not found at $ENV_CONFIG_DIR/frontend.env"
  log "Continuing with default environment variables"
fi

# Navigate to frontend directory
log "Changing to frontend directory: $FRONTEND_DIR"
cd "$FRONTEND_DIR"

# Install dependencies
log "Installing dependencies..."
npm ci

# Run tests
log "Running tests..."
npm test -- --passWithNoTests

# Build the application
log "Building the application..."
npm run build

# Copy Vercel configuration
log "Copying Vercel configuration..."
cp "$VERCEL_CONFIG" ./vercel.json

# Deploy to Vercel
log "Deploying to Vercel ($ENVIRONMENT environment)..."
if [ "$ENVIRONMENT" = "production" ]; then
  vercel --prod --yes
else
  vercel --yes
fi

# Check deployment status
if [ $? -eq 0 ]; then
  log "Deployment successful!"
  
  # Get deployment URL
  DEPLOYMENT_URL=$(vercel ls --json | jq -r '.[0].url')
  log "Deployment URL: https://$DEPLOYMENT_URL"
  
  # Run post-deployment checks
  log "Running post-deployment checks..."
  
  # Check if site is accessible
  HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$DEPLOYMENT_URL")
  if [ "$HTTP_STATUS" = "200" ]; then
    log "Site is accessible (HTTP 200)"
  else
    log "WARNING: Site returned HTTP status $HTTP_STATUS"
  fi
  
  # Check for critical resources
  curl -s "https://$DEPLOYMENT_URL" | grep -q "main.js" && log "Main JavaScript bundle found" || log "WARNING: Main JavaScript bundle not found"
  curl -s "https://$DEPLOYMENT_URL" | grep -q "main.css" && log "Main CSS bundle found" || log "WARNING: Main CSS bundle not found"
  
  log "Deployment process completed successfully!"
else
  log "ERROR: Deployment failed!"
  exit 1
fi

# Return to project directory
cd "$PROJECT_DIR"

# Update deployment status file
echo "{\"frontend\": {\"lastDeployed\": \"$(date +%Y-%m-%dT%H:%M:%S%z)\", \"environment\": \"$ENVIRONMENT\", \"url\": \"https://$DEPLOYMENT_URL\", \"status\": \"success\"}}" > "$PROJECT_DIR/deployment-status.json"

log "Frontend deployment script completed"
exit 0