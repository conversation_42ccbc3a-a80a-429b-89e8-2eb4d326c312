@echo off
echo Starting FamEduConnect...
echo.

REM Kill any existing processes on ports 3000 and 3002
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

cd backend
start "Backend" cmd /k "node simple-server.js"

cd ../frontend  
start "Frontend" cmd /k "npm start"

timeout /t 8 /nobreak >nul
start http://localhost:3000

echo Done! App should open in your browser.
echo If not, go to: http://localhost:3000
echo Backend API: http://localhost:3002/api
pause 