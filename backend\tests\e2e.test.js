const request = require('supertest');
const { app } = require('../server');
const { sequelize } = require('../models');

describe('End-to-End Application Tests', () => {
  let parentToken, teacherToken, studentToken, adminToken;
  let parentUser, teacherUser, studentUser, adminUser;

  beforeAll(async () => {
    await sequelize.sync({ force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('Complete User Journey Tests', () => {
    test('Complete parent user journey', async () => {
      // 1. Register parent user
      const parentData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Parent',
        role: 'parent'
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(parentData)
        .expect(201);

      parentToken = registerResponse.body.token;
      parentUser = registerResponse.body.user;

      // 2. Update profile with onboarding data
      const profileData = {
        firstName: 'John',
        lastName: 'Parent',
        phone: '1234567890',
        role: 'parent',
        hasCompletedOnboarding: true,
        preferences: {
          notifications: true,
          emailUpdates: true,
          smsUpdates: false
        }
      };

      await request(app)
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${parentToken}`)
        .send(profileData)
        .expect(200);

      // 3. Access dashboard
      const dashboardResponse = await request(app)
        .get('/api/dashboard')
        .set('Authorization', `Bearer ${parentToken}`)
        .expect(200);

      expect(dashboardResponse.body).toHaveProperty('stats');
      expect(dashboardResponse.body).toHaveProperty('recentMessages');
    });

    test('Complete teacher user journey', async () => {
      // 1. Register teacher user
      const teacherData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Teacher',
        role: 'teacher'
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(teacherData)
        .expect(201);

      teacherToken = registerResponse.body.token;
      teacherUser = registerResponse.body.user;

      // 2. Create a class
      const classData = {
        name: 'Advanced Mathematics',
        description: 'Advanced math course for 11th grade',
        subject: 'Mathematics',
        grade: '11th Grade',
        teacherId: teacherUser.id
      };

      const classResponse = await request(app)
        .post('/api/classes')
        .set('Authorization', `Bearer ${teacherToken}`)
        .send(classData)
        .expect(201);

      expect(classResponse.body).toHaveProperty('id');
      expect(classResponse.body.name).toBe(classData.name);

      // 3. Send message to parent
      const messageData = {
        recipientId: parentUser.id,
        content: 'Welcome to the new school year!',
        type: 'text'
      };

      await request(app)
        .post('/api/messages')
        .set('Authorization', `Bearer ${teacherToken}`)
        .send(messageData)
        .expect(201);
    });

    test('Complete student user journey', async () => {
      // 1. Register student user
      const studentData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Alex',
        lastName: 'Student',
        role: 'student'
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(studentData)
        .expect(201);

      studentToken = registerResponse.body.token;
      studentUser = registerResponse.body.user;

      // 2. View assignments
      const assignmentsResponse = await request(app)
        .get('/api/assignments')
        .set('Authorization', `Bearer ${studentToken}`)
        .expect(200);

      expect(Array.isArray(assignmentsResponse.body)).toBe(true);
    });

    test('Complete admin user journey', async () => {
      // 1. Register admin user
      const adminData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(adminData)
        .expect(201);

      adminToken = registerResponse.body.token;
      adminUser = registerResponse.body.user;

      // 2. Access admin dashboard
      const adminDashboardResponse = await request(app)
        .get('/api/admin/dashboard')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(adminDashboardResponse.body).toHaveProperty('stats');
      expect(adminDashboardResponse.body).toHaveProperty('users');

      // 3. View all users
      const usersResponse = await request(app)
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(Array.isArray(usersResponse.body)).toBe(true);
      expect(usersResponse.body.length).toBeGreaterThan(0);
    });
  });

  describe('Cross-User Communication Tests', () => {
    test('Teacher can communicate with parent', async () => {
      const messageData = {
        recipientId: parentUser.id,
        content: 'Your child is doing great in class!',
        type: 'text'
      };

      const response = await request(app)
        .post('/api/messages')
        .set('Authorization', `Bearer ${teacherToken}`)
        .send(messageData)
        .expect(201);

      expect(response.body.content).toBe(messageData.content);
    });

    test('Parent can view messages from teacher', async () => {
      const response = await request(app)
        .get('/api/messages')
        .set('Authorization', `Bearer ${parentToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });
  });

  describe('Role-Based Access Control Tests', () => {
    test('Parent cannot access admin endpoints', async () => {
      await request(app)
        .get('/api/admin/dashboard')
        .set('Authorization', `Bearer ${parentToken}`)
        .expect(403);
    });

    test('Student cannot create classes', async () => {
      const classData = {
        name: 'Test Class',
        description: 'Test description',
        subject: 'Test Subject',
        grade: '10th Grade'
      };

      await request(app)
        .post('/api/classes')
        .set('Authorization', `Bearer ${studentToken}`)
        .send(classData)
        .expect(403);
    });

    test('Teacher can access teacher-specific endpoints', async () => {
      const response = await request(app)
        .get('/api/classes/my-classes')
        .set('Authorization', `Bearer ${teacherToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('Data Persistence Tests', () => {
    test('User data persists across sessions', async () => {
      // Login with existing user
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200);

      const newToken = loginResponse.body.token;

      // Verify profile data is still there
      const profileResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${newToken}`)
        .expect(200);

      expect(profileResponse.body.firstName).toBe('John');
      expect(profileResponse.body.role).toBe('parent');
    });

    test('Messages persist and are retrievable', async () => {
      // Send a new message
      const messageData = {
        recipientId: parentUser.id,
        content: 'Persistent test message',
        type: 'text'
      };

      await request(app)
        .post('/api/messages')
        .set('Authorization', `Bearer ${teacherToken}`)
        .send(messageData)
        .expect(201);

      // Retrieve messages and verify persistence
      const messagesResponse = await request(app)
        .get('/api/messages')
        .set('Authorization', `Bearer ${parentToken}`)
        .expect(200);

      const hasPersistentMessage = messagesResponse.body.some(
        msg => msg.content === 'Persistent test message'
      );
      expect(hasPersistentMessage).toBe(true);
    });
  });

  describe('Error Recovery Tests', () => {
    test('Application handles invalid tokens gracefully', async () => {
      await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    test('Application handles malformed requests gracefully', async () => {
      await request(app)
        .post('/api/auth/login')
        .send({ invalid: 'data' })
        .expect(400);
    });

    test('Application handles database connection issues gracefully', async () => {
      // This test would require mocking database failures
      // For now, we'll test that the app responds to valid requests
      const response = await request(app)
        .get('/api/test')
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Backend is working!');
    });
  });
}); 