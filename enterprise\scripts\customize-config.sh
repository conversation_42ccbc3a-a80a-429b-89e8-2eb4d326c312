#!/bin/bash

# FamEduConnect Enterprise Configuration Customization Script
# This script helps customize the YAML configuration files for your specific environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
    fi
    
    eval "$var_name='$input'"
}

# Function to generate secure password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Function to base64 encode
base64_encode() {
    echo -n "$1" | base64
}

# Function to update YAML file
update_yaml() {
    local file="$1"
    local search="$2"
    local replace="$3"
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s|$search|$replace|g" "$file"
    else
        # Linux
        sed -i "s|$search|$replace|g" "$file"
    fi
}

# Function to create secrets file
create_secrets_file() {
    local file="$1"
    cat > "$file" << EOF
# FamEduConnect Enterprise Secrets
# Generated on: $(date)
# WARNING: Keep this file secure and never commit to version control

# Database Secrets
POSTGRES_PASSWORD="$POSTGRES_PASSWORD"
POSTGRES_REPLICATION_PASSWORD="$POSTGRES_REPLICATION_PASSWORD"
POSTGRES_BACKUP_PASSWORD="$POSTGRES_BACKUP_PASSWORD"

# Base64 Encoded Secrets (for Kubernetes)
BASE64_ENCODED_POSTGRES_PASSWORD="$(base64_encode "$POSTGRES_PASSWORD")"
BASE64_ENCODED_REPLICATION_PASSWORD="$(base64_encode "$POSTGRES_REPLICATION_PASSWORD")"
BASE64_ENCODED_BACKUP_PASSWORD="$(base64_encode "$POSTGRES_BACKUP_PASSWORD")"

# JWT Secret
JWT_SECRET="$JWT_SECRET"
BASE64_ENCODED_JWT_SECRET="$(base64_encode "$JWT_SECRET")"

# Monitoring Secrets
ELASTICSEARCH_USERNAME="$ELASTICSEARCH_USERNAME"
ELASTICSEARCH_PASSWORD="$ELASTICSEARCH_PASSWORD"
BASE64_ENCODED_ELASTICSEARCH_USERNAME="$(base64_encode "$ELASTICSEARCH_USERNAME")"
BASE64_ENCODED_ELASTICSEARCH_PASSWORD="$(base64_encode "$ELASTICSEARCH_PASSWORD")"

# SSO Configuration (if using SAML/OIDC)
SAML_ENTITY_ID="$SAML_ENTITY_ID"
SAML_ACS_URL="$SAML_ACS_URL"
SAML_IDP_SSO_URL="$SAML_IDP_SSO_URL"
SAML_IDP_CERT="$SAML_IDP_CERT"

# Backup Storage
S3_BUCKET="$S3_BUCKET"
S3_ACCESS_KEY="$S3_ACCESS_KEY"
S3_SECRET_KEY="$S3_SECRET_KEY"
BASE64_ENCODED_S3_ACCESS_KEY="$(base64_encode "$S3_ACCESS_KEY")"
BASE64_ENCODED_S3_SECRET_KEY="$(base64_encode "$S3_SECRET_KEY")"
EOF
}

# Main script
main() {
    print_header "FamEduConnect Enterprise Configuration Customization"
    
    # Check if running from the correct directory
    if [ ! -f "enterprise/ENTERPRISE_DEPLOYMENT_GUIDE.md" ]; then
        print_error "Please run this script from the FamEduConnect_Full_Codebase directory"
        exit 1
    fi
    
    print_status "Starting configuration customization..."
    
    # Create configuration directory
    mkdir -p enterprise/config-customized
    
    # Step 1: Domain Configuration
    print_header "Step 1: Domain Configuration"
    
    prompt_with_default "Enter your primary domain (e.g., yourdomain.com)" "yourdomain.com" DOMAIN
    prompt_with_default "Enter your app subdomain" "app" APP_SUBDOMAIN
    prompt_with_default "Enter your admin subdomain" "admin" ADMIN_SUBDOMAIN
    prompt_with_default "Enter your API subdomain" "api" API_SUBDOMAIN
    
    APP_DOMAIN="${APP_SUBDOMAIN}.${DOMAIN}"
    ADMIN_DOMAIN="${ADMIN_SUBDOMAIN}.${DOMAIN}"
    API_DOMAIN="${API_SUBDOMAIN}.${DOMAIN}"
    
    print_status "Configured domains:"
    echo "  App: $APP_DOMAIN"
    echo "  Admin: $ADMIN_DOMAIN"
    echo "  API: $API_DOMAIN"
    
    # Step 2: Database Configuration
    print_header "Step 2: Database Configuration"
    
    prompt_with_default "Enter database name" "fameduconnect_prod" DB_NAME
    prompt_with_default "Enter database user" "fameduconnect" DB_USER
    
    # Generate secure passwords
    POSTGRES_PASSWORD=$(generate_password)
    POSTGRES_REPLICATION_PASSWORD=$(generate_password)
    POSTGRES_BACKUP_PASSWORD=$(generate_password)
    
    print_status "Generated secure database passwords"
    
    # Step 3: Storage Configuration
    print_header "Step 3: Storage Configuration"
    
    prompt_with_default "Enter storage class name (e.g., gp3-encrypted for AWS)" "gp3-encrypted" STORAGE_CLASS
    prompt_with_default "Enter primary database storage size (e.g., 100Gi)" "100Gi" PRIMARY_STORAGE
    prompt_with_default "Enter replica database storage size (e.g., 50Gi)" "50Gi" REPLICA_STORAGE
    
    # Step 4: Resource Configuration
    print_header "Step 4: Resource Configuration"
    
    prompt_with_default "Enter backend min replicas" "3" BACKEND_MIN_REPLICAS
    prompt_with_default "Enter backend max replicas" "50" BACKEND_MAX_REPLICAS
    prompt_with_default "Enter frontend min replicas" "2" FRONTEND_MIN_REPLICAS
    prompt_with_default "Enter frontend max replicas" "20" FRONTEND_MAX_REPLICAS
    prompt_with_default "Enter admin min replicas" "2" ADMIN_MIN_REPLICAS
    prompt_with_default "Enter admin max replicas" "10" ADMIN_MAX_REPLICAS
    
    # Step 5: Monitoring Configuration
    print_header "Step 5: Monitoring Configuration"
    
    prompt_with_default "Enter Elasticsearch username" "elastic" ELASTICSEARCH_USERNAME
    ELASTICSEARCH_PASSWORD=$(generate_password)
    
    print_status "Generated Elasticsearch password"
    
    # Step 6: Backup Configuration
    print_header "Step 6: Backup Configuration"
    
    prompt_with_default "Enter S3 bucket name for backups" "fameduconnect-backups" S3_BUCKET
    prompt_with_default "Enter S3 access key" "" S3_ACCESS_KEY
    prompt_with_default "Enter S3 secret key" "" S3_SECRET_KEY
    
    # Step 7: Security Configuration
    print_header "Step 7: Security Configuration"
    
    JWT_SECRET=$(generate_password)
    print_status "Generated JWT secret"
    
    # Step 8: SSO Configuration (Optional)
    print_header "Step 8: SSO Configuration (Optional)"
    
    read -p "Do you want to configure SSO (SAML/OIDC)? (y/N): " configure_sso
    if [[ $configure_sso =~ ^[Yy]$ ]]; then
        prompt_with_default "Enter SAML Entity ID" "https://$API_DOMAIN/saml/metadata" SAML_ENTITY_ID
        prompt_with_default "Enter SAML ACS URL" "https://$API_DOMAIN/saml/acs" SAML_ACS_URL
        prompt_with_default "Enter SAML IdP SSO URL" "" SAML_IDP_SSO_URL
        prompt_with_default "Enter SAML IdP Certificate (base64)" "" SAML_IDP_CERT
    else
        SAML_ENTITY_ID=""
        SAML_ACS_URL=""
        SAML_IDP_SSO_URL=""
        SAML_IDP_CERT=""
    fi
    
    # Step 9: Generate Configuration Files
    print_header "Step 9: Generating Configuration Files"
    
    # Create secrets file
    create_secrets_file "enterprise/config-customized/secrets.env"
    print_status "Created secrets file: enterprise/config-customized/secrets.env"
    
    # Copy and customize YAML files
    print_status "Customizing YAML configuration files..."
    
    # Copy files to customized directory
    cp -r enterprise/k8s enterprise/config-customized/
    cp -r enterprise/database enterprise/config-customized/
    cp -r enterprise/istio enterprise/config-customized/
    cp -r enterprise/monitoring enterprise/config-customized/
    cp -r enterprise/auth enterprise/config-customized/
    cp -r enterprise/dr enterprise/config-customized/
    
    # Update domain names in Istio config
    update_yaml "enterprise/config-customized/istio/istio-config.yaml" "app.fameduconnect.xyz" "$APP_DOMAIN"
    update_yaml "enterprise/config-customized/istio/istio-config.yaml" "admin.fameduconnect.xyz" "$ADMIN_DOMAIN"
    update_yaml "enterprise/config-customized/istio/istio-config.yaml" "api.fameduconnect.xyz" "$API_DOMAIN"
    
    # Update database configuration
    update_yaml "enterprise/config-customized/database/postgres-ha.yaml" "fameduconnect_prod" "$DB_NAME"
    update_yaml "enterprise/config-customized/database/postgres-ha.yaml" "fameduconnect" "$DB_USER"
    update_yaml "enterprise/config-customized/database/postgres-ha.yaml" "gp3-encrypted" "$STORAGE_CLASS"
    update_yaml "enterprise/config-customized/database/postgres-ha.yaml" "100Gi" "$PRIMARY_STORAGE"
    update_yaml "enterprise/config-customized/database/postgres-ha.yaml" "50Gi" "$REPLICA_STORAGE"
    
    # Update HPA configuration
    update_yaml "enterprise/config-customized/k8s/hpa.yaml" "minReplicas: 3" "minReplicas: $BACKEND_MIN_REPLICAS"
    update_yaml "enterprise/config-customized/k8s/hpa.yaml" "maxReplicas: 50" "maxReplicas: $BACKEND_MAX_REPLICAS"
    update_yaml "enterprise/config-customized/k8s/hpa.yaml" "minReplicas: 2" "minReplicas: $FRONTEND_MIN_REPLICAS"
    update_yaml "enterprise/config-customized/k8s/hpa.yaml" "maxReplicas: 20" "maxReplicas: $FRONTEND_MAX_REPLICAS"
    update_yaml "enterprise/config-customized/k8s/hpa.yaml" "minReplicas: 2" "minReplicas: $ADMIN_MIN_REPLICAS"
    update_yaml "enterprise/config-customized/k8s/hpa.yaml" "maxReplicas: 10" "maxReplicas: $ADMIN_MAX_REPLICAS"
    
    # Create deployment script
    cat > "enterprise/config-customized/deploy.sh" << EOF
#!/bin/bash

# FamEduConnect Enterprise Deployment Script
# Generated on: $(date)

set -e

# Load secrets
source ./secrets.env

# Create namespace
kubectl create namespace fameduconnect --dry-run=client -o yaml | kubectl apply -f -

# Apply database configuration
kubectl apply -f database/

# Apply Istio configuration
kubectl apply -f istio/

# Apply monitoring configuration
kubectl apply -f monitoring/

# Apply authentication configuration
kubectl apply -f auth/

# Apply disaster recovery configuration
kubectl apply -f dr/

# Apply HPA configuration
kubectl apply -f k8s/

echo "Deployment completed successfully!"
echo "Access your application at:"
echo "  App: https://$APP_DOMAIN"
echo "  Admin: https://$ADMIN_DOMAIN"
echo "  API: https://$API_DOMAIN"
EOF
    
    chmod +x "enterprise/config-customized/deploy.sh"
    
    # Create environment variables file
    cat > "enterprise/config-customized/environment.env" << EOF
# FamEduConnect Environment Variables
# Generated on: $(date)

# Domain Configuration
DOMAIN=$DOMAIN
APP_DOMAIN=$APP_DOMAIN
ADMIN_DOMAIN=$ADMIN_DOMAIN
API_DOMAIN=$API_DOMAIN

# Database Configuration
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DATABASE_URL=**************************************************************/$DB_NAME

# Storage Configuration
STORAGE_CLASS=$STORAGE_CLASS
PRIMARY_STORAGE=$PRIMARY_STORAGE
REPLICA_STORAGE=$REPLICA_STORAGE

# Resource Configuration
BACKEND_MIN_REPLICAS=$BACKEND_MIN_REPLICAS
BACKEND_MAX_REPLICAS=$BACKEND_MAX_REPLICAS
FRONTEND_MIN_REPLICAS=$FRONTEND_MIN_REPLICAS
FRONTEND_MAX_REPLICAS=$FRONTEND_MAX_REPLICAS
ADMIN_MIN_REPLICAS=$ADMIN_MIN_REPLICAS
ADMIN_MAX_REPLICAS=$ADMIN_MAX_REPLICAS

# Monitoring Configuration
ELASTICSEARCH_USERNAME=$ELASTICSEARCH_USERNAME
ELASTICSEARCH_PASSWORD=$ELASTICSEARCH_PASSWORD

# Backup Configuration
S3_BUCKET=$S3_BUCKET
S3_ACCESS_KEY=$S3_ACCESS_KEY
S3_SECRET_KEY=$S3_SECRET_KEY

# Security Configuration
JWT_SECRET=$JWT_SECRET

# SSO Configuration
SAML_ENTITY_ID=$SAML_ENTITY_ID
SAML_ACS_URL=$SAML_ACS_URL
SAML_IDP_SSO_URL=$SAML_IDP_SSO_URL
SAML_IDP_CERT=$SAML_IDP_CERT
EOF
    
    # Step 10: Summary
    print_header "Configuration Customization Complete!"
    
    print_status "Generated files:"
    echo "  - enterprise/config-customized/secrets.env (SECURE - contains passwords)"
    echo "  - enterprise/config-customized/environment.env (environment variables)"
    echo "  - enterprise/config-customized/deploy.sh (deployment script)"
    echo "  - enterprise/config-customized/ (customized YAML files)"
    
    print_warning "IMPORTANT:"
    echo "  1. Review all generated files before deployment"
    echo "  2. Keep secrets.env secure and never commit to version control"
    echo "  3. Update DNS records for your domains"
    echo "  4. Obtain SSL certificates for your domains"
    echo "  5. Configure your cloud provider storage classes"
    
    print_status "Next steps:"
    echo "  1. Review the CONFIGURATION_REVIEW_GUIDE.md"
    echo "  2. Set up your Kubernetes cluster"
    echo "  3. Install Istio"
    echo "  4. Run: cd enterprise/config-customized && ./deploy.sh"
    
    print_status "Configuration customization completed successfully!"
}

# Run main function
main "$@" 