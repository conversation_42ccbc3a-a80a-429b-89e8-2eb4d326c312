// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  password          String
  firstName         String
  lastName          String
  role              UserRole  @default(PARENT)
  phone             String?
  preferredLanguage String    @default("en")
  profilePicture    String?
  isActive          Boolean   @default(true)
  isLocked          Boolean   @default(false)
  failedLoginAttempts Int     @default(0)
  lastLoginAt       DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  sentMessages      Message[] @relation("MessageSender")
  receivedMessages  Message[] @relation("MessageRecipient")
  students          Student[]
  classes           Class[]
  videoCalls        VideoCall[]
  notifications     Notification[]

  @@map("users")
}

model Student {
  id          String   @id @default(cuid())
  firstName   String
  lastName    String
  dateOfBirth DateTime
  grade       String
  school      String?
  parentId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent      User     @relation(fields: [parentId], references: [id])
  classes     Class[]
  attendance  Attendance[]
  performance Performance[]

  @@map("students")
}

model Class {
  id          String   @id @default(cuid())
  name        String
  description String?
  grade       String
  teacherId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  teacher     User     @relation(fields: [teacherId], references: [id])
  students    Student[]
  messages    Message[]
  attendance  Attendance[]
  videoCalls  VideoCall[]

  @@map("classes")
}

model Message {
  id          String      @id @default(cuid())
  content     String
  messageType MessageType @default(TEXT)
  senderId    String
  recipientId String?
  classId     String?
  isRead      Boolean     @default(false)
  isSent      Boolean     @default(false)
  sentAt      DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  sender      User        @relation("MessageSender", fields: [senderId], references: [id])
  recipient   User?       @relation("MessageRecipient", fields: [recipientId], references: [id])
  class       Class?      @relation(fields: [classId], references: [id])
  translations Translation[]

  @@map("messages")
}

model VideoCall {
  id          String   @id @default(cuid())
  title       String
  description String?
  hostId      String
  classId     String?
  startTime   DateTime
  endTime     DateTime?
  isActive    Boolean  @default(false)
  recordingUrl String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  host        User     @relation(fields: [hostId], references: [id])
  class       Class?   @relation(fields: [classId], references: [id])

  @@map("video_calls")
}

model Translation {
  id          String   @id @default(cuid())
  messageId   String
  targetLanguage String
  translatedContent String
  createdAt   DateTime @default(now())

  // Relations
  message     Message  @relation(fields: [messageId], references: [id])

  @@map("translations")
}

model Attendance {
  id        String   @id @default(cuid())
  studentId String
  classId   String
  date      DateTime
  status    AttendanceStatus
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  student   Student  @relation(fields: [studentId], references: [id])
  class     Class    @relation(fields: [classId], references: [id])

  @@unique([studentId, classId, date])
  @@map("attendance")
}

model Performance {
  id          String   @id @default(cuid())
  studentId   String
  subject     String
  grade       String
  score       Float?
  maxScore    Float?
  comments    String?
  date        DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  student     Student  @relation(fields: [studentId], references: [id])

  @@map("performance")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  message   String
  type      NotificationType
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relations
  user      User             @relation(fields: [userId], references: [id])

  @@map("notifications")
}

enum UserRole {
  ADMIN
  TEACHER
  PARENT
  STUDENT
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  VOICE
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum NotificationType {
  MESSAGE
  CALL
  ASSIGNMENT
  GRADE
  ATTENDANCE
  SYSTEM
} 