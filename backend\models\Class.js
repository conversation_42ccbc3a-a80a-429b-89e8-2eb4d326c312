module.exports = (sequelize, DataTypes) => {
  const Class = sequelize.define('Class', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    className: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    classCode: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 20]
      }
    },
    grade: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isIn: [['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']]
      }
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [1, 50]
      }
    },
    teacherId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    schoolYear: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        is: /^\d{4}-\d{4}$/
      }
    },
    semester: {
      type: DataTypes.ENUM('Fall', 'Spring', 'Summer', 'Full Year'),
      defaultValue: 'Full Year'
    },
    maxStudents: {
      type: DataTypes.INTEGER,
      defaultValue: 30,
      validate: {
        min: 1,
        max: 50
      }
    },
    currentEnrollment: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    schedule: {
      type: DataTypes.JSON,
      defaultValue: {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: []
      }
    },
    classroom: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    syllabus: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    resources: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    announcements: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    parentCommunicationSettings: {
      type: DataTypes.JSON,
      defaultValue: {
        weeklyUpdates: true,
        attendanceAlerts: true,
        performanceReports: true,
        behaviorNotifications: true
      }
    }
  }, {
    indexes: [
      { fields: ['classCode'] },
      { fields: ['teacherId'] },
      { fields: ['grade'] },
      { fields: ['schoolYear'] },
      { fields: ['isActive'] }
    ]
  });

  Class.associate = function(models) {
    Class.belongsTo(models.User, {
      foreignKey: 'teacherId',
      as: 'teacher'
    });
    Class.hasMany(models.Student, {
      foreignKey: 'classId',
      as: 'students'
    });
    Class.hasMany(models.Message, {
      foreignKey: 'classId',
      as: 'messages'
    });
    Class.hasMany(models.VideoCall, {
      foreignKey: 'classId',
      as: 'videoCalls'
    });
    Class.hasMany(models.Attendance, {
      foreignKey: 'classId',
      as: 'attendanceRecords'
    });
  };

  return Class;
};