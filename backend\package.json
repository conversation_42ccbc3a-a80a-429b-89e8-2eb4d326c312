{"name": "fameduconnect-backend", "version": "1.0.0", "description": "FamEduConnect Backend API", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "test": "jest", "test:integration": "jest --testPathPattern=integration.test.js", "test:e2e": "jest --testPathPattern=e2e.test.js", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "dependencies": {"@google-cloud/translate": "^8.0.2", "bcryptjs": "^2.4.3", "compression": "^1.8.1", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.11.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "pg": "^8.11.3", "sequelize": "^6.32.1", "simple-peer": "^9.11.1", "socket.io": "^4.7.2", "sqlite3": "^5.1.7", "twilio": "^4.14.0", "uuid": "^9.0.0", "ws": "^8.13.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.4"}}