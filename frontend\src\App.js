import React from 'react';
import { Provider } from 'react-redux';
import { Toaster } from 'react-hot-toast';
import { store } from './store/store';
import ErrorBoundary from './components/UI/ErrorBoundary';
import AppContent from './components/Layout/AppContent';
import ConnectionTest from './components/UI/ConnectionTest';

function App() {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <AppContent />
        <ConnectionTest />
        <Toaster position="top-right" />
      </Provider>
    </ErrorBoundary>
  );
}

export default App;