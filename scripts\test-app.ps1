# FamEduConnect Application Testing Script for Windows PowerShell
# This script sets up and runs comprehensive tests for the entire application

param(
    [switch]$SkipInstall,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Function to print colored output
function Write-Status {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-Host "SUCCESS: $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "WARNING: $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "ERROR: $Message" -ForegroundColor $Red
}

Write-Status "Starting FamEduConnect Application Testing" $Blue
Write-Status "==============================================" $Blue

# Check if Node.js is installed
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Node.js is installed ($nodeVersion)"
    } else {
        Write-Error "Node.js is not installed. Please install Node.js first."
        exit 1
    }
} catch {
    Write-Error "Node.js is not installed. Please install Node.js first."
    exit 1
}

# Check if npm is installed
try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "npm is installed ($npmVersion)"
    } else {
        Write-Error "npm is not installed. Please install npm first."
        exit 1
    }
} catch {
    Write-Error "npm is not installed. Please install npm first."
    exit 1
}

# Get the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptDir
$backendDir = Join-Path $projectRoot "FamEduConnect_Full_Codebase\backend"
$frontendDir = Join-Path $projectRoot "FamEduConnect_Full_Codebase\frontend"

# Setup backend
Write-Status "Setting up backend dependencies..." $Blue
Set-Location $backendDir
if (-not $SkipInstall -and -not (Test-Path "node_modules")) {
    try {
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to install backend dependencies"
            exit 1
        }
        Write-Success "Backend dependencies installed"
    } catch {
        Write-Error "Failed to install backend dependencies"
        exit 1
    }
} else {
    Write-Success "Backend dependencies already installed or skipped"
}

# Setup frontend
Write-Status "Setting up frontend dependencies..." $Blue
Set-Location $frontendDir
if (-not $SkipInstall -and -not (Test-Path "node_modules")) {
    try {
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to install frontend dependencies"
            exit 1
        }
        Write-Success "Frontend dependencies installed"
    } catch {
        Write-Error "Failed to install frontend dependencies"
        exit 1
    }
} else {
    Write-Success "Frontend dependencies already installed or skipped"
}

# Run backend tests
Write-Status "Running backend tests..." $Blue
Set-Location $backendDir
try {
    npm test
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Backend tests failed"
        exit 1
    }
    Write-Success "Backend tests passed"
} catch {
    Write-Error "Backend tests failed"
    exit 1
}

# Run frontend tests
Write-Status "Running frontend tests..." $Blue
Set-Location $frontendDir
try {
    npm test -- --watchAll=false
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Frontend tests failed"
        exit 1
    }
    Write-Success "Frontend tests passed"
} catch {
    Write-Error "Frontend tests failed"
    exit 1
}

# Start backend server for integration tests
Write-Status "Starting backend server for integration tests..." $Blue
Set-Location $backendDir
$serverJob = Start-Job -ScriptBlock {
    param($backendDir)
    Set-Location $backendDir
    npm start
} -ArgumentList $backendDir

# Wait for server to start
Start-Sleep -Seconds 5

# Run integration tests
Write-Status "Running integration tests..." $Blue
try {
    npm run test:integration
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Integration tests failed"
        Stop-Job $serverJob -ErrorAction SilentlyContinue
        Remove-Job $serverJob -ErrorAction SilentlyContinue
        exit 1
    }
    Write-Success "Integration tests passed"
} catch {
    Write-Error "Integration tests failed"
    Stop-Job $serverJob -ErrorAction SilentlyContinue
    Remove-Job $serverJob -ErrorAction SilentlyContinue
    exit 1
}

# Run end-to-end tests
Write-Status "Running end-to-end tests..." $Blue
try {
    npm run test:e2e
    if ($LASTEXITCODE -ne 0) {
        Write-Error "End-to-end tests failed"
        Stop-Job $serverJob -ErrorAction SilentlyContinue
        Remove-Job $serverJob -ErrorAction SilentlyContinue
        exit 1
    }
    Write-Success "End-to-end tests passed"
} catch {
    Write-Error "End-to-end tests failed"
    Stop-Job $serverJob -ErrorAction SilentlyContinue
    Remove-Job $serverJob -ErrorAction SilentlyContinue
    exit 1
}

# Stop backend server
Write-Status "Stopping backend server..." $Blue
Stop-Job $serverJob -ErrorAction SilentlyContinue
Remove-Job $serverJob -ErrorAction SilentlyContinue

# Generate test report
Write-Status "Generating test report..." $Blue
Write-Status "==============================================" $Blue
Write-Success "Testing completed successfully!"
Write-Host ""
Write-Status "Test Summary:" $Blue
Write-Status "- Backend tests: PASSED" $Green
Write-Status "- Frontend tests: PASSED" $Green
Write-Status "- Integration tests: PASSED" $Green
Write-Status "- End-to-end tests: PASSED" $Green
Write-Host ""
Write-Status "Your application is ready for use!" $Green
Write-Status "==============================================" $Blue

# Return to original directory
Set-Location $scriptDir 