import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../config/constants';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

class NotificationService {
  constructor() {
    this.expoPushToken = null;
    this.notificationListener = null;
    this.responseListener = null;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return false;
      }

      // Get push token
      if (Device.isDevice) {
        this.expoPushToken = await Notifications.getExpoPushTokenAsync({
          projectId: Constants.expoConfig.extra.eas.projectId,
        });
        
        // Store token locally
        await AsyncStorage.setItem('expoPushToken', this.expoPushToken.data);
        
        // Register token with backend
        await this.registerToken(this.expoPushToken.data);
      } else {
        console.log('Must use physical device for Push Notifications');
      }

      // Set up notification listeners
      this.setupNotificationListeners();

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return false;
    }
  }

  setupNotificationListeners() {
    // Handle notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
      this.handleNotificationReceived(notification);
    });

    // Handle notification responses (when user taps notification)
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      this.handleNotificationResponse(response);
    });
  }

  async registerToken(token) {
    try {
      const userToken = await AsyncStorage.getItem('userToken');
      
      const response = await fetch(`${API_BASE_URL}/api/notifications/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`,
        },
        body: JSON.stringify({
          token: token,
          platform: Platform.OS,
          deviceId: Device.osInternalBuildId,
          appVersion: Constants.expoConfig.version,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to register push token');
      }

      console.log('Push token registered successfully');
    } catch (error) {
      console.error('Error registering push token:', error);
    }
  }

  async unregisterToken() {
    try {
      const userToken = await AsyncStorage.getItem('userToken');
      const token = await AsyncStorage.getItem('expoPushToken');
      
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/api/notifications/unregister`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`,
        },
        body: JSON.stringify({
          token: token,
        }),
      });

      if (response.ok) {
        await AsyncStorage.removeItem('expoPushToken');
        console.log('Push token unregistered successfully');
      }
    } catch (error) {
      console.error('Error unregistering push token:', error);
    }
  }

  async scheduleLocalNotification(title, body, data = {}, trigger = null) {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: title,
          body: body,
          data: data,
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: trigger || null, // null means show immediately
      });

      console.log('Local notification scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling local notification:', error);
      throw error;
    }
  }

  async cancelNotification(notificationId) {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      console.log('Notification cancelled:', notificationId);
    } catch (error) {
      console.error('Error cancelling notification:', error);
    }
  }

  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('All notifications cancelled');
    } catch (error) {
      console.error('Error cancelling all notifications:', error);
    }
  }

  async getBadgeCount() {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('Error getting badge count:', error);
      return 0;
    }
  }

  async setBadgeCount(count) {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }

  async clearBadge() {
    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.error('Error clearing badge:', error);
    }
  }

  // Handle different types of notifications
  handleNotificationReceived(notification) {
    const { title, body, data } = notification.request.content;
    
    // Handle different notification types
    switch (data?.type) {
      case 'message':
        this.handleMessageNotification(data);
        break;
      case 'call':
        this.handleCallNotification(data);
        break;
      case 'assignment':
        this.handleAssignmentNotification(data);
        break;
      case 'announcement':
        this.handleAnnouncementNotification(data);
        break;
      case 'reminder':
        this.handleReminderNotification(data);
        break;
      default:
        console.log('Unknown notification type:', data?.type);
    }
  }

  handleNotificationResponse(response) {
    const { data } = response.notification.request.content;
    
    // Navigate based on notification type
    switch (data?.type) {
      case 'message':
        this.navigateToChat(data.chatId);
        break;
      case 'call':
        this.navigateToCall(data.callId);
        break;
      case 'assignment':
        this.navigateToAssignment(data.assignmentId);
        break;
      case 'announcement':
        this.navigateToAnnouncement(data.announcementId);
        break;
      case 'reminder':
        this.navigateToReminder(data.reminderId);
        break;
      default:
        console.log('Unknown notification response type:', data?.type);
    }
  }

  // Specific notification handlers
  handleMessageNotification(data) {
    // Update message count, play sound, etc.
    console.log('Message notification:', data);
  }

  handleCallNotification(data) {
    // Handle incoming call notification
    console.log('Call notification:', data);
  }

  handleAssignmentNotification(data) {
    // Handle new assignment notification
    console.log('Assignment notification:', data);
  }

  handleAnnouncementNotification(data) {
    // Handle new announcement notification
    console.log('Announcement notification:', data);
  }

  handleReminderNotification(data) {
    // Handle reminder notification
    console.log('Reminder notification:', data);
  }

  // Navigation methods (to be implemented by the app)
  navigateToChat(chatId) {
    // Navigate to specific chat
    console.log('Navigate to chat:', chatId);
  }

  navigateToCall(callId) {
    // Navigate to call screen
    console.log('Navigate to call:', callId);
  }

  navigateToAssignment(assignmentId) {
    // Navigate to assignment
    console.log('Navigate to assignment:', assignmentId);
  }

  navigateToAnnouncement(announcementId) {
    // Navigate to announcement
    console.log('Navigate to announcement:', announcementId);
  }

  navigateToReminder(reminderId) {
    // Navigate to reminder
    console.log('Navigate to reminder:', reminderId);
  }

  // Utility methods
  async scheduleReminder(title, body, date, data = {}) {
    const trigger = new Date(date);
    return await this.scheduleLocalNotification(title, body, {
      ...data,
      type: 'reminder'
    }, trigger);
  }

  async scheduleAssignmentReminder(assignmentId, assignmentTitle, dueDate) {
    const title = 'Assignment Due Soon';
    const body = `Your assignment "${assignmentTitle}" is due soon!`;
    const data = {
      type: 'assignment',
      assignmentId: assignmentId
    };
    
    // Schedule reminder 1 hour before due date
    const reminderDate = new Date(dueDate);
    reminderDate.setHours(reminderDate.getHours() - 1);
    
    return await this.scheduleLocalNotification(title, body, data, reminderDate);
  }

  async scheduleClassReminder(className, startTime) {
    const title = 'Class Starting Soon';
    const body = `Your class "${className}" starts in 10 minutes`;
    const data = {
      type: 'reminder',
      className: className
    };
    
    // Schedule reminder 10 minutes before class
    const reminderDate = new Date(startTime);
    reminderDate.setMinutes(reminderDate.getMinutes() - 10);
    
    return await this.scheduleLocalNotification(title, body, data, reminderDate);
  }

  // Cleanup
  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }

  // Getter methods
  getExpoPushToken() {
    return this.expoPushToken?.data;
  }

  isInitialized() {
    return this.isInitialized;
  }
}

export default new NotificationService(); 