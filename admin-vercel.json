{"version": 2, "name": "fameduconnect-admin", "builds": [{"src": "admin/package.json", "use": "@vercel/static-build", "config": {"distDir": "admin/build"}}], "routes": [{"src": "/api/(.*)", "dest": "https://api.fameduconnect.app/api/$1"}, {"src": "/(.*)", "dest": "/admin/build/$1"}], "env": {"REACT_APP_API_URL": "https://api.fameduconnect.app", "REACT_APP_SOCKET_URL": "https://api.fameduconnect.app", "REACT_APP_ENVIRONMENT": "production", "REACT_APP_ADMIN_MODE": "true"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.fameduconnect.app wss://api.fameduconnect.app"}]}], "rewrites": [{"source": "/((?!api).*)", "destination": "/index.html"}]}