import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MagnifyingGlassIcon, 
  XMarkIcon,
  UserIcon,
  AcademicCapIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { userAPI, messageAPI, classAPI } from '../../services/api';

const SearchModal = ({ isOpen, onClose, onSearch, initialQuery = '' }) => {
  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState({
    users: [],
    messages: [],
    classes: [],
    recent: []
  });
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [recentSearches, setRecentSearches] = useState([]);
  
  const inputRef = useRef(null);
  const searchTimeoutRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      inputRef.current?.focus();
      loadRecentSearches();
    }
  }, [isOpen]);

  useEffect(() => {
    if (query.length >= 2) {
      // Debounce search
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      
      searchTimeoutRef.current = setTimeout(() => {
        performSearch(query);
      }, 300);
    } else {
      setResults({ users: [], messages: [], classes: [], recent: [] });
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query]);

  const loadRecentSearches = () => {
    const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    setRecentSearches(recent.slice(0, 5));
  };

  const saveRecentSearch = (searchQuery) => {
    const recent = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    const updated = [searchQuery, ...recent.filter(q => q !== searchQuery)].slice(0, 10);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  const performSearch = async (searchQuery) => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    try {
      const [usersResponse, messagesResponse, classesResponse] = await Promise.allSettled([
        userAPI.searchUsers(searchQuery),
        messageAPI.getMessages({ search: searchQuery, limit: 5 }),
        classAPI.getClasses({ search: searchQuery, limit: 5 })
      ]);

      setResults({
        users: usersResponse.status === 'fulfilled' ? usersResponse.value.data : [],
        messages: messagesResponse.status === 'fulfilled' ? messagesResponse.value.data.messages || [] : [],
        classes: classesResponse.status === 'fulfilled' ? classesResponse.value.data.classes || [] : [],
        recent: recentSearches
      });
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchQuery) => {
    if (searchQuery.trim()) {
      saveRecentSearch(searchQuery.trim());
      onSearch(searchQuery.trim());
      onClose();
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSearch(query);
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  const getResultCount = () => {
    return results.users.length + results.messages.length + results.classes.length;
  };

  const tabs = [
    { id: 'all', label: 'All', count: getResultCount() },
    { id: 'users', label: 'People', count: results.users.length },
    { id: 'messages', label: 'Messages', count: results.messages.length },
    { id: 'classes', label: 'Classes', count: results.classes.length }
  ];

  const renderUserResult = (user) => (
    <div
      key={user.id}
      className="flex items-center space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer rounded-lg"
      onClick={() => handleSearch(`user:${user.email}`)}
    >
      <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
        {user.profilePicture ? (
          <img src={user.profilePicture} alt="" className="w-full h-full rounded-full object-cover" />
        ) : (
          <UserIcon className="h-5 w-5 text-gray-500" />
        )}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
          {user.firstName} {user.lastName}
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
          {user.email} • {user.role}
        </p>
      </div>
    </div>
  );

  const renderMessageResult = (message) => (
    <div
      key={message.id}
      className="flex items-start space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer rounded-lg"
      onClick={() => handleSearch(`message:${message.id}`)}
    >
      <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-400 mt-1" />
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-white">
          {message.sender?.firstName} {message.sender?.lastName}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
          {message.content}
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {new Date(message.createdAt).toLocaleDateString()}
        </p>
      </div>
    </div>
  );

  const renderClassResult = (classItem) => (
    <div
      key={classItem.id}
      className="flex items-center space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer rounded-lg"
      onClick={() => handleSearch(`class:${classItem.classCode}`)}
    >
      <AcademicCapIcon className="h-5 w-5 text-gray-400" />
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
          {classItem.className}
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {classItem.classCode} • Grade {classItem.grade} • {classItem.studentCount} students
        </p>
      </div>
    </div>
  );

  const renderRecentSearch = (search, index) => (
    <div
      key={index}
      className="flex items-center space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer rounded-lg"
      onClick={() => {
        setQuery(search);
        handleSearch(search);
      }}
    >
      <ClockIcon className="h-4 w-4 text-gray-400" />
      <span className="text-sm text-gray-700 dark:text-gray-300">{search}</span>
    </div>
  );

  const renderResults = () => {
    if (query.length < 2) {
      return (
        <div className="p-4">
          {recentSearches.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Recent Searches</h3>
              <div className="space-y-1">
                {recentSearches.map(renderRecentSearch)}
              </div>
            </div>
          )}
        </div>
      );
    }

    if (loading) {
      return (
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
        </div>
      );
    }

    if (getResultCount() === 0) {
      return (
        <div className="text-center p-8">
          <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No results found</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Try adjusting your search terms
          </p>
        </div>
      );
    }

    const showUsers = activeTab === 'all' || activeTab === 'users';
    const showMessages = activeTab === 'all' || activeTab === 'messages';
    const showClasses = activeTab === 'all' || activeTab === 'classes';

    return (
      <div className="p-4 space-y-4">
        {showUsers && results.users.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">People</h3>
            <div className="space-y-1">
              {results.users.map(renderUserResult)}
            </div>
          </div>
        )}

        {showMessages && results.messages.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Messages</h3>
            <div className="space-y-1">
              {results.messages.map(renderMessageResult)}
            </div>
          </div>
        )}

        {showClasses && results.classes.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Classes</h3>
            <div className="space-y-1">
              {results.classes.map(renderClassResult)}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 overflow-y-auto"
        >
          <div className="flex min-h-screen items-start justify-center p-4 pt-16">
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -20 }}
              className="w-full max-w-2xl bg-white dark:bg-gray-800 rounded-lg shadow-xl"
            >
              {/* Search Input */}
              <div className="flex items-center p-4 border-b border-gray-200 dark:border-gray-700">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 mr-3" />
                <input
                  ref={inputRef}
                  type="text"
                  placeholder="Search for people, messages, classes..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="flex-1 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none"
                />
                <button
                  onClick={onClose}
                  className="ml-3 p-1 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>

              {/* Tabs */}
              {query.length >= 2 && (
                <div className="flex border-b border-gray-200 dark:border-gray-700">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                        activeTab === tab.id
                          ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                          : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                      }`}
                    >
                      {tab.label}
                      {tab.count > 0 && (
                        <span className="ml-2 px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded-full">
                          {tab.count}
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              )}

              {/* Results */}
              <div className="max-h-96 overflow-y-auto">
                {renderResults()}
              </div>

              {/* Footer */}
              <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 rounded-b-lg">
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>Press Enter to search, Esc to close</span>
                  <span>Powered by FamEduConnect</span>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SearchModal;