import React, { useState, useEffect } from 'react';
import api from '../../services/api';

const ConnectionTest = () => {
  const [status, setStatus] = useState('checking');
  const [healthData, setHealthData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    try {
      setStatus('checking');
      setError(null);
      
      // Test health endpoint
      const healthResponse = await api.get('/health');
      setHealthData(healthResponse.data);
      setStatus('connected');
      
      console.log('✅ Backend connection successful:', healthResponse.data);
    } catch (err) {
      console.error('❌ Backend connection failed:', err);
      setError(err.message);
      setStatus('failed');
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'connected': return 'text-green-600';
      case 'failed': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'connected': return '✅';
      case 'failed': return '❌';
      default: return '⏳';
    }
  };

  return (
    <div className="fixed top-4 right-4 bg-white p-4 rounded-lg shadow-lg border max-w-sm">
      <h3 className="font-semibold mb-2">Backend Connection</h3>
      
      <div className={`flex items-center gap-2 mb-2 ${getStatusColor()}`}>
        <span>{getStatusIcon()}</span>
        <span className="capitalize">{status}</span>
      </div>

      {status === 'connected' && healthData && (
        <div className="text-sm space-y-1">
          <p><strong>Status:</strong> {healthData.status}</p>
          <p><strong>Database:</strong> {healthData.database}</p>
          <p><strong>Uptime:</strong> {Math.round(healthData.uptime)}s</p>
        </div>
      )}

      {status === 'failed' && error && (
        <div className="text-sm text-red-600">
          <p><strong>Error:</strong> {error}</p>
        </div>
      )}

      <button
        onClick={testConnection}
        className="mt-2 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
      >
        Retry Connection
      </button>
    </div>
  );
};

export default ConnectionTest; 