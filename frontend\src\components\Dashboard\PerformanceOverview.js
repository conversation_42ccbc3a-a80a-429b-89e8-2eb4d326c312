import React from 'react';
import { motion } from 'framer-motion';
import { ChartBarIcon, ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';

const PerformanceOverview = ({ data = null }) => {
  // Mock data if none provided
  const mockData = {
    averageGrade: 85.5,
    attendanceRate: 92.3,
    trend: 2.1,
    subjects: [
      { name: 'Mathematics', grade: 88, trend: 3 },
      { name: 'Science', grade: 85, trend: -1 },
      { name: 'English', grade: 90, trend: 5 },
      { name: 'History', grade: 82, trend: 1 }
    ]
  };

  const performanceData = data || mockData;

  const getGradeColor = (grade) => {
    if (grade >= 90) return 'text-green-600';
    if (grade >= 80) return 'text-blue-600';
    if (grade >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getGradeBg = (grade) => {
    if (grade >= 90) return 'bg-green-100 dark:bg-green-900/20';
    if (grade >= 80) return 'bg-blue-100 dark:bg-blue-900/20';
    if (grade >= 70) return 'bg-yellow-100 dark:bg-yellow-900/20';
    return 'bg-red-100 dark:bg-red-900/20';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Performance Overview</h3>
        <ChartBarIcon className="h-5 w-5 text-gray-400" />
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {performanceData.averageGrade}%
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">Average Grade</p>
          <div className="flex items-center justify-center mt-1">
            {performanceData.trend > 0 ? (
              <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <ArrowTrendingDownIcon className="h-4 w-4 text-red-500 mr-1" />
            )}
            <span className={`text-xs ${performanceData.trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {performanceData.trend > 0 ? '+' : ''}{performanceData.trend}%
            </span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {performanceData.attendanceRate}%
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">Attendance</p>
          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${performanceData.attendanceRate}%` }}
            ></div>
          </div>
        </motion.div>
      </div>

      {/* Subject Breakdown */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Subject Performance</h4>
        {performanceData.subjects.map((subject, index) => (
          <motion.div
            key={subject.name}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${getGradeBg(subject.grade)}`}></div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {subject.name}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className={`text-sm font-semibold ${getGradeColor(subject.grade)}`}>
                {subject.grade}%
              </span>
              <div className="flex items-center">
                {subject.trend > 0 ? (
                  <ArrowTrendingUpIcon className="h-3 w-3 text-green-500" />
                ) : subject.trend < 0 ? (
                  <ArrowTrendingDownIcon className="h-3 w-3 text-red-500" />
                ) : null}
                <span className={`text-xs ml-1 ${
                  subject.trend > 0 ? 'text-green-600' : 
                  subject.trend < 0 ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {subject.trend > 0 ? '+' : ''}{subject.trend}
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Progress Bar */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
          <span>Overall Progress</span>
          <span>{performanceData.averageGrade}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${performanceData.averageGrade}%` }}
            transition={{ duration: 1, delay: 0.5 }}
            className={`h-3 rounded-full ${
              performanceData.averageGrade >= 90 ? 'bg-green-500' :
              performanceData.averageGrade >= 80 ? 'bg-blue-500' :
              performanceData.averageGrade >= 70 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
          ></motion.div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceOverview;