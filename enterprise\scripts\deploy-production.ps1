# FamEduConnect Production Deployment Script
# Enterprise-level deployment with security, monitoring, and rollback capabilities

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("staging", "production")]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [string]$Version = "v1.0.0",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipTests,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force,
    
    [Parameter(Mandatory=$false)]
    [switch]$Rollback
)

# Configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $Color
}

# Error handling function
function Write-ErrorLog {
    param([string]$Message)
    Write-Log $Message "ERROR" $Red
    exit 1
}

# Success function
function Write-Success {
    param([string]$Message)
    Write-Log $Message "SUCCESS" $Green
}

# Warning function
function Write-Warning {
    param([string]$Message)
    Write-Log $Message "WARNING" $Yellow
}

# Information function
function Write-Info {
    param([string]$Message)
    Write-Log $Message "INFO" $Blue
}

# Check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check if kubectl is installed
    try {
        $kubectlVersion = kubectl version --client --short
        Write-Success "kubectl found: $kubectlVersion"
    } catch {
        Write-ErrorLog "kubectl is not installed or not in PATH"
    }
    
    # Check if docker is installed
    try {
        $dockerVersion = docker --version
        Write-Success "Docker found: $dockerVersion"
    } catch {
        Write-ErrorLog "Docker is not installed or not in PATH"
    }
    
    # Check if helm is installed
    try {
        $helmVersion = helm version --short
        Write-Success "Helm found: $helmVersion"
    } catch {
        Write-ErrorLog "Helm is not installed or not in PATH"
    }
    
    # Check Kubernetes cluster connectivity
    try {
        $clusterInfo = kubectl cluster-info
        Write-Success "Kubernetes cluster is accessible"
    } catch {
        Write-ErrorLog "Cannot connect to Kubernetes cluster"
    }
}

# Security checks
function Test-SecurityChecks {
    Write-Info "Running security checks..."
    
    # Check for secrets
    $requiredSecrets = @(
        "fameduconnect-secrets",
        "postgres-secrets",
        "grafana-secrets"
    )
    
    foreach ($secret in $requiredSecrets) {
        try {
            $secretExists = kubectl get secret $secret -n fameduconnect-prod --ignore-not-found
            if ($secretExists) {
                Write-Success "Secret $secret exists"
            } else {
                Write-ErrorLog "Required secret $secret is missing"
            }
        } catch {
            Write-ErrorLog "Failed to check secret $secret"
        }
    }
    
    # Check SSL certificates
    try {
        $certManager = kubectl get pods -n cert-manager --ignore-not-found
        if ($certManager) {
            Write-Success "Cert-manager is running"
        } else {
            Write-Warning "Cert-manager not found - SSL certificates may not be automatically managed"
        }
    } catch {
        Write-Warning "Could not check cert-manager status"
    }
}

# Run tests
function Invoke-Tests {
    if ($SkipTests) {
        Write-Warning "Skipping tests as requested"
        return
    }
    
    Write-Info "Running tests..."
    
    # Unit tests
    try {
        Write-Info "Running unit tests..."
        npm test --silent
        Write-Success "Unit tests passed"
    } catch {
        Write-ErrorLog "Unit tests failed"
    }
    
    # Integration tests
    try {
        Write-Info "Running integration tests..."
        npm run test:integration --silent
        Write-Success "Integration tests passed"
    } catch {
        Write-ErrorLog "Integration tests failed"
    }
    
    # Security tests
    try {
        Write-Info "Running security tests..."
        npm audit --audit-level=high
        Write-Success "Security audit passed"
    } catch {
        Write-Warning "Security audit found issues - review required"
    }
}

# Build Docker images
function Build-DockerImages {
    Write-Info "Building Docker images..."
    
    # Build backend image
    try {
        Write-Info "Building backend image..."
        docker build -t fameduconnect/backend:$Version -f backend/Dockerfile ./backend
        Write-Success "Backend image built successfully"
    } catch {
        Write-ErrorLog "Failed to build backend image"
    }
    
    # Build frontend image
    try {
        Write-Info "Building frontend image..."
        docker build -t fameduconnect/frontend:$Version -f frontend/Dockerfile ./frontend
        Write-Success "Frontend image built successfully"
    } catch {
        Write-ErrorLog "Failed to build frontend image"
    }
    
    # Build mobile image (if needed)
    try {
        Write-Info "Building mobile image..."
        docker build -t fameduconnect/mobile:$Version -f mobile/Dockerfile ./mobile
        Write-Success "Mobile image built successfully"
    } catch {
        Write-Warning "Failed to build mobile image - continuing"
    }
}

# Push Docker images
function Push-DockerImages {
    Write-Info "Pushing Docker images to registry..."
    
    $images = @("backend", "frontend", "mobile")
    
    foreach ($image in $images) {
        try {
            Write-Info "Pushing $image image..."
            docker push fameduconnect/$image`:$Version
            Write-Success "$image image pushed successfully"
        } catch {
            Write-ErrorLog "Failed to push $image image"
        }
    }
}

# Deploy to Kubernetes
function Deploy-Kubernetes {
    Write-Info "Deploying to Kubernetes..."
    
    # Create namespaces
    try {
        Write-Info "Creating namespaces..."
        kubectl apply -f enterprise/k8s/production-cluster.yaml
        kubectl apply -f enterprise/database/production-postgres.yaml
        kubectl apply -f enterprise/monitoring/production-monitoring.yaml
        Write-Success "Namespaces created successfully"
    } catch {
        Write-ErrorLog "Failed to create namespaces"
    }
    
    # Wait for namespaces to be ready
    Write-Info "Waiting for namespaces to be ready..."
    Start-Sleep -Seconds 10
    
    # Deploy database
    try {
        Write-Info "Deploying database..."
        kubectl apply -f enterprise/database/production-postgres.yaml
        Write-Success "Database deployed successfully"
    } catch {
        Write-ErrorLog "Failed to deploy database"
    }
    
    # Wait for database to be ready
    Write-Info "Waiting for database to be ready..."
    kubectl wait --for=condition=ready pod -l app=fameduconnect-postgres -n fameduconnect-database --timeout=300s
    
    # Deploy monitoring
    try {
        Write-Info "Deploying monitoring stack..."
        kubectl apply -f enterprise/monitoring/production-monitoring.yaml
        Write-Success "Monitoring stack deployed successfully"
    } catch {
        Write-ErrorLog "Failed to deploy monitoring stack"
    }
    
    # Deploy application
    try {
        Write-Info "Deploying application..."
        kubectl apply -f enterprise/k8s/production-cluster.yaml
        Write-Success "Application deployed successfully"
    } catch {
        Write-ErrorLog "Failed to deploy application"
    }
    
    # Wait for application to be ready
    Write-Info "Waiting for application to be ready..."
    kubectl wait --for=condition=ready pod -l app=fameduconnect-backend -n fameduconnect-prod --timeout=300s
    kubectl wait --for=condition=ready pod -l app=fameduconnect-frontend -n fameduconnect-prod --timeout=300s
}

# Health checks
function Test-HealthChecks {
    Write-Info "Running health checks..."
    
    $services = @(
        @{Name="Backend API"; URL="https://api.fameduconnect.com/health"},
        @{Name="Frontend"; URL="https://fameduconnect.com"},
        @{Name="Admin Dashboard"; URL="https://admin.fameduconnect.com"},
        @{Name="Grafana"; URL="https://grafana.fameduconnect.com"},
        @{Name="Kibana"; URL="https://kibana.fameduconnect.com"}
    )
    
    foreach ($service in $services) {
        try {
            $response = Invoke-WebRequest -Uri $service.URL -TimeoutSec 30 -SkipCertificateCheck
            if ($response.StatusCode -eq 200) {
                Write-Success "$($service.Name) is healthy"
            } else {
                Write-Warning "$($service.Name) returned status $($response.StatusCode)"
            }
        } catch {
            Write-Warning "$($service.Name) health check failed: $($_.Exception.Message)"
        }
    }
}

# Performance tests
function Invoke-PerformanceTests {
    Write-Info "Running performance tests..."
    
    # Load test with k6 (if available)
    try {
        Write-Info "Running load tests..."
        k6 run scripts/load-test.js
        Write-Success "Performance tests completed"
    } catch {
        Write-Warning "k6 not available - skipping performance tests"
    }
}

# Setup monitoring
function Setup-Monitoring {
    Write-Info "Setting up monitoring..."
    
    # Configure Grafana dashboards
    try {
        Write-Info "Configuring Grafana dashboards..."
        kubectl apply -f enterprise/monitoring/grafana-dashboards.yaml
        Write-Success "Grafana dashboards configured"
    } catch {
        Write-Warning "Failed to configure Grafana dashboards"
    }
    
    # Configure alerting rules
    try {
        Write-Info "Configuring alerting rules..."
        kubectl apply -f enterprise/monitoring/audit-logging.yaml
        Write-Success "Alerting rules configured"
    } catch {
        Write-Warning "Failed to configure alerting rules"
    }
}

# Rollback function
function Invoke-Rollback {
    Write-Info "Initiating rollback..."
    
    # Get previous version
    $previousVersion = kubectl get deployment fameduconnect-backend -n fameduconnect-prod -o jsonpath='{.spec.template.spec.containers[0].image}' | Select-String -Pattern 'v\d+\.\d+\.\d+' | ForEach-Object { $_.Matches[0].Value }
    
    if ($previousVersion) {
        Write-Info "Rolling back to version: $previousVersion"
        
        # Update deployments to previous version
        kubectl set image deployment/fameduconnect-backend backend=fameduconnect/backend:$previousVersion -n fameduconnect-prod
        kubectl set image deployment/fameduconnect-frontend frontend=fameduconnect/frontend:$previousVersion -n fameduconnect-prod
        
        Write-Success "Rollback completed"
    } else {
        Write-ErrorLog "No previous version found for rollback"
    }
}

# Main deployment function
function Start-Deployment {
    Write-Info "Starting FamEduConnect deployment to $Environment environment"
    Write-Info "Version: $Version"
    
    # Check if force flag is set
    if (-not $Force) {
        $confirmation = Read-Host "Are you sure you want to deploy to $Environment? (y/N)"
        if ($confirmation -ne "y" -and $confirmation -ne "Y") {
            Write-Info "Deployment cancelled by user"
            exit 0
        }
    }
    
    # Rollback if requested
    if ($Rollback) {
        Invoke-Rollback
        return
    }
    
    # Run deployment steps
    try {
        Test-Prerequisites
        Test-SecurityChecks
        Invoke-Tests
        Build-DockerImages
        Push-DockerImages
        Deploy-Kubernetes
        Test-HealthChecks
        Invoke-PerformanceTests
        Setup-Monitoring
        
        Write-Success "Deployment completed successfully!"
        Write-Info "Application URLs:"
        Write-Info "  - Main App: https://fameduconnect.com"
        Write-Info "  - API: https://api.fameduconnect.com"
        Write-Info "  - Admin: https://admin.fameduconnect.com"
        Write-Info "  - Grafana: https://grafana.fameduconnect.com"
        Write-Info "  - Kibana: https://kibana.fameduconnect.com"
        
    } catch {
        Write-ErrorLog "Deployment failed: $($_.Exception.Message)"
        Write-Info "To rollback, run: .\deploy-production.ps1 -Environment $Environment -Rollback"
    }
}

# Execute deployment
Start-Deployment 