module.exports = (sequelize, DataTypes) => {
  const Performance = sequelize.define('Performance', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    studentId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Students',
        key: 'id'
      }
    },
    classId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Classes',
        key: 'id'
      }
    },
    assessmentType: {
      type: DataTypes.ENUM('quiz', 'test', 'assignment', 'project', 'participation', 'behavior', 'homework'),
      allowNull: false
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 200]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false
    },
    maxScore: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    earnedScore: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      validate: {
        min: 0,
        max: 100
      }
    },
    letterGrade: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isIn: [['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'D-', 'F']]
      }
    },
    gradingPeriod: {
      type: DataTypes.ENUM('Q1', 'Q2', 'Q3', 'Q4', 'S1', 'S2', 'Final'),
      allowNull: false
    },
    dueDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    submittedDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    isLate: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    daysLate: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    feedback: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    rubricScores: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    skillsAssessed: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    improvementAreas: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    strengths: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    parentNotified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    notificationSentAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    requiresFollowUp: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    followUpCompleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    recordedBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    metadata: {
      type: DataTypes.JSON,
      defaultValue: {}
    }
  }, {
    hooks: {
      beforeSave: (performance) => {
        if (performance.earnedScore && performance.maxScore) {
          performance.percentage = (performance.earnedScore / performance.maxScore) * 100;
        }
      }
    },
    indexes: [
      { fields: ['studentId'] },
      { fields: ['classId'] },
      { fields: ['assessmentType'] },
      { fields: ['subject'] },
      { fields: ['gradingPeriod'] },
      { fields: ['dueDate'] },
      { fields: ['recordedBy'] },
      { fields: ['parentNotified'] }
    ]
  });

  Performance.associate = function(models) {
    Performance.belongsTo(models.Student, {
      foreignKey: 'studentId',
      as: 'student'
    });
    Performance.belongsTo(models.Class, {
      foreignKey: 'classId',
      as: 'class'
    });
    Performance.belongsTo(models.User, {
      foreignKey: 'recordedBy',
      as: 'teacher'
    });
  };

  return Performance;
};