apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "famedu-backend.fullname" . }}
  labels:
    {{- include "famedu-backend.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "famedu-backend.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "famedu-backend.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "famedu-backend.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /api/health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /api/health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
          env:
            - name: NODE_ENV
              value: {{ .Values.env.NODE_ENV | quote }}
            - name: PORT
              value: {{ .Values.env.PORT | quote }}
            - name: LOG_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: {{ include "famedu-backend.fullname" . }}-config
                  key: LOG_LEVEL
            - name: ENABLE_METRICS
              valueFrom:
                configMapKeyRef:
                  name: {{ include "famedu-backend.fullname" . }}-config
                  key: ENABLE_METRICS
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "famedu-backend.fullname" . }}-secrets
                  key: DATABASE_URL
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "famedu-backend.fullname" . }}-secrets
                  key: JWT_SECRET
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "famedu-backend.fullname" . }}-secrets
                  key: REDIS_URL
            - name: RABBITMQ_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "famedu-backend.fullname" . }}-secrets
                  key: RABBITMQ_URL
            - name: SENTRY_DSN
              valueFrom:
                secretKeyRef:
                  name: {{ include "famedu-backend.fullname" . }}-secrets
                  key: SENTRY_DSN
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }} 