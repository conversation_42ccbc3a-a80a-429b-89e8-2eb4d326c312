import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchClasses } from '../../store/slices/classesSlice';
import ClassTable from '../../components/Classes/ClassTable';

const Classes = () => {
  const dispatch = useDispatch();
  const { classes, isLoading } = useSelector((state) => state.classes);

  useEffect(() => {
    dispatch(fetchClasses());
  }, [dispatch]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Classes</h1>
        <div className="text-sm text-gray-500">
          Total: {classes.length} classes
        </div>
      </div>
      
      <ClassTable classes={classes} isLoading={isLoading} />
    </div>
  );
};

export default Classes;