# FamEduConnect Enterprise Monitoring Setup Script
# This script sets up comprehensive monitoring and performance tracking

param(
    [string]$Environment = "production",
    [switch]$Verbose,
    [switch]$SkipAlerts
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "================================`n$Message`n================================" -ForegroundColor Blue
}

# Function to create Grafana dashboard
function New-GrafanaDashboard {
    param(
        [string]$DashboardName,
        [string]$DashboardJson,
        [string]$Namespace = "fameduconnect"
    )
    
    $dashboardConfigMap = @"
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-$DashboardName
  namespace: $Namespace
  labels:
    grafana_dashboard: "1"
data:
  $DashboardName.json: |
$DashboardJson
"@
    
    return $dashboardConfigMap
}

# Function to create Prometheus alert rule
function New-PrometheusAlertRule {
    param(
        [string]$AlertName,
        [string]$AlertExpression,
        [string]$Severity = "warning",
        [string]$Description = ""
    )
    
    $alertRule = @"
  - alert: $AlertName
    expr: $AlertExpression
    for: 5m
    labels:
      severity: $Severity
    annotations:
      summary: "$AlertName"
      description: "$Description"
"@
    
    return $alertRule
}

# Main monitoring setup function
function Main {
    Write-Header "FamEduConnect Enterprise Monitoring Setup"
    
    # Check if running from the correct directory
    if (-not (Test-Path "enterprise/ENTERPRISE_DEPLOYMENT_GUIDE.md")) {
        Write-Error "Please run this script from the FamEduConnect_Full_Codebase directory"
        exit 1
    }
    
    Write-Status "Setting up monitoring for environment: $Environment"
    
    # Create monitoring directory
    $monitoringDir = "enterprise/monitoring/$Environment"
    if (Test-Path $monitoringDir) {
        Remove-Item $monitoringDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $monitoringDir -Force | Out-Null
    
    # Step 1: Create Grafana Dashboards
    Write-Header "Step 1: Creating Grafana Dashboards"
    
    # Application Overview Dashboard
    $appOverviewDashboard = @'
{
  "dashboard": {
    "id": null,
    "title": "FamEduConnect Application Overview",
    "tags": ["fameduconnect", "application"],
    "style": "dark",
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])",
            "legendFormat": "{{method}} {{route}}"
          }
        ],
        "yAxes": [
          {
            "label": "Response Time (seconds)",
            "min": 0
          }
        ]
      },
      {
        "id": 2,
        "title": "Active Users",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(active_users_total)",
            "legendFormat": "Active Users"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "green", "value": 0},
                {"color": "yellow", "value": 100},
                {"color": "red", "value": 500}
              ]
            }
          }
        }
      },
      {
        "id": 3,
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m])",
            "legendFormat": "5xx Errors"
          }
        ],
        "yAxes": [
          {
            "label": "Errors per second",
            "min": 0
          }
        ]
      },
      {
        "id": 4,
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends",
            "legendFormat": "{{datname}}"
          }
        ],
        "yAxes": [
          {
            "label": "Connections",
            "min": 0
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
'@
    
    $appOverviewConfigMap = New-GrafanaDashboard "app-overview" $appOverviewDashboard
    Set-Content "$monitoringDir/grafana-dashboard-app-overview.yaml" $appOverviewConfigMap
    Write-Status "Created Application Overview Dashboard"
    
    # Infrastructure Dashboard
    $infrastructureDashboard = @'
{
  "dashboard": {
    "id": null,
    "title": "FamEduConnect Infrastructure",
    "tags": ["fameduconnect", "infrastructure"],
    "style": "dark",
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "CPU Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "{{instance}}"
          }
        ],
        "yAxes": [
          {
            "label": "CPU %",
            "min": 0,
            "max": 100
          }
        ]
      },
      {
        "id": 2,
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100",
            "legendFormat": "{{instance}}"
          }
        ],
        "yAxes": [
          {
            "label": "Memory %",
            "min": 0,
            "max": 100
          }
        ]
      },
      {
        "id": 3,
        "title": "Disk Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "(node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100",
            "legendFormat": "{{instance}} {{mountpoint}}"
          }
        ],
        "yAxes": [
          {
            "label": "Disk %",
            "min": 0,
            "max": 100
          }
        ]
      },
      {
        "id": 4,
        "title": "Pod Status",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(kube_pod_status_phase)",
            "legendFormat": "{{phase}}"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
'@
    
    $infrastructureConfigMap = New-GrafanaDashboard "infrastructure" $infrastructureDashboard
    Set-Content "$monitoringDir/grafana-dashboard-infrastructure.yaml" $infrastructureConfigMap
    Write-Status "Created Infrastructure Dashboard"
    
    # Security Dashboard
    $securityDashboard = @'
{
  "dashboard": {
    "id": null,
    "title": "FamEduConnect Security Monitoring",
    "tags": ["fameduconnect", "security"],
    "style": "dark",
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Failed Login Attempts",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(failed_login_attempts_total[5m])",
            "legendFormat": "Failed Logins"
          }
        ],
        "yAxes": [
          {
            "label": "Failed attempts per second",
            "min": 0
          }
        ]
      },
      {
        "id": 2,
        "title": "Suspicious Activities",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(suspicious_activities_total[5m])",
            "legendFormat": "Suspicious Activities"
          }
        ],
        "yAxes": [
          {
            "label": "Activities per second",
            "min": 0
          }
        ]
      },
      {
        "id": 3,
        "title": "SSL Certificate Expiry",
        "type": "stat",
        "targets": [
          {
            "expr": "ssl_certificate_expiry_days",
            "legendFormat": "{{domain}}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "yellow", "value": 30},
                {"color": "green", "value": 90}
              ]
            }
          }
        }
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
'@
    
    $securityConfigMap = New-GrafanaDashboard "security" $securityDashboard
    Set-Content "$monitoringDir/grafana-dashboard-security.yaml" $securityConfigMap
    Write-Status "Created Security Dashboard"
    
    # Step 2: Create Prometheus Alert Rules
    Write-Header "Step 2: Creating Prometheus Alert Rules"
    
    $alertRules = @()
    
    # High CPU Usage Alert
    $alertRules += New-PrometheusAlertRule `
        -AlertName "HighCPUUsage" `
        -AlertExpression "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100) > 80" `
        -Severity "warning" `
        -Description "CPU usage is above 80% for 5 minutes"
    
    # High Memory Usage Alert
    $alertRules += New-PrometheusAlertRule `
        -AlertName "HighMemoryUsage" `
        -AlertExpression "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85" `
        -Severity "warning" `
        -Description "Memory usage is above 85% for 5 minutes"
    
    # High Error Rate Alert
    $alertRules += New-PrometheusAlertRule `
        -AlertName "HighErrorRate" `
        -AlertExpression "rate(http_requests_total{status=~\"5..\"}[5m]) > 0.1" `
        -Severity "critical" `
        -Description "Error rate is above 10% for 5 minutes"
    
    # Database Connection Alert
    $alertRules += New-PrometheusAlertRule `
        -AlertName "HighDatabaseConnections" `
        -AlertExpression "pg_stat_database_numbackends > 80" `
        -Severity "warning" `
        -Description "Database connections are above 80"
    
    # SSL Certificate Expiry Alert
    $alertRules += New-PrometheusAlertRule `
        -AlertName "SSLCertificateExpiring" `
        -AlertExpression "ssl_certificate_expiry_days < 30" `
        -Severity "warning" `
        -Description "SSL certificate expires in less than 30 days"
    
    # Pod Restart Alert
    $alertRules += New-PrometheusAlertRule `
        -AlertName "PodRestarting" `
        -AlertExpression "increase(kube_pod_container_status_restarts_total[1h]) > 5" `
        -Severity "critical" `
        -Description "Pod has restarted more than 5 times in the last hour"
    
    # Create PrometheusRule
    $prometheusRule = @"
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: fameduconnect-alerts
  namespace: fameduconnect
  labels:
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
  - name: fameduconnect.rules
    rules:
$($alertRules -join "`n")
"@
    
    Set-Content "$monitoringDir/prometheus-alert-rules.yaml" $prometheusRule
    Write-Status "Created Prometheus Alert Rules"
    
    # Step 3: Create AlertManager Configuration
    Write-Header "Step 3: Creating AlertManager Configuration"
    
    $alertManagerConfig = @"
global:
  resolve_timeout: 5m
  slack_api_url: 'https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'slack-notifications'
  routes:
  - match:
      severity: critical
    receiver: 'pagerduty-critical'
    continue: true

receivers:
- name: 'slack-notifications'
  slack_configs:
  - channel: '#fameduconnect-alerts'
    send_resolved: true
    title: '{{ template "slack.title" . }}'
    text: '{{ template "slack.text" . }}'

- name: 'pagerduty-critical'
  pagerduty_configs:
  - routing_key: 'YOUR_PAGERDUTY_ROUTING_KEY'
    send_resolved: true

templates:
- '/etc/alertmanager/template/*.tmpl'
"@
    
    $alertManagerConfigMap = @"
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: fameduconnect
data:
  alertmanager.yml: |
$alertManagerConfig
"@
    
    Set-Content "$monitoringDir/alertmanager-config.yaml" $alertManagerConfigMap
    Write-Status "Created AlertManager Configuration"
    
    # Step 4: Create Monitoring Deployment Script
    Write-Header "Step 4: Creating Monitoring Deployment Script"
    
    $deployScript = @"
# FamEduConnect Monitoring Deployment Script - $Environment
# Generated on: $(Get-Date)

Write-Host "Deploying monitoring configuration for $Environment..." -ForegroundColor Green

# Create namespace if it doesn't exist
kubectl create namespace fameduconnect --dry-run=client -o yaml | kubectl apply -f -

# Apply Grafana dashboards
Write-Host "Applying Grafana dashboards..." -ForegroundColor Yellow
kubectl apply -f grafana-dashboard-app-overview.yaml
kubectl apply -f grafana-dashboard-infrastructure.yaml
kubectl apply -f grafana-dashboard-security.yaml

# Apply Prometheus alert rules
Write-Host "Applying Prometheus alert rules..." -ForegroundColor Yellow
kubectl apply -f prometheus-alert-rules.yaml

# Apply AlertManager configuration
Write-Host "Applying AlertManager configuration..." -ForegroundColor Yellow
kubectl apply -f alertmanager-config.yaml

# Restart AlertManager to pick up new configuration
Write-Host "Restarting AlertManager..." -ForegroundColor Yellow
kubectl rollout restart deployment alertmanager -n fameduconnect

Write-Host "Monitoring deployment completed!" -ForegroundColor Green
Write-Host "Dashboards available at:" -ForegroundColor Cyan
Write-Host "  - Application Overview: http://grafana.fameduconnect.com/d/app-overview" -ForegroundColor White
Write-Host "  - Infrastructure: http://grafana.fameduconnect.com/d/infrastructure" -ForegroundColor White
Write-Host "  - Security: http://grafana.fameduconnect.com/d/security" -ForegroundColor White
Write-Host "  - Prometheus: http://prometheus.fameduconnect.com" -ForegroundColor White
Write-Host "  - AlertManager: http://alertmanager.fameduconnect.com" -ForegroundColor White
"@
    
    Set-Content "$monitoringDir/deploy-monitoring.ps1" $deployScript
    
    # Step 5: Create Monitoring Guide
    Write-Header "Step 5: Creating Monitoring Guide"
    
    $monitoringGuide = @"
# FamEduConnect Monitoring Guide

## Overview
This guide provides instructions for monitoring the FamEduConnect enterprise deployment using Grafana dashboards, Prometheus metrics, and alerting systems.

## Dashboard Access

### Grafana Dashboards
- **Application Overview**: http://grafana.fameduconnect.com/d/app-overview
- **Infrastructure**: http://grafana.fameduconnect.com/d/infrastructure
- **Security**: http://grafana.fameduconnect.com/d/security

### Monitoring Tools
- **Prometheus**: http://prometheus.fameduconnect.com
- **AlertManager**: http://alertmanager.fameduconnect.com
- **Elasticsearch**: http://elasticsearch.fameduconnect.com

## Key Metrics to Monitor

### Application Metrics
- API response times (p50, p95, p99)
- Request rate and throughput
- Error rates (4xx, 5xx)
- Active users and sessions
- Database query performance

### Infrastructure Metrics
- CPU usage per node
- Memory utilization
- Disk space and I/O
- Network traffic
- Pod status and restarts

### Security Metrics
- Failed login attempts
- Suspicious activities
- SSL certificate expiry
- Authentication failures
- Network policy violations

## Alerting Rules

### Critical Alerts
- High error rate (>10%)
- Pod restarting frequently
- Database connection issues
- SSL certificate expiring soon

### Warning Alerts
- High CPU usage (>80%)
- High memory usage (>85%)
- High database connections
- Disk space running low

## Troubleshooting

### Common Issues
1. **Dashboard not loading**: Check Grafana pod status
2. **No metrics**: Verify Prometheus targets
3. **Alerts not firing**: Check PrometheusRule status
4. **No notifications**: Verify AlertManager configuration

### Useful Commands
```bash
# Check monitoring pod status
kubectl get pods -n fameduconnect -l app=grafana
kubectl get pods -n fameduconnect -l app=prometheus
kubectl get pods -n fameduconnect -l app=alertmanager

# Check alert rules
kubectl get prometheusrules -n fameduconnect

# View alert manager logs
kubectl logs -n fameduconnect -l app=alertmanager

# Test alert rule
kubectl port-forward -n fameduconnect svc/prometheus 9090:9090
```

## Performance Optimization

### Dashboard Optimization
- Use appropriate time ranges
- Limit query complexity
- Use rate() functions for counters
- Aggregate data when possible

### Alert Optimization
- Set appropriate thresholds
- Use for: clause to prevent flapping
- Group related alerts
- Test alert rules regularly
"@
    
    Set-Content "$monitoringDir/MONITORING_GUIDE.md" $monitoringGuide
    
    # Step 6: Create Performance Monitoring Script
    Write-Header "Step 6: Creating Performance Monitoring Script"
    
    $performanceScript = @"
# FamEduConnect Performance Monitoring Script
# This script provides real-time performance monitoring

param(
    [switch]`$Continuous,
    [int]`$Interval = 30
)

function Get-PerformanceMetrics {
    Write-Host "=== FamEduConnect Performance Metrics ===" -ForegroundColor Blue
    Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Gray
    
    # Get pod status
    Write-Host "`nPod Status:" -ForegroundColor Yellow
    kubectl get pods -n fameduconnect --no-headers | ForEach-Object {
        `$parts = `$_ -split '\s+'
        `$podName = `$parts[0]
        `$status = `$parts[2]
        `$ready = `$parts[1]
        
        if (`$status -eq "Running" -and `$ready -match "^\d+/\d+$") {
            Write-Host "  ✓ `$podName - `$status (`$ready)" -ForegroundColor Green
        } else {
            Write-Host "  ✗ `$podName - `$status (`$ready)" -ForegroundColor Red
        }
    }
    
    # Get resource usage
    Write-Host "`nResource Usage:" -ForegroundColor Yellow
    try {
        `$topOutput = kubectl top pods -n fameduconnect --no-headers 2>`$null
        if (`$topOutput) {
            `$topOutput | ForEach-Object {
                `$parts = `$_ -split '\s+'
                `$podName = `$parts[0]
                `$cpu = `$parts[1]
                `$memory = `$parts[2]
                Write-Host "  `$podName - CPU: `$cpu, Memory: `$memory" -ForegroundColor White
            }
        } else {
            Write-Host "  Metrics server not available" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "  Unable to get resource metrics" -ForegroundColor Red
    }
    
    # Get service endpoints
    Write-Host "`nService Endpoints:" -ForegroundColor Yellow
    kubectl get endpoints -n fameduconnect --no-headers | ForEach-Object {
        `$parts = `$_ -split '\s+'
        `$serviceName = `$parts[0]
        `$endpoints = `$parts[1]
        Write-Host "  `$serviceName - `$endpoints" -ForegroundColor White
    }
    
    # Get recent events
    Write-Host "`nRecent Events:" -ForegroundColor Yellow
    kubectl get events -n fameduconnect --sort-by='.lastTimestamp' --no-headers | Select-Object -Last 5 | ForEach-Object {
        `$parts = `$_ -split '\s+'
        `$time = `$parts[0] + " " + `$parts[1]
        `$type = `$parts[2]
        `$reason = `$parts[3]
        `$object = `$parts[4]
        `$message = `$parts[5..(`$parts.Length-1)] -join " "
        
        if (`$type -eq "Warning") {
            Write-Host "  ⚠ `$time - `$reason on `$object: `$message" -ForegroundColor Yellow
        } else {
            Write-Host "  ℹ `$time - `$reason on `$object: `$message" -ForegroundColor Gray
        }
    }
    
    Write-Host "`n" + "="*50 -ForegroundColor Blue
}

# Main execution
if (`$Continuous) {
    Write-Host "Starting continuous monitoring (Press Ctrl+C to stop)..." -ForegroundColor Green
    while (`$true) {
        Get-PerformanceMetrics
        Start-Sleep -Seconds `$Interval
        Clear-Host
    }
} else {
    Get-PerformanceMetrics
}
"@
    
    Set-Content "$monitoringDir/monitor-performance.ps1" $performanceScript
    
    # Step 7: Summary
    Write-Header "Monitoring Setup Complete!"
    
    Write-Status "Generated monitoring files for $Environment environment:"
    Write-Host "  - $monitoringDir/grafana-dashboard-app-overview.yaml" -ForegroundColor White
    Write-Host "  - $monitoringDir/grafana-dashboard-infrastructure.yaml" -ForegroundColor White
    Write-Host "  - $monitoringDir/grafana-dashboard-security.yaml" -ForegroundColor White
    Write-Host "  - $monitoringDir/prometheus-alert-rules.yaml" -ForegroundColor White
    Write-Host "  - $monitoringDir/alertmanager-config.yaml" -ForegroundColor White
    Write-Host "  - $monitoringDir/deploy-monitoring.ps1" -ForegroundColor White
    Write-Host "  - $monitoringDir/MONITORING_GUIDE.md" -ForegroundColor White
    Write-Host "  - $monitoringDir/monitor-performance.ps1" -ForegroundColor White
    
    Write-Warning "IMPORTANT:"
    Write-Host "  1. Update Slack webhook URL in alertmanager-config.yaml" -ForegroundColor White
    Write-Host "  2. Update PagerDuty routing key for critical alerts" -ForegroundColor White
    Write-Host "  3. Customize alert thresholds based on your environment" -ForegroundColor White
    Write-Host "  4. Test alert notifications after deployment" -ForegroundColor White
    
    Write-Status "Next steps:"
    Write-Host "  1. Review and customize monitoring configuration" -ForegroundColor White
    Write-Host "  2. Update notification settings" -ForegroundColor White
    Write-Host "  3. Run: cd $monitoringDir && .\deploy-monitoring.ps1" -ForegroundColor White
    Write-Host "  4. Access Grafana dashboards to monitor system health" -ForegroundColor White
    
    Write-Status "Monitoring setup completed successfully!"
}

# Run main function
Main 