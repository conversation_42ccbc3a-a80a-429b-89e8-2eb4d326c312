# FamEduConnect Setup Instructions

## Quick Start

1. **Install Dependencies**:
   ```
   npm install
   cd backend && npm install && cd ..
   cd frontend && npm install --legacy-peer-deps && cd ..
   cd admin && npm install --legacy-peer-deps && cd ..
   cd mobile && npm install --legacy-peer-deps && cd ..
   ```

2. **Start the Project**:
   ```
   node start-project.js
   ```

## Manual Setup

If you prefer to set up each component individually:

### Backend
```
cd backend
npm install
node server.js
```

### Frontend
```
cd frontend
npm install --legacy-peer-deps
npm start
```

### Admin Dashboard
```
cd admin
npm install --legacy-peer-deps
npm start
```

### Mobile App
```
cd mobile
npm install --legacy-peer-deps
npm start
```

## Database Setup

For full functionality, you'll need PostgreSQL:

1. Install PostgreSQL
2. Create a database named 'fameduconnect'
3. Update the .env file with your database credentials

## Troubleshooting

- **Port conflicts**: If you see EADDRINUSE errors, change the port in the respective .env file
- **Database connection issues**: Make sure PostgreSQL is running and accessible
- **Node version**: This project requires Node.js 18+

## Production Deployment

See the docs/DEPLOYMENT.md file for production deployment instructions.