const { sequelize } = require('./models');
const { User } = require('./models');

async function resetAdmin() {
  try {
    console.log('🔧 Resetting admin user...');
    
    // Delete existing admin user
    await User.destroy({ 
      where: { email: '<EMAIL>' } 
    });
    console.log('✅ Existing admin user deleted');
    
    // Create new admin user with correct password hashing
    const adminUser = await User.create({
      email: '<EMAIL>',
      password: 'AdminDemo2025!', // Will be hashed by model hook
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      isActive: true,
      isVerified: true,
      preferredLanguage: 'en',
      accessibilitySettings: {
        highContrast: false,
        largeText: false,
        screenReader: false
      },
      notificationSettings: {
        email: true,
        push: true,
        sms: false
      }
    });
    
    console.log('✅ Admin user recreated successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: AdminDemo2025!');
    console.log('🆔 User ID:', adminUser.id);
    
  } catch (error) {
    console.error('❌ Error resetting admin user:', error);
  } finally {
    await sequelize.close();
  }
}

resetAdmin(); 