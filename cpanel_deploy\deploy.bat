@echo off
echo ========================================
echo FamEduConnect Frontend - cPanel Deploy
echo ========================================
echo.

echo [1/4] Building frontend...
cd ..\frontend
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Frontend build failed!
    pause
    exit /b 1
)

echo [2/4] Copying build files...
cd ..\cpanel_deploy
robocopy ..\frontend\build static /E /XD node_modules
if %errorlevel% gtr 7 (
    echo ERROR: File copy failed!
    pause
    exit /b 1
)

echo [3/4] Creating deployment package...
if exist "fameduconnect-frontend.zip" del "fameduconnect-frontend.zip"
powershell -command "Compress-Archive -Path 'static\*', '.htaccess', 'config.js', 'DEPLOYMENT_INSTRUCTIONS.md' -DestinationPath 'fameduconnect-frontend.zip'"

echo [4/4] Deployment package created!
echo.
echo ========================================
echo DEPLOYMENT PACKAGE READY
echo ========================================
echo File: fameduconnect-frontend.zip
echo.
echo Next steps:
echo 1. Upload the zip file to your cPanel File Manager
echo 2. Extract it to your domain's document root
echo 3. Edit config.js with your production settings
echo 4. Test your deployment
echo.
echo See DEPLOYMENT_INSTRUCTIONS.md for detailed steps
echo ========================================
pause