<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FamEduConnect - Deployment Verification</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #2563eb;
            margin-bottom: 30px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            background: #f9fafb;
        }
        .status {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }
        .success { background: #dcfce7; color: #166534; }
        .error { background: #fef2f2; color: #dc2626; }
        .warning { background: #fef3c7; color: #d97706; }
        .info { background: #dbeafe; color: #1d4ed8; }
        .config-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8fafc;
            border-radius: 5px;
            border-left: 4px solid #2563eb;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1d4ed8; }
        .code {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 FamEduConnect</h1>
            <h2>Deployment Verification</h2>
        </div>

        <div id="tests">
            <div class="test-item">
                <span>Static Files Loading</span>
                <span id="static-status" class="status info">Testing...</span>
            </div>
            <div class="test-item">
                <span>Configuration File</span>
                <span id="config-status" class="status info">Testing...</span>
            </div>
            <div class="test-item">
                <span>API Connection</span>
                <span id="api-status" class="status info">Testing...</span>
            </div>
            <div class="test-item">
                <span>HTTPS/SSL</span>
                <span id="ssl-status" class="status info">Testing...</span>
            </div>
            <div class="test-item">
                <span>Routing (.htaccess)</span>
                <span id="routing-status" class="status info">Testing...</span>
            </div>
        </div>

        <div class="config-section">
            <h3>Current Configuration</h3>
            <div id="current-config" class="code">Loading configuration...</div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runTests()">Run Tests Again</button>
            <button onclick="testAPI()">Test API Connection</button>
            <button onclick="window.location.href='/'">Go to App</button>
        </div>

        <div id="results" style="margin-top: 20px;"></div>
    </div>

    <script>
        // Load configuration
        function loadConfig() {
            try {
                if (typeof window.ENV !== 'undefined') {
                    document.getElementById('current-config').textContent = JSON.stringify(window.ENV, null, 2);
                    document.getElementById('config-status').textContent = 'OK';
                    document.getElementById('config-status').className = 'status success';
                } else {
                    document.getElementById('current-config').textContent = 'Configuration not found! Make sure config.js is loaded.';
                    document.getElementById('config-status').textContent = 'ERROR';
                    document.getElementById('config-status').className = 'status error';
                }
            } catch (e) {
                document.getElementById('config-status').textContent = 'ERROR';
                document.getElementById('config-status').className = 'status error';
            }
        }

        // Test static files
        function testStaticFiles() {
            const img = new Image();
            img.onload = function() {
                document.getElementById('static-status').textContent = 'OK';
                document.getElementById('static-status').className = 'status success';
            };
            img.onerror = function() {
                document.getElementById('static-status').textContent = 'ERROR';
                document.getElementById('static-status').className = 'status error';
            };
            img.src = '/favicon.ico?' + Date.now();
        }

        // Test SSL
        function testSSL() {
            if (location.protocol === 'https:') {
                document.getElementById('ssl-status').textContent = 'OK';
                document.getElementById('ssl-status').className = 'status success';
            } else {
                document.getElementById('ssl-status').textContent = 'WARNING';
                document.getElementById('ssl-status').className = 'status warning';
            }
        }

        // Test routing
        function testRouting() {
            fetch('/test-route-that-does-not-exist')
                .then(response => {
                    if (response.status === 200) {
                        document.getElementById('routing-status').textContent = 'OK';
                        document.getElementById('routing-status').className = 'status success';
                    } else {
                        document.getElementById('routing-status').textContent = 'CHECK';
                        document.getElementById('routing-status').className = 'status warning';
                    }
                })
                .catch(() => {
                    document.getElementById('routing-status').textContent = 'CHECK';
                    document.getElementById('routing-status').className = 'status warning';
                });
        }

        // Test API connection
        function testAPI() {
            if (typeof window.ENV === 'undefined' || !window.ENV.API_BASE_URL) {
                document.getElementById('api-status').textContent = 'NO CONFIG';
                document.getElementById('api-status').className = 'status error';
                return;
            }

            fetch(window.ENV.API_BASE_URL + '/health')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('api-status').textContent = 'OK';
                        document.getElementById('api-status').className = 'status success';
                    } else {
                        document.getElementById('api-status').textContent = 'ERROR';
                        document.getElementById('api-status').className = 'status error';
                    }
                })
                .catch(error => {
                    document.getElementById('api-status').textContent = 'ERROR';
                    document.getElementById('api-status').className = 'status error';
                    console.error('API test failed:', error);
                });
        }

        // Run all tests
        function runTests() {
            // Reset statuses
            document.querySelectorAll('.status').forEach(el => {
                el.textContent = 'Testing...';
                el.className = 'status info';
            });

            setTimeout(() => {
                loadConfig();
                testStaticFiles();
                testSSL();
                testRouting();
                testAPI();
            }, 500);
        }

        // Run tests on page load
        window.onload = function() {
            runTests();
        };
    </script>
    
    <!-- Load configuration -->
    <script src="/config.js"></script>
</body>
</html>