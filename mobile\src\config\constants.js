// API Configuration
export const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3001' 
  : 'https://api.fameduconnect.com';

export const SOCKET_URL = __DEV__ 
  ? 'ws://localhost:3001' 
  : 'wss://api.fameduconnect.com';

// App Configuration
export const APP_NAME = 'FamEduConnect';
export const APP_VERSION = '1.0.0';

// Feature Flags
export const FEATURES = {
  VIDEO_CALLS: true,
  PUSH_NOTIFICATIONS: true,
  FILE_UPLOAD: true,
  REAL_TIME_MESSAGING: true,
  OFFLINE_MODE: true,
};

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
  },
  USERS: {
    PROFILE: '/api/users/profile',
    SEARCH: '/api/users/search',
    UPDATE: '/api/users/update',
  },
  MESSAGES: {
    LIST: '/api/messages',
    SEND: '/api/messages',
    MARK_READ: '/api/messages/read',
  },
  NOTIFICATIONS: {
    REGISTER: '/api/notifications/register',
    UNREGISTER: '/api/notifications/unregister',
    SETTINGS: '/api/notifications/settings',
  },
  FILES: {
    UPLOAD: '/api/files/upload',
    DOWNLOAD: '/api/files/download',
    DELETE: '/api/files/delete',
  },
  CALLS: {
    START: '/api/calls/start',
    END: '/api/calls/end',
    STATUS: '/api/calls/status',
  },
};

// WebRTC Configuration
export const WEBRTC_CONFIG = {
  ICE_SERVERS: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    {
      urls: 'turn:fameduconnect-turn.com:3478',
      username: 'fameduconnect',
      credential: 'your-turn-password'
    }
  ],
  ICE_CANDIDATE_POOL_SIZE: 10,
};

// Notification Configuration
export const NOTIFICATION_CONFIG = {
  CHANNELS: {
    MESSAGES: 'messages',
    CALLS: 'calls',
    ASSIGNMENTS: 'assignments',
    ANNOUNCEMENTS: 'announcements',
    REMINDERS: 'reminders',
  },
  SOUNDS: {
    MESSAGE: 'message.mp3',
    CALL: 'call.mp3',
    REMINDER: 'reminder.mp3',
  },
};

// Storage Keys
export const STORAGE_KEYS = {
  USER_TOKEN: 'userToken',
  USER_PROFILE: 'userProfile',
  PUSH_TOKEN: 'expoPushToken',
  SETTINGS: 'appSettings',
  CACHE: 'appCache',
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  AUTH_ERROR: 'Authentication failed. Please log in again.',
  PERMISSION_ERROR: 'Permission denied. Please enable required permissions.',
  CAMERA_ERROR: 'Camera access denied. Please enable camera permissions.',
  MICROPHONE_ERROR: 'Microphone access denied. Please enable microphone permissions.',
  NOTIFICATION_ERROR: 'Notification permission denied. Please enable notifications.',
  FILE_UPLOAD_ERROR: 'File upload failed. Please try again.',
  CALL_ERROR: 'Call failed. Please try again.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful!',
  REGISTER_SUCCESS: 'Registration successful!',
  MESSAGE_SENT: 'Message sent successfully!',
  FILE_UPLOADED: 'File uploaded successfully!',
  CALL_STARTED: 'Call started successfully!',
  SETTINGS_SAVED: 'Settings saved successfully!',
};

// Validation Rules
export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_MIN_LENGTH: 8,
  USERNAME_MIN_LENGTH: 3,
  MESSAGE_MAX_LENGTH: 1000,
  FILE_MAX_SIZE: 50 * 1024 * 1024, // 50MB
};

// UI Constants
export const UI_CONSTANTS = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
  PULL_TO_REFRESH_DELAY: 1000,
  TYPING_INDICATOR_DELAY: 1000,
  MESSAGE_TIMESTAMP_FORMAT: 'HH:mm',
  DATE_FORMAT: 'MMM dd, yyyy',
  TIME_FORMAT: 'HH:mm:ss',
};

// Cache Configuration
export const CACHE_CONFIG = {
  MESSAGES_TTL: 24 * 60 * 60 * 1000, // 24 hours
  PROFILE_TTL: 7 * 24 * 60 * 60 * 1000, // 7 days
  SETTINGS_TTL: 30 * 24 * 60 * 60 * 1000, // 30 days
  MAX_CACHE_SIZE: 100 * 1024 * 1024, // 100MB
};

// Analytics Events
export const ANALYTICS_EVENTS = {
  APP_OPEN: 'app_open',
  USER_LOGIN: 'user_login',
  USER_REGISTER: 'user_register',
  MESSAGE_SENT: 'message_sent',
  CALL_STARTED: 'call_started',
  FILE_UPLOADED: 'file_uploaded',
  NOTIFICATION_RECEIVED: 'notification_received',
  FEATURE_USED: 'feature_used',
};

// Performance Monitoring
export const PERFORMANCE_CONFIG = {
  SLOW_NETWORK_THRESHOLD: 3000, // 3 seconds
  ANIMATION_FRAME_RATE: 60,
  MEMORY_WARNING_THRESHOLD: 0.8, // 80%
  BATTERY_SAVING_MODE: false,
};

export default {
  API_BASE_URL,
  SOCKET_URL,
  APP_NAME,
  APP_VERSION,
  FEATURES,
  API_ENDPOINTS,
  WEBRTC_CONFIG,
  NOTIFICATION_CONFIG,
  STORAGE_KEYS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  VALIDATION_RULES,
  UI_CONSTANTS,
  CACHE_CONFIG,
  ANALYTICS_EVENTS,
  PERFORMANCE_CONFIG,
}; 