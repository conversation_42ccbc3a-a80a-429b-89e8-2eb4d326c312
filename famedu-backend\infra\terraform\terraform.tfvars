# Local Development Variables
environment = "dev"
aws_region = "us-east-1"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"
availability_zones = ["us-east-1a", "us-east-1b"]

# Database Configuration
database_name = "fameduconnect_dev"
database_username = "famedu_user"
database_password = "famedu_password_123"
database_instance_class = "db.t3.micro"

# Redis Configuration
redis_node_type = "cache.t3.micro"

# RabbitMQ Configuration
rabbitmq_username = "famedu_user"
rabbitmq_password = "famedu_password_123"
rabbitmq_instance_type = "mq.t3.micro"

# EKS Configuration
eks_desired_capacity = 2
eks_max_capacity = 4
eks_min_capacity = 1
eks_instance_types = ["t3.medium"]

# Domain Configuration
domain_name = "api.fameduconnect.local"
certificate_arn = ""

# JWT Configuration
jwt_secret = "your-super-secret-jwt-key-change-in-production" 