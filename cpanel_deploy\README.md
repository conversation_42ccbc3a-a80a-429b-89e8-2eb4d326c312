# FamEduConnect Frontend - cPanel Deployment Package

## 📦 Package Overview

This deployment package contains everything needed to deploy the FamEduConnect frontend to a cPanel hosting environment.

### Package Contents

```
cpanel_deploy/
├── static/                     # Production build files
├── .htaccess                   # Apache configuration
├── config.js                   # Environment configuration
├── deploy.bat                  # Windows deployment script
├── deploy.ps1                  # PowerShell deployment script
├── verify-deployment.html      # Deployment verification tool
├── DEPLOYMENT_INSTRUCTIONS.md  # Detailed deployment guide
└── README.md                   # This file
```

## 🚀 Quick Start

### Option 1: Automated Deployment (Recommended)

1. **Run the deployment script:**
   ```bash
   # Windows Command Prompt
   deploy.bat
   
   # PowerShell
   .\deploy.ps1
   ```

2. **Upload the generated zip file:**
   - Upload `fameduconnect-frontend.zip` to your cPanel File Manager
   - Extract it to your domain's document root (usually `public_html`)

3. **Configure your environment:**
   - Edit `config.js` with your production API URLs
   - Test your deployment using `verify-deployment.html`

### Option 2: Manual Deployment

1. **Build the frontend:**
   ```bash
   cd ../frontend
   npm run build
   ```

2. **Copy files to cPanel:**
   - Upload all files from `frontend/build/` to your web root
   - Upload `.htaccess` to your web root
   - Upload `config.js` to your web root

3. **Configure and test:**
   - Edit `config.js` with your settings
   - Visit your domain to test

## ⚙️ Configuration

### Required Configuration

Edit `config.js` with your production values:

```javascript
window.ENV = {
  API_BASE_URL: 'https://your-api-domain.com/api',
  SOCKET_URL: 'https://your-api-domain.com',
  ENVIRONMENT: 'production'
};
```

### Backend Requirements

Your backend API must be:
- ✅ Deployed and accessible
- ✅ CORS configured for your frontend domain
- ✅ SSL/HTTPS enabled
- ✅ Health check endpoint available at `/api/health`

## 🔧 Verification

After deployment, use the verification tool:

1. Visit `https://your-domain.com/verify-deployment.html`
2. Check all tests pass
3. Fix any issues before going live

## 📋 Pre-Deployment Checklist

### Domain & Hosting
- [ ] Domain purchased and configured
- [ ] cPanel hosting account active
- [ ] SSL certificate installed
- [ ] DNS properly configured

### Backend Setup
- [ ] Backend API deployed
- [ ] Database connected
- [ ] CORS configured
- [ ] SSL/HTTPS enabled

### Frontend Build
- [ ] Dependencies installed (`npm install`)
- [ ] Build successful (`npm run build`)
- [ ] No build errors or warnings

## 🛠️ Troubleshooting

### Common Issues

**Build Fails**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
npm run build
```

**404 Errors on Refresh**
- Ensure `.htaccess` is uploaded
- Check mod_rewrite is enabled in cPanel

**API Connection Issues**
- Verify `config.js` API URLs
- Check CORS settings on backend
- Ensure SSL certificates are valid

**Static Assets Not Loading**
- Check file permissions (644 for files, 755 for directories)
- Verify all build files were uploaded

## 📁 File Structure After Deployment

Your web root should contain:
```
public_html/
├── static/
│   ├── css/
│   ├── js/
│   └── media/
├── index.html
├── favicon.ico
├── asset-manifest.json
├── .htaccess
├── config.js
└── verify-deployment.html (optional)
```

## 🔒 Security Features

The deployment includes:
- ✅ Security headers in `.htaccess`
- ✅ Content Security Policy
- ✅ XSS protection
- ✅ Clickjacking prevention
- ✅ HTTPS enforcement
- ✅ Sensitive file blocking

## 📈 Performance Optimizations

Included optimizations:
- ✅ Gzip compression
- ✅ Static asset caching
- ✅ Minified CSS/JS
- ✅ Optimized images
- ✅ Lazy loading

## 🆘 Support

### Getting Help

1. **Check the verification tool** - Visit `/verify-deployment.html`
2. **Review logs** - Check cPanel error logs
3. **Test locally** - Ensure it works in development
4. **Check documentation** - See `DEPLOYMENT_INSTRUCTIONS.md`

### Common Commands

```bash
# Rebuild and redeploy
npm run build
.\deploy.ps1

# Test locally
npm start

# Check for issues
npm run lint
npm test
```

## 📝 Maintenance

### Regular Tasks
- Monitor application performance
- Check error logs weekly
- Update dependencies monthly
- Backup files regularly

### Updates
To deploy updates:
1. Pull latest code
2. Run `npm run build`
3. Run deployment script
4. Upload new zip file
5. Test functionality

---

**Need help?** Check the detailed deployment instructions in `DEPLOYMENT_INSTRUCTIONS.md`