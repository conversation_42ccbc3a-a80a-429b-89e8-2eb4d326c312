import React from 'react';
import { useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';

const TypingIndicator = () => {
  const { typingUsers } = useSelector((state) => state.socket);
  const { selectedConversation } = useSelector((state) => state.messages);

  // Filter typing users for current conversation
  const relevantTypingUsers = typingUsers.filter(user => {
    if (!selectedConversation) return false;
    
    if (selectedConversation.type === 'direct') {
      return user.userId === selectedConversation.participantId;
    } else if (selectedConversation.type === 'class') {
      return user.classId === selectedConversation.classId;
    }
    
    return false;
  });

  if (relevantTypingUsers.length === 0) {
    return null;
  }

  const getTypingText = () => {
    const count = relevantTypingUsers.length;
    
    if (count === 1) {
      return `${relevantTypingUsers[0].firstName} is typing...`;
    } else if (count === 2) {
      return `${relevantTypingUsers[0].firstName} and ${relevantTypingUsers[1].firstName} are typing...`;
    } else {
      return `${count} people are typing...`;
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className="flex items-center space-x-2 px-4 py-2"
      >
        <div className="flex items-center space-x-1">
          {/* Typing animation dots */}
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
                className="w-2 h-2 bg-gray-400 rounded-full"
              />
            ))}
          </div>
        </div>
        
        <span className="text-sm text-gray-500 dark:text-gray-400 italic">
          {getTypingText()}
        </span>
      </motion.div>
    </AnimatePresence>
  );
};

export default TypingIndicator;