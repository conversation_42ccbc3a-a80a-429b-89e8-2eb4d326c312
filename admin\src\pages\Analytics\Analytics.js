import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchAnalytics } from '../../store/slices/analyticsSlice';
import Chart from '../../components/Dashboard/Chart';

const Analytics = () => {
  const dispatch = useDispatch();
  const { data } = useSelector((state) => state.analytics);

  useEffect(() => {
    dispatch(fetchAnalytics());
  }, [dispatch]);

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Chart title="Daily Active Users" data={data?.dailyActiveUsers || []} />
        <Chart title="Message Volume" data={data?.messageVolume || []} />
        <Chart title="Video Call Duration" data={data?.callDuration || []} />
        <Chart title="User Engagement" data={data?.userEngagement || []} />
      </div>
    </div>
  );
};

export default Analytics;