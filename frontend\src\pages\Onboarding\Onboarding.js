import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence, useAnimation, useMotionValue, useTransform } from 'framer-motion';
import { 
  UserIcon, 
  AcademicCapIcon, 
  CogIcon, 
  CheckCircleIcon,
  ChatBubbleLeftRightIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  SparklesIcon,
  MicrophoneIcon,
  PaperAirplaneIcon,
  HeartIcon,
  ShieldCheckIcon,
  BellIcon,
  EyeIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
  AcademicCapIcon as SchoolIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { updateUserProfile, completeOnboarding } from '../../store/slices/authSlice';
import {
  ReanimatedView,
  ReanimatedText,
  ReanimatedButton,
  ReanimatedCard,
  ReanimatedProgress,
  ReanimatedSwitch
} from '../../components/UI/ReanimatedComponents';

const Onboarding = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const messagesEndRef = useRef(null);
  const aiChatRef = useRef(null);
  const containerRef = useRef(null);
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    phone: user?.phone || '',
    role: user?.role || 'parent',
    children: [],
    preferences: {
      notifications: true,
      emailUpdates: true,
      smsUpdates: false,
      accessibility: {
        highContrast: false,
        largeText: false,
        screenReader: false
      }
    }
  });

  const [aiBotOpen, setAiBotOpen] = useState(false);
  const [aiMessages, setAiMessages] = useState([
    {
      id: 1,
      type: 'bot',
      message: "Hi! I'm your AI assistant. I'm here to help you get started with FamEduConnect. What would you like to know about the onboarding process?",
      timestamp: new Date()
    }
  ]);
  const [userInput, setUserInput] = useState('');
  const [isListening, setIsListening] = useState(false);

  // React Native Reanimated-style motion values
  const progress = useMotionValue(0);
  const scale = useMotionValue(1);
  const rotate = useMotionValue(0);
  const opacity = useMotionValue(1);

  // Transform values for smooth animations
  const progressWidth = useTransform(progress, [0, 1], ['0%', '100%']);
  const cardScale = useTransform(scale, [0.8, 1], [0.8, 1]);
  const cardRotate = useTransform(rotate, [0, 360], [0, 360]);

  const steps = [
    {
      id: 0,
      title: "Welcome to FamEduConnect",
      subtitle: "Let's create your personalized experience",
      icon: SparklesIcon,
      description: "We're excited to have you join our community. This setup will take about 3-5 minutes and will help us customize your experience.",
      fields: []
    },
    {
      id: 1,
      title: "Personal Information",
      subtitle: "Tell us about yourself",
      icon: UserIcon,
      description: "This information helps us personalize your experience and connect you with the right people.",
      fields: [
        { name: 'firstName', label: 'First Name', type: 'text', required: true },
        { name: 'lastName', label: 'Last Name', type: 'text', required: true },
        { name: 'phone', label: 'Phone Number', type: 'tel', required: false }
      ]
    },
    {
      id: 2,
      title: "Your Role",
      subtitle: "How will you be using FamEduConnect?",
      icon: AcademicCapIcon,
      description: "Your role determines what features and tools you'll have access to. Don't worry, you can change this later.",
      fields: [
        { 
          name: 'role', 
          label: 'Role', 
          type: 'select', 
          options: [
            { value: 'parent', label: 'Parent', icon: HeartIcon, description: 'View children\'s progress and communicate with teachers' },
            { value: 'teacher', label: 'Teacher', icon: SchoolIcon, description: 'Manage classes and track student progress' },
            { value: 'student', label: 'Student', icon: AcademicCapIcon, description: 'Access learning materials and submit assignments' },
            { value: 'admin', label: 'Administrator', icon: Cog6ToothIcon, description: 'Manage the platform and oversee users' }
          ],
          required: true 
        }
      ]
    },
    {
      id: 3,
      title: "Communication Preferences",
      subtitle: "How would you like to stay connected?",
      icon: BellIcon,
      description: "Choose how you'd like to receive updates and notifications about important activities.",
      fields: []
    },
    {
      id: 4,
      title: "Accessibility & Comfort",
      subtitle: "Make FamEduConnect work for you",
      icon: EyeIcon,
      description: "These settings help make the platform more comfortable and accessible for your needs.",
      fields: []
    },
    {
      id: 5,
      title: "Almost Done!",
      subtitle: "Review and complete your setup",
      icon: CheckCircleIcon,
      description: "Take a moment to review your information. You can always update these settings later.",
      fields: []
    }
  ];

  // Auto-scroll effects
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [aiMessages]);

  useEffect(() => {
    aiChatRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentStep]);

  // Update progress animation
  useEffect(() => {
    const newProgress = (currentStep + 1) / steps.length;
    progress.set(newProgress);
  }, [currentStep, steps.length, progress]);

  // Smooth step transitions
  const handleNext = async () => {
    if (currentStep < steps.length - 1 && !isTransitioning) {
      setIsTransitioning(true);
      
      // Exit animation
      await scale.set(0.8);
      await opacity.set(0);
      
      // Update step
      setCurrentStep(currentStep + 1);
      
      // Enter animation
      setTimeout(() => {
        scale.set(1);
        opacity.set(1);
        setIsTransitioning(false);
      }, 300);
    }
  };

  const handleBack = async () => {
    if (currentStep > 0 && !isTransitioning) {
      setIsTransitioning(true);
      
      // Exit animation
      await scale.set(0.8);
      await opacity.set(0);
      
      // Update step
      setCurrentStep(currentStep - 1);
      
      // Enter animation
      setTimeout(() => {
        scale.set(1);
        opacity.set(1);
        setIsTransitioning(false);
      }, 300);
    }
  };

  const handleElementClick = () => {
    setTimeout(() => {
      aiChatRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const handleInputChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePreferenceChange = (category, key, value) => {
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [category]: {
          ...prev.preferences[category],
          [key]: value
        }
      }
    }));
  };

  const handleComplete = async () => {
    try {
      // Success animation
      await rotate.set(360);
      
      // Update local state immediately
      dispatch(completeOnboarding(formData));
      
      // Try to update server (but don't block on it)
    try {
      await dispatch(updateUserProfile({
        ...formData,
        hasCompletedOnboarding: true
      })).unwrap();
      } catch (apiError) {
        console.log('API update failed, but continuing with local state:', apiError);
      }
      
      toast.success('Onboarding completed successfully!');
      
      // Navigate to dashboard
      navigateToRoleBasedDashboard(formData.role);
      
    } catch (error) {
      console.error('Onboarding completion error:', error);
      
      // Fallback: update local state and navigate
      dispatch(completeOnboarding(formData));
      toast.success('Onboarding completed! Navigating to dashboard...');
      navigateToRoleBasedDashboard(formData.role);
    }
  };

  const handleSkip = () => {
    const assumedRole = user?.role === 'admin' ? 'admin' : 
                       user?.role === 'teacher' ? 'teacher' : 'parent';
    
    toast.success('Onboarding skipped. You can complete it later in settings.');
    navigateToRoleBasedDashboard(assumedRole);
  };

  const navigateToRoleBasedDashboard = (role) => {
    try {
    switch (role) {
      case 'parent':
          navigate('/dashboard/parent');
        break;
      case 'teacher':
          navigate('/dashboard/teacher');
        break;
      case 'student':
          navigate('/dashboard/student');
        break;
      case 'admin':
        navigate('/admin');
        break;
      default:
          navigate('/dashboard');
          break;
      }
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to main dashboard
        navigate('/dashboard');
    }
  };

  const sendAiMessage = async () => {
    if (!userInput.trim()) return;

    const userMessage = {
      id: aiMessages.length + 1,
      type: 'user',
      message: userInput,
      timestamp: new Date()
    };

    setAiMessages(prev => [...prev, userMessage]);
    setUserInput('');

    // Simulate AI response with typing animation
    setTimeout(() => {
      const aiResponse = {
        id: aiMessages.length + 2,
        type: 'bot',
        message: getAiResponse(userInput),
        timestamp: new Date()
      };
      setAiMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  const startVoiceRecognition = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';
      
      recognition.onstart = () => {
        setIsListening(true);
        toast.success('Listening... Speak now!');
      };
      
      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setUserInput(transcript);
        toast.success('Voice input received!');
      };
      
      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        toast.error('Voice recognition failed. Please try typing.');
      };
      
      recognition.onend = () => {
        setIsListening(false);
      };
      
      recognition.start();
    } else {
      toast.error('Voice recognition not supported in this browser.');
    }
  };

  const getAiResponse = (input) => {
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes('help') || lowerInput.includes('what')) {
      return "I can help you with: setting up your profile, understanding your role, configuring preferences, and navigating the platform. What specific area would you like assistance with?";
    } else if (lowerInput.includes('role') || lowerInput.includes('parent') || lowerInput.includes('teacher')) {
      return "Your role determines what features you'll have access to. Parents can view their children's progress, teachers can manage classes, and students can access their learning materials.";
    } else if (lowerInput.includes('preference') || lowerInput.includes('setting')) {
      return "Preferences help customize your experience. You can control notifications, accessibility features, and communication preferences. You can always change these later in settings.";
    } else if (lowerInput.includes('skip') || lowerInput.includes('later')) {
      return "You can skip onboarding and complete it later in your profile settings. However, completing it now will give you the best experience.";
    } else if (lowerInput.includes('dashboard') || lowerInput.includes('go to')) {
      return "After completing onboarding, you'll be taken to your role-specific dashboard. Parents see their children's progress, teachers see their classes, and students see their assignments.";
    } else if (lowerInput.includes('settings') || lowerInput.includes('profile')) {
      return "You can access settings anytime from your dashboard. There you can complete onboarding, update your profile, and customize your preferences.";
    } else if (lowerInput.includes('next') || lowerInput.includes('continue')) {
      handleNext();
      return "Moving to the next step of onboarding...";
    } else if (lowerInput.includes('back') || lowerInput.includes('previous')) {
      handleBack();
      return "Going back to the previous step...";
    } else if (lowerInput.includes('complete') || lowerInput.includes('finish')) {
      handleComplete();
      return "Completing your onboarding setup...";
    } else if (lowerInput.includes('skip')) {
      handleSkip();
      return "Skipping onboarding for now. You can complete it later in settings.";
    } else {
      return "I'm here to help you get the most out of FamEduConnect! Feel free to ask me anything about the platform, your role, or how to customize your experience. You can also say 'next', 'back', 'complete', or 'skip' to navigate.";
    }
  };

  const renderStepContent = () => {
    const step = steps[currentStep];

    switch (currentStep) {
      case 0:
        return (
          <motion.div 
            className="text-center space-y-8" 
            ref={aiChatRef}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="mx-auto w-32 h-32 bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-2xl"
            >
              <SparklesIcon className="w-16 h-16 text-white" />
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Welcome to FamEduConnect!
              </h2>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                We're excited to have you join our community. Let's get you set up in just a few minutes.
              </p>
              
              <motion.div 
                className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-200"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <div className="flex items-center justify-center space-x-2 mb-3">
                  <StarIcon className="w-5 h-5 text-yellow-500" />
                  <p className="text-blue-800 font-medium">Pro tip</p>
                </div>
                <p className="text-blue-800">
                  Use the AI assistant (bottom right) if you need help during onboarding!
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        );

      case 1:
        return (
          <motion.div 
            className="space-y-8" 
            ref={aiChatRef}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-3">Personal Information</h2>
              <p className="text-lg text-gray-600 mb-6">{step.description}</p>
            </div>
            
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 gap-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {step.fields.map((field, index) => (
                <motion.div 
                  key={field.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.1 * index }}
                  whileHover={{ scale: 1.02 }}
                  className="space-y-2"
                >
                  <label className="block text-sm font-semibold text-gray-700">
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </label>
                  <input
                    type={field.type}
                    value={formData[field.name]}
                    onChange={(e) => handleInputChange(field.name, e.target.value)}
                    onClick={handleElementClick}
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-lg"
                    placeholder={`Enter your ${field.label.toLowerCase()}`}
                  />
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div 
            className="space-y-8" 
            ref={aiChatRef}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-3">Your Role</h2>
              <p className="text-lg text-gray-600 mb-6">{step.description}</p>
            </div>
            
            <motion.div 
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {step.fields.map((field) => (
                <div key={field.name} className="space-y-4">
                  <label className="block text-sm font-semibold text-gray-700">
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </label>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {field.options.map((option, index) => (
                      <motion.div
                        key={option.value}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.1 * index }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                          formData[field.name] === option.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => handleInputChange(field.name, option.value)}
                      >
                        <div className="flex items-start space-x-4">
                          <div className={`p-3 rounded-lg ${
                            formData[field.name] === option.value
                              ? 'bg-blue-500 text-white'
                              : 'bg-gray-100 text-gray-600'
                          }`}>
                            <option.icon className="w-6 h-6" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-1">{option.label}</h3>
                            <p className="text-sm text-gray-600">{option.description}</p>
                          </div>
                          {formData[field.name] === option.value && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="absolute top-4 right-4"
                            >
                              <CheckCircleIcon className="w-6 h-6 text-blue-500" />
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              ))}
            </motion.div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div 
            className="space-y-8" 
            ref={aiChatRef}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-3">Communication Preferences</h2>
              <p className="text-lg text-gray-600 mb-6">{step.description}</p>
            </div>
            
            <motion.div 
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[
                  { key: 'notifications', label: 'Push Notifications', icon: BellIcon, description: 'Get instant updates on important activities' },
                  { key: 'emailUpdates', label: 'Email Updates', icon: PaperAirplaneIcon, description: 'Receive detailed reports via email' },
                  { key: 'smsUpdates', label: 'SMS Updates', icon: DevicePhoneMobileIcon, description: 'Get quick alerts via text message' }
                ].map((pref, index) => (
                  <motion.div
                    key={pref.key}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.1 * index }}
                    whileHover={{ scale: 1.02 }}
                    className="p-6 border-2 border-gray-200 rounded-xl hover:border-blue-300 transition-all duration-200"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <pref.icon className="w-5 h-5 text-blue-600" />
                      </div>
                      <h3 className="font-semibold text-gray-900">{pref.label}</h3>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">{pref.description}</p>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                        checked={formData.preferences[pref.key]}
                        onChange={(e) => handlePreferenceChange(pref.key, pref.key, e.target.checked)}
                        onClick={handleElementClick}
                        className="mr-3 h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="text-gray-700">Enable {pref.label.toLowerCase()}</span>
                  </label>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div 
            className="space-y-8" 
            ref={aiChatRef}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
              <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-3">Accessibility & Comfort</h2>
              <p className="text-lg text-gray-600 mb-6">{step.description}</p>
            </div>
            
            <motion.div 
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[
                  { key: 'highContrast', label: 'High Contrast', icon: EyeIcon, description: 'Enhanced visibility for better readability' },
                  { key: 'largeText', label: 'Large Text', icon: GlobeAltIcon, description: 'Increase text size for easier reading' },
                  { key: 'screenReader', label: 'Screen Reader', icon: ShieldCheckIcon, description: 'Enable screen reader support' }
                ].map((access, index) => (
                  <motion.div
                    key={access.key}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.1 * index }}
                    whileHover={{ scale: 1.02 }}
                    className="p-6 border-2 border-gray-200 rounded-xl hover:border-green-300 transition-all duration-200"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <access.icon className="w-5 h-5 text-green-600" />
                      </div>
                      <h3 className="font-semibold text-gray-900">{access.label}</h3>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">{access.description}</p>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                        checked={formData.preferences.accessibility[access.key]}
                        onChange={(e) => handlePreferenceChange('accessibility', access.key, e.target.checked)}
                        onClick={handleElementClick}
                        className="mr-3 h-5 w-5 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <span className="text-gray-700">Enable {access.label.toLowerCase()}</span>
                  </label>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        );

      case 5:
        return (
          <motion.div 
            className="space-y-8" 
            ref={aiChatRef}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <div className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="mx-auto w-24 h-24 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mb-6"
              >
                <CheckCircleIcon className="w-12 h-12 text-white" />
              </motion.div>
              
              <h2 className="text-3xl font-bold text-gray-900 mb-3">Almost Done!</h2>
              <p className="text-lg text-gray-600 mb-8">{step.description}</p>
            </div>
            
            <motion.div 
              className="bg-gray-50 p-6 rounded-xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h3 className="font-semibold text-gray-900 mb-4">Review Your Information:</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Name:</span>
                  <span className="font-medium">{formData.firstName} {formData.lastName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Role:</span>
                  <span className="font-medium capitalize">{formData.role}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Phone:</span>
                  <span className="font-medium">{formData.phone || 'Not provided'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Notifications:</span>
                  <span className="font-medium">{formData.preferences.notifications ? 'Enabled' : 'Disabled'}</span>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              className="text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <p className="text-gray-600 mb-4">
                Ready to start your FamEduConnect journey?
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleComplete}
                className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Complete Setup
              </motion.button>
            </motion.div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex">
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <motion.div 
          className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-200 px-6 py-4"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.div 
                className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <span className="text-white font-bold text-lg">F</span>
              </motion.div>
              <h1 className="text-2xl font-bold text-gray-900">FamEduConnect</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">Step {currentStep + 1} of {steps.length}</span>
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <motion.div 
                  className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                  style={{ width: progressWidth }}
                />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 flex items-center justify-center p-6">
          <motion.div 
            ref={containerRef}
            className="max-w-4xl w-full bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl p-8"
            style={{ scale: cardScale, opacity }}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.4 }}
              >
            {renderStepContent()}
              </motion.div>
            </AnimatePresence>
            
            {/* Navigation Buttons */}
            <motion.div 
              className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <motion.button
                onClick={() => {
                  handleBack();
                  handleElementClick();
                }}
                disabled={currentStep === 0 || isTransitioning}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center px-6 py-3 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg hover:bg-gray-100 transition-all duration-200"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                Back
              </motion.button>
              
              <div className="flex items-center space-x-4">
                <motion.button
                  onClick={() => {
                    handleSkip();
                    handleElementClick();
                  }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-3 text-gray-600 hover:text-gray-800 border-2 border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200 font-medium"
                >
                  Skip for now
                </motion.button>
                
                {currentStep < steps.length - 1 && (
                  <motion.button
                    onClick={() => {
                      handleNext();
                      handleElementClick();
                    }}
                    disabled={isTransitioning}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all duration-200 font-semibold"
                  >
                    Next
                    <ArrowRightIcon className="w-5 h-5 ml-2" />
                  </motion.button>
                )}
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* AI Assistant */}
      <div className="fixed bottom-6 right-6 z-50">
        {/* AI Chat Window */}
        <AnimatePresence>
        {aiBotOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-200 w-96 h-[500px] flex flex-col"
          >
            {/* Chat Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-4 rounded-t-2xl flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <ChatBubbleLeftRightIcon className="w-6 h-6" />
                  <span className="font-semibold text-lg">AI Assistant</span>
              </div>
                <motion.button
                onClick={() => setAiBotOpen(false)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="text-white hover:text-gray-200 text-2xl font-bold"
              >
                ×
                </motion.button>
            </div>

            {/* Chat Messages */}
              <div className="flex-1 overflow-y-auto p-6 space-y-4">
                <AnimatePresence>
                  {aiMessages.map((message, index) => (
                    <motion.div
                  key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                      <motion.div
                        className={`max-w-xs px-4 py-3 rounded-2xl ${
                      message.type === 'user'
                            ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                        whileHover={{ scale: 1.02 }}
                  >
                        <p className="text-sm leading-relaxed">{message.message}</p>
                        <p className="text-xs opacity-70 mt-2">
                      {message.timestamp.toLocaleTimeString()}
                    </p>
                      </motion.div>
                    </motion.div>
              ))}
                </AnimatePresence>
              <div ref={messagesEndRef} />
            </div>

            {/* Chat Input */}
              <div className="p-6 border-t border-gray-200">
                <div className="flex items-center space-x-3">
                  <motion.input
                  type="text"
                  value={userInput}
                  onChange={(e) => setUserInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendAiMessage()}
                  placeholder="Ask me anything..."
                    className="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    whileFocus={{ scale: 1.02 }}
                />
                  <motion.button
                  onClick={startVoiceRecognition}
                  disabled={isListening}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className={`flex-shrink-0 p-3 rounded-xl ${
                    isListening 
                      ? 'bg-red-500 text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                  title="Voice input"
                >
                    <MicrophoneIcon className="w-5 h-5" />
                  </motion.button>
                  <motion.button
                  onClick={sendAiMessage}
                  disabled={!userInput.trim()}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="flex-shrink-0 p-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Send message"
                >
                    <PaperAirplaneIcon className="w-5 h-5" />
                  </motion.button>
                </div>
            </div>
          </motion.div>
        )}
        </AnimatePresence>

        {/* AI Assistant Toggle Button */}
        <motion.button
          onClick={() => setAiBotOpen(!aiBotOpen)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-200"
        >
          <ChatBubbleLeftRightIcon className="w-7 h-7" />
        </motion.button>
      </div>
    </div>
  );
};

export default Onboarding; 