#!/bin/bash

# FamEduConnect Deployment Script
# © 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc. – NPO: 2023-001341848
# Creative Director: <PERSON><PERSON><PERSON><PERSON>

set -e

echo "🚀 Starting FamEduConnect Deployment..."
echo "Landing: fameduconnect.com"
echo "Application: fameduconnect.app"
echo "Admin: fameduconnect.xyz"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
print_status "Checking prerequisites..."

if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed"
    exit 1
fi

if ! command -v vercel &> /dev/null; then
    print_warning "Vercel CLI not found, installing..."
    npm install -g vercel
fi

print_success "Prerequisites check completed"

# Build Frontend
print_status "Building Frontend Application..."
cd frontend
npm ci --production
npm run build
print_success "Frontend build completed"

# Build Admin Dashboard
print_status "Building Admin Dashboard..."
cd ../admin
npm ci --production
npm run build
print_success "Admin dashboard build completed"

# Prepare Backend for deployment
print_status "Preparing Backend for deployment..."
cd ../backend
npm ci --production
print_success "Backend preparation completed"

# Deploy to Vercel
print_status "Deploying to Vercel..."

# Deploy Landing Page
cd ..
print_status "Deploying Landing Page to fameduconnect.com..."
vercel --prod --name fameduconnect-landing --local-config vercel-landing.json

# Deploy Frontend App
print_status "Deploying Frontend App to fameduconnect.app..."
vercel --prod --name fameduconnect-app --local-config vercel.json

# Deploy Admin Dashboard
print_status "Deploying Admin Dashboard to fameduconnect.xyz..."
vercel --prod --name fameduconnect-admin --local-config admin-vercel.json

print_success "Vercel deployments completed"

# Deploy Backend (assuming Railway/Render)
print_status "Backend deployment instructions:"
echo "1. Push backend code to your Git repository"
echo "2. Connect to Railway/Render/Heroku"
echo "3. Set environment variables from backend/.env.production"
echo "4. Deploy with: railway up / render deploy / git push heroku main"

# SSL Certificate Setup
print_status "SSL Certificate setup:"
echo "1. Certificates should be automatically provisioned by Vercel"
echo "2. For custom domains, add DNS records:"
echo "   - A record: @ -> Vercel IP"
echo "   - CNAME: app -> cname.vercel-dns.com"
echo "   - CNAME: admin -> cname.vercel-dns.com"

# Final checks
print_status "Running final deployment checks..."

# Check if sites are accessible
check_site() {
    local url=$1
    local name=$2
    
    if curl -s --head "$url" | head -n 1 | grep -q "200 OK"; then
        print_success "$name is accessible at $url"
    else
        print_warning "$name may not be fully deployed yet at $url"
    fi
}

sleep 30 # Wait for DNS propagation

check_site "https://fameduconnect.com" "Landing Page"
check_site "https://fameduconnect.app" "Frontend App"
check_site "https://fameduconnect.xyz" "Admin Dashboard"

print_success "🎉 FamEduConnect deployment completed!"
print_status "Next steps:"
echo "1. Configure DNS records with your domain provider"
echo "2. Set up monitoring with Sentry/LogRocket"
echo "3. Configure analytics (GA4, Amplitude)"
echo "4. Test all functionality in production"
echo "5. Set up automated backups"

print_status "Admin Credentials:"
echo "Username: <EMAIL>"
echo "Password: [Generated during first setup]"
echo "Access: https://fameduconnect.xyz"

print_status "© 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc. – NPO: 2023-001341848"
print_status "Creative Director: Na'imah Barnes"