import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PaperAirplaneIcon,
  PaperClipIcon,
  FaceSmileIcon,
  MicrophoneIcon,
  StopIcon
} from '@heroicons/react/24/outline';

const MessageInput = ({ 
  onSendMessage, 
  onFileUpload, 
  onVoiceMessage,
  disabled = false,
  placeholder = "Type your message..."
}) => {
  const [messageText, setMessageText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const textareaRef = useRef(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (messageText.trim() && !disabled) {
      onSendMessage({
        content: messageText.trim(),
        messageType: 'text'
      });
      setMessageText('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleTextareaChange = (e) => {
    setMessageText(e.target.value);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };

  const handleFileClick = () => {
    if (onFileUpload) {
      onFileUpload();
    }
  };

  const handleVoiceClick = () => {
    if (isRecording) {
      setIsRecording(false);
      if (onVoiceMessage) {
        onVoiceMessage(null); // Cancel recording
      }
    } else {
      setIsRecording(true);
      if (onVoiceMessage) {
        onVoiceMessage('start');
      }
    }
  };

  return (
    <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <form onSubmit={handleSubmit} className="flex items-end space-x-2">
        {/* File Upload Button */}
        <button
          type="button"
          onClick={handleFileClick}
          disabled={disabled}
          className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          title="Attach file"
        >
          <PaperClipIcon className="h-5 w-5" />
        </button>

        {/* Message Input */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={messageText}
            onChange={handleTextareaChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className="w-full px-4 py-2 pr-20 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ minHeight: '40px', maxHeight: '120px' }}
          />
          
          {/* Emoji Button */}
          <button
            type="button"
            disabled={disabled}
            className="absolute right-12 top-1/2 transform -translate-y-1/2 p-1 rounded-md text-gray-400 hover:text-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Add emoji"
          >
            <FaceSmileIcon className="h-5 w-5" />
          </button>

          {/* Voice Recording Button */}
          <button
            type="button"
            onClick={handleVoiceClick}
            disabled={disabled}
            className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
              isRecording 
                ? 'text-red-500 hover:text-red-600' 
                : 'text-gray-400 hover:text-gray-500'
            }`}
            title={isRecording ? 'Stop recording' : 'Record voice message'}
          >
            {isRecording ? (
              <StopIcon className="h-5 w-5" />
            ) : (
              <MicrophoneIcon className="h-5 w-5" />
            )}
          </button>
        </div>

        {/* Send Button */}
        <button
          type="submit"
          disabled={!messageText.trim() || disabled}
          className="p-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Send message"
        >
          <PaperAirplaneIcon className="h-5 w-5" />
        </button>
      </form>

      {/* Recording Indicator */}
      <AnimatePresence>
        {isRecording && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="mt-2 flex items-center justify-center space-x-2 text-red-500"
          >
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
            <span className="text-sm font-medium">Recording...</span>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MessageInput;