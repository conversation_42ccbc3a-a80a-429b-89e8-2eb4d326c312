# FamEduConnect - Play Store Deployment Script

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "FamEduConnect - Play Store Deployment" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor Yellow
Write-Host ""

try {
    $easVersion = eas --version 2>$null
    Write-Host "[✓] EAS CLI found: $easVersion" -ForegroundColor Green
} catch {
    Write-Host "[✗] EAS CLI not found!" -ForegroundColor Red
    Write-Host "Please install it first: npm install -g @expo/eas-cli" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Current configuration:" -ForegroundColor Blue
Write-Host "- App Name: FamEduConnect" -ForegroundColor White
Write-Host "- Package: com.fameduconnect.app" -ForegroundColor White
Write-Host "- Version: 1.0.0" -ForegroundColor White
Write-Host ""

# Menu
do {
    Write-Host "What would you like to do?" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. Build for Play Store (AAB)" -ForegroundColor White
    Write-Host "2. Submit to Play Store" -ForegroundColor White
    Write-Host "3. Build and Submit" -ForegroundColor White
    Write-Host "4. Check build status" -ForegroundColor White
    Write-Host "5. Exit" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Enter your choice (1-5)"
    
    switch ($choice) {
        "1" {
            Write-Host ""
            Write-Host "[1/1] Building Android App Bundle for Play Store..." -ForegroundColor Yellow
            Write-Host "This may take 10-15 minutes..." -ForegroundColor Blue
            Write-Host ""
            
            try {
                eas build --platform android --profile production
                Write-Host ""
                Write-Host "✅ Build completed successfully!" -ForegroundColor Green
                Write-Host "Check your Expo dashboard for the download link." -ForegroundColor Blue
            } catch {
                Write-Host "ERROR: Build failed!" -ForegroundColor Red
                Write-Host $_.Exception.Message -ForegroundColor Red
            }
            break
        }
        
        "2" {
            Write-Host ""
            Write-Host "[1/1] Submitting to Play Store..." -ForegroundColor Yellow
            Write-Host "Make sure you have google-service-account.json configured!" -ForegroundColor Blue
            Write-Host ""
            
            try {
                eas submit --platform android --profile production
                Write-Host ""
                Write-Host "✅ Submission completed!" -ForegroundColor Green
                Write-Host "Check Google Play Console for review status." -ForegroundColor Blue
            } catch {
                Write-Host "ERROR: Submission failed!" -ForegroundColor Red
                Write-Host "Make sure your google-service-account.json is configured." -ForegroundColor Yellow
            }
            break
        }
        
        "3" {
            Write-Host ""
            Write-Host "[1/2] Building Android App Bundle..." -ForegroundColor Yellow
            Write-Host "This may take 10-15 minutes..." -ForegroundColor Blue
            Write-Host ""
            
            try {
                eas build --platform android --profile production
                
                Write-Host ""
                Write-Host "[2/2] Submitting to Play Store..." -ForegroundColor Yellow
                eas submit --platform android --profile production
                
                Write-Host ""
                Write-Host "✅ Build and submission completed!" -ForegroundColor Green
            } catch {
                Write-Host "ERROR: Process failed!" -ForegroundColor Red
                Write-Host $_.Exception.Message -ForegroundColor Red
            }
            break
        }
        
        "4" {
            Write-Host ""
            Write-Host "Checking build status..." -ForegroundColor Yellow
            try {
                eas build:list
            } catch {
                Write-Host "ERROR: Could not fetch build status" -ForegroundColor Red
            }
            break
        }
        
        "5" {
            Write-Host "Goodbye! 👋" -ForegroundColor Green
            exit 0
        }
        
        default {
            Write-Host "Invalid choice. Please try again." -ForegroundColor Red
        }
    }
    
    if ($choice -ne "5") {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "Next Steps:" -ForegroundColor Yellow
        Write-Host "1. Check your Expo dashboard for build status" -ForegroundColor White
        Write-Host "2. Complete your Play Store listing" -ForegroundColor White
        Write-Host "3. Add screenshots and descriptions" -ForegroundColor White
        Write-Host "4. Submit for review" -ForegroundColor White
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        
        $continue = Read-Host "Press Enter to continue or 'q' to quit"
        if ($continue -eq 'q') { break }
    }
    
} while ($choice -ne "5")

Write-Host ""
Write-Host "Happy deploying! 🚀" -ForegroundColor Green