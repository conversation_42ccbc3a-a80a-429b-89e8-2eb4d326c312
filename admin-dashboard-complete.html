<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FamEduConnect - Complete Admin Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #2d3748;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .admin-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .admin-badge {
            background: #e53e3e;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        /* Sidebar */
        .container {
            display: flex;
            min-height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 280px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 2rem 0;
        }
        
        .nav-section {
            margin-bottom: 2rem;
        }
        
        .nav-section-title {
            padding: 0 2rem;
            font-size: 0.8rem;
            font-weight: bold;
            color: #a0aec0;
            text-transform: uppercase;
            margin-bottom: 1rem;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 2rem;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background: #667eea;
            color: white;
        }
        
        .nav-icon {
            font-size: 1.2rem;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            padding: 2rem;
        }
        
        .dashboard-header {
            margin-bottom: 2rem;
        }
        
        .dashboard-title {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .dashboard-subtitle {
            color: #718096;
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .stat-card.warning {
            border-left-color: #ed8936;
        }
        
        .stat-card.success {
            border-left-color: #48bb78;
        }
        
        .stat-card.danger {
            border-left-color: #e53e3e;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2d3748;
        }
        
        .stat-label {
            color: #718096;
            font-size: 0.9rem;
        }
        
        .stat-change {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }
        
        .stat-change.positive {
            color: #48bb78;
        }
        
        .stat-change.negative {
            color: #e53e3e;
        }
        
        /* Tables */
        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .table-header {
            background: #f7fafc;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-active {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .status-pending {
            background: #feebc8;
            color: #744210;
        }
        
        .status-blocked {
            background: #fed7d7;
            color: #742a2a;
        }
        
        /* Buttons */
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-sm {
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
        }
        
        .btn-danger {
            background: #e53e3e;
        }
        
        .btn-danger:hover {
            background: #c53030;
        }
        
        .btn-warning {
            background: #ed8936;
        }
        
        .btn-warning:hover {
            background: #dd6b20;
        }
        
        .btn-success {
            background: #48bb78;
        }
        
        .btn-success:hover {
            background: #38a169;
        }
        
        /* Charts placeholder */
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #718096;
            text-align: center;
        }
        
        /* Blockchain specific styles */
        .blockchain-hash {
            font-family: 'Courier New', monospace;
            background: #f7fafc;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 80%;
            max-width: 800px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem 2rem;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .close {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }
        
        .close:hover {
            opacity: 0.7;
        }
        
        .modal-body {
            padding: 2rem;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .detail-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .detail-label {
            font-weight: bold;
            color: #4a5568;
            margin-bottom: 0.5rem;
        }
        
        .detail-value {
            color: #2d3748;
            font-family: 'Courier New', monospace;
            background: white;
            padding: 0.5rem;
            border-radius: 4px;
            word-break: break-all;
        }
        
        .verification-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: bold;
            color: #48bb78;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                display: flex;
                overflow-x: auto;
                padding: 1rem 0;
            }
            
            .nav-section {
                display: flex;
                gap: 1rem;
                margin: 0;
            }
            
            .nav-item {
                white-space: nowrap;
                padding: 0.5rem 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="logo">🎓 FamEduConnect Admin</div>
        <div class="admin-info">
            <span class="admin-badge">ADMIN</span>
            <div>👤 Admin User</div>
        </div>
    </header>

    <div class="container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <div class="nav-section-title">Overview</div>
                <a href="#" class="nav-item active" onclick="showSection('dashboard')">
                    <span class="nav-icon">📊</span>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('analytics')">
                    <span class="nav-icon">📈</span>
                    <span>Advanced Analytics</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Management</div>
                <a href="#" class="nav-item" onclick="showSection('schools')">
                    <span class="nav-icon">🏫</span>
                    <span>School Administration</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('users')">
                    <span class="nav-icon">👥</span>
                    <span>User Management</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('messages')">
                    <span class="nav-icon">💬</span>
                    <span>Content Moderation</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Reports & Monitoring</div>
                <a href="#" class="nav-item" onclick="showSection('reports')">
                    <span class="nav-icon">📋</span>
                    <span>Usage Reports</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('video-logs')">
                    <span class="nav-icon">📹</span>
                    <span>Video Call Monitoring</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('audit')">
                    <span class="nav-icon">⛓️</span>
                    <span>Blockchain Audit</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">System</div>
                <a href="#" class="nav-item" onclick="showSection('settings')">
                    <span class="nav-icon">⚙️</span>
                    <span>Settings</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('compliance')">
                    <span class="nav-icon">🛡️</span>
                    <span>Compliance</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="section">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">System Overview</h1>
                    <p class="dashboard-subtitle">Monitor FamEduConnect platform performance and usage</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">Total Users</div>
                        <div class="stat-change positive">↗ +12% this month</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">156</div>
                        <div class="stat-label">Active Schools</div>
                        <div class="stat-change positive">↗ +3 new schools</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">Messages Today</div>
                        <div class="stat-change positive">↗ +8% from yesterday</div>
                    </div>
                    <div class="stat-card danger">
                        <div class="stat-number">23</div>
                        <div class="stat-label">Flagged Content</div>
                        <div class="stat-change negative">↗ Needs review</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div>📊 Real-time Platform Analytics<br><small>Live data visualization showing user activity, system performance, and engagement metrics</small></div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Recent System Activity</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>User</th>
                                <th>Action</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>3:45 PM</td>
                                <td>Sarah Johnson (Parent)</td>
                                <td>Started video call with Ms. Rodriguez</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td><button class="btn btn-sm" onclick="monitorActivity('Sarah Johnson', 'video call')">Monitor</button></td>
                            </tr>
                            <tr>
                                <td>3:42 PM</td>
                                <td>Lincoln Elementary</td>
                                <td>New teacher registration pending</td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td><button class="btn btn-sm btn-success" onclick="approveRegistration('Lincoln Elementary teacher')">Approve</button></td>
                            </tr>
                            <tr>
                                <td>3:40 PM</td>
                                <td>System</td>
                                <td>Automated security scan completed</td>
                                <td><span class="status-badge status-active">Success</span></td>
                                <td><button class="btn btn-sm" onclick="viewSystemReport('security scan')">View Report</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>  
          <!-- School Administration Interface -->
            <div id="schools" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">School Administration</h1>
                    <p class="dashboard-subtitle">Manage schools, districts, and educational institutions</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">Active Schools</div>
                        <div class="stat-change positive">↗ +3 this month</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">12</div>
                        <div class="stat-label">School Districts</div>
                        <div class="stat-change positive">↗ +1 new district</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">8</div>
                        <div class="stat-label">Pending Approvals</div>
                        <div class="stat-change">Needs review</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">45,678</div>
                        <div class="stat-label">Total Students</div>
                        <div class="stat-change positive">↗ +5% enrollment</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">School Directory</h3>
                        <button class="btn" onclick="addNewSchool()">+ Add New School</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>School Name</th>
                                <th>District</th>
                                <th>Students</th>
                                <th>Teachers</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Lincoln Elementary School</td>
                                <td>Metro District</td>
                                <td>485</td>
                                <td>24</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>
                                    <button class="btn btn-sm">Edit</button>
                                    <button class="btn btn-sm btn-warning">Settings</button>
                                    <button class="btn btn-sm btn-success">Reports</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Washington Middle School</td>
                                <td>Metro District</td>
                                <td>672</td>
                                <td>38</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>
                                    <button class="btn btn-sm">Edit</button>
                                    <button class="btn btn-sm btn-warning">Settings</button>
                                    <button class="btn btn-sm btn-success">Reports</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Roosevelt High School</td>
                                <td>Metro District</td>
                                <td>1,234</td>
                                <td>67</td>
                                <td><span class="status-badge status-pending">Setup</span></td>
                                <td>
                                    <button class="btn btn-sm btn-success">Complete Setup</button>
                                    <button class="btn btn-sm btn-danger">Remove</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Jefferson Elementary</td>
                                <td>East District</td>
                                <td>356</td>
                                <td>18</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>
                                    <button class="btn btn-sm">Edit</button>
                                    <button class="btn btn-sm btn-warning">Settings</button>
                                    <button class="btn btn-sm btn-success">Reports</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="chart-container">
                    <div>🏫 School Performance Analytics<br><small>Enrollment trends, engagement metrics, and performance comparisons across schools</small></div>
                </div>
            </div>

            <!-- Content Moderation Interface -->
            <div id="messages" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Content Moderation</h1>
                    <p class="dashboard-subtitle">Monitor and moderate platform communications for safety and compliance</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card danger">
                        <div class="stat-number">23</div>
                        <div class="stat-label">Flagged Messages</div>
                        <div class="stat-change">Requires immediate review</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">156</div>
                        <div class="stat-label">Auto-Moderated</div>
                        <div class="stat-change">Last 24 hours</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">99.2%</div>
                        <div class="stat-label">Clean Content Rate</div>
                        <div class="stat-change positive">↗ Improving quality</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">45,678</div>
                        <div class="stat-label">Messages Processed</div>
                        <div class="stat-change">This week</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Flagged Content Review Queue</h3>
                        <button class="btn btn-warning" onclick="showBulkActions()">Bulk Actions</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>From</th>
                                <th>To</th>
                                <th>Content Preview</th>
                                <th>Flag Reason</th>
                                <th>Severity</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>3:45 PM</td>
                                <td>John Parent</td>
                                <td>Ms. Smith (Teacher)</td>
                                <td>"This is completely unacceptable behavior from your class..."</td>
                                <td>Aggressive Language</td>
                                <td><span class="status-badge" style="background: #fed7d7; color: #742a2a;">High</span></td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="approveContent('John Parent', 'Ms. Smith', 'Aggressive Language')">Approve</button>
                                    <button class="btn btn-sm btn-danger" onclick="blockContent('John Parent', 'Ms. Smith', 'Aggressive Language')">Block</button>
                                    <button class="btn btn-sm btn-warning" onclick="editAndSend('John Parent', 'Ms. Smith', 'This is completely unacceptable behavior from your class...')">Edit & Send</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2:30 PM</td>
                                <td>Teacher Mike</td>
                                <td>Parent Group</td>
                                <td>"Let's meet at my house this weekend to discuss..."</td>
                                <td>Inappropriate Meeting Location</td>
                                <td><span class="status-badge" style="background: #feebc8; color: #744210;">Medium</span></td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="approveContent('Teacher Mike', 'Parent Group', 'Inappropriate Meeting Location')">Approve</button>
                                    <button class="btn btn-sm btn-danger" onclick="blockContent('Teacher Mike', 'Parent Group', 'Inappropriate Meeting Location')">Block</button>
                                    <button class="btn btn-sm btn-warning" onclick="suggestEdit('Teacher Mike', 'Parent Group', 'Let\\'s meet at my house this weekend to discuss...')">Suggest Edit</button>
                                </td>
                            </tr>
                            <tr>
                                <td>1:15 PM</td>
                                <td>Sarah Johnson</td>
                                <td>Principal Davis</td>
                                <td>"You can reach me at 555-1234 or <EMAIL>"</td>
                                <td>Personal Information Sharing</td>
                                <td><span class="status-badge" style="background: #e6fffa; color: #234e52;">Low</span></td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="approveContent('Sarah Johnson', 'Principal Davis', 'Personal Information Sharing')">Approve</button>
                                    <button class="btn btn-sm btn-danger" onclick="blockContent('Sarah Johnson', 'Principal Davis', 'Personal Information Sharing')">Block</button>
                                    <button class="btn btn-sm btn-warning" onclick="redactInfo('Sarah Johnson', 'Principal Davis', 'You can reach me at 555-1234 or <EMAIL>')">Redact Info</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="chart-container">
                    <div>🛡️ Content Moderation Analytics<br><small>Flagging trends, moderation effectiveness, and content safety metrics over time</small></div>
                </div>
            </div>

            <!-- Analytics and Reporting Interface -->
            <div id="reports" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Usage Reports & Analytics</h1>
                    <p class="dashboard-subtitle">Comprehensive platform usage and performance metrics</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">45,678</div>
                        <div class="stat-label">Total Messages</div>
                        <div class="stat-change positive">↗ +15% this week</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">Video Calls</div>
                        <div class="stat-change positive">↗ +22% this week</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">89.5%</div>
                        <div class="stat-label">User Satisfaction</div>
                        <div class="stat-change positive">↗ +2.3% improvement</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">67</div>
                        <div class="stat-label">Languages Used</div>
                        <div class="stat-change">Global reach expanding</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div class="chart-container">
                        <div>📈 User Engagement Trends<br><small>Daily/Weekly/Monthly active users and engagement patterns</small></div>
                    </div>
                    <div class="chart-container">
                        <div>🌍 Geographic Usage Distribution<br><small>Global user distribution and regional activity heatmap</small></div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Top Performing Schools by Engagement</h3>
                        <button class="btn" onclick="exportUsageReport()">Export Report</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>School</th>
                                <th>Active Users</th>
                                <th>Messages/Day</th>
                                <th>Video Calls/Week</th>
                                <th>Engagement Score</th>
                                <th>Growth Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Lincoln Elementary</td>
                                <td>485/500</td>
                                <td>234</td>
                                <td>45</td>
                                <td><span style="color: #48bb78; font-weight: bold;">97%</span></td>
                                <td><span style="color: #48bb78;">+12%</span></td>
                            </tr>
                            <tr>
                                <td>Washington Middle</td>
                                <td>672/710</td>
                                <td>456</td>
                                <td>67</td>
                                <td><span style="color: #48bb78; font-weight: bold;">94%</span></td>
                                <td><span style="color: #48bb78;">+8%</span></td>
                            </tr>
                            <tr>
                                <td>Roosevelt High</td>
                                <td>1,234/1,300</td>
                                <td>789</td>
                                <td>123</td>
                                <td><span style="color: #ed8936; font-weight: bold;">89%</span></td>
                                <td><span style="color: #48bb78;">+5%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Feature Usage Analytics</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Feature</th>
                                <th>Daily Active Users</th>
                                <th>Usage Frequency</th>
                                <th>Satisfaction Score</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>💬 Messaging</td>
                                <td>2,456</td>
                                <td>8.3 times/day</td>
                                <td>4.9/5</td>
                                <td><span style="color: #48bb78;">↗ +15%</span></td>
                            </tr>
                            <tr>
                                <td>📹 Video Calls</td>
                                <td>1,234</td>
                                <td>2.1 times/week</td>
                                <td>4.7/5</td>
                                <td><span style="color: #48bb78;">↗ +22%</span></td>
                            </tr>
                            <tr>
                                <td>📚 Grade Tracking</td>
                                <td>1,890</td>
                                <td>5.6 times/week</td>
                                <td>4.8/5</td>
                                <td><span style="color: #48bb78;">↗ +8%</span></td>
                            </tr>
                            <tr>
                                <td>🌍 Translation</td>
                                <td>567</td>
                                <td>3.2 times/day</td>
                                <td>4.6/5</td>
                                <td><span style="color: #48bb78;">↗ +35%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div> 
           <!-- Video Call Monitoring Interface -->
            <div id="video-logs" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Video Call Monitoring</h1>
                    <p class="dashboard-subtitle">Monitor video calls, recordings, and compliance in real-time</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">Total Calls This Month</div>
                        <div class="stat-change positive">↗ +18% from last month</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">45.6</div>
                        <div class="stat-label">Avg Call Duration (min)</div>
                        <div class="stat-change">Optimal engagement length</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">23</div>
                        <div class="stat-label">Active Calls Now</div>
                        <div class="stat-change">Live monitoring available</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">98.7%</div>
                        <div class="stat-label">Call Success Rate</div>
                        <div class="stat-change positive">↗ Excellent quality</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Live Video Calls</h3>
                        <button class="btn btn-warning" onclick="emergencyOverride()">Emergency Override</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Participants</th>
                                <th>School</th>
                                <th>Duration</th>
                                <th>Quality</th>
                                <th>Translation</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Sarah Johnson ↔ Ms. Rodriguez</td>
                                <td>Lincoln Elementary</td>
                                <td>12:34</td>
                                <td><span style="color: #48bb78; font-weight: bold;">HD</span></td>
                                <td>EN → ES</td>
                                <td>
                                    <button class="btn btn-sm" onclick="monitorCall('Sarah Johnson ↔ Ms. Rodriguez', '12:34')">Monitor</button>
                                    <button class="btn btn-sm btn-warning" onclick="joinCall('Sarah Johnson ↔ Ms. Rodriguez')">Join</button>
                                    <button class="btn btn-sm btn-danger" onclick="endCall('Sarah Johnson ↔ Ms. Rodriguez')">End Call</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Parent Group Meeting (8 participants)</td>
                                <td>Washington Middle</td>
                                <td>23:45</td>
                                <td><span style="color: #48bb78; font-weight: bold;">HD</span></td>
                                <td>Multi-language</td>
                                <td>
                                    <button class="btn btn-sm" onclick="monitorCall('Parent Group Meeting', '23:45')">Monitor</button>
                                    <button class="btn btn-sm btn-warning" onclick="joinCall('Parent Group Meeting')">Join</button>
                                    <button class="btn btn-sm btn-danger" onclick="endCall('Parent Group Meeting')">End Call</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Mike Thompson ↔ Principal Davis</td>
                                <td>Roosevelt High</td>
                                <td>8:12</td>
                                <td><span style="color: #ed8936; font-weight: bold;">SD</span></td>
                                <td>None</td>
                                <td>
                                    <button class="btn btn-sm" onclick="monitorCall('Mike Thompson ↔ Principal Davis', '8:12')">Monitor</button>
                                    <button class="btn btn-sm btn-warning" onclick="joinCall('Mike Thompson ↔ Principal Davis')">Join</button>
                                    <button class="btn btn-sm btn-danger" onclick="endCall('Mike Thompson ↔ Principal Davis')">End Call</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Recent Call History & Recordings</h3>
                        <button class="btn">Export Call Logs</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Date/Time</th>
                                <th>Participants</th>
                                <th>Duration</th>
                                <th>Recording</th>
                                <th>Transcript</th>
                                <th>Compliance</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Jan 21, 3:30 PM</td>
                                <td>John Smith ↔ Ms. Davis</td>
                                <td>23:45</td>
                                <td><span class="status-badge status-active">Available</span></td>
                                <td><span class="status-badge status-active">Generated</span></td>
                                <td><span class="status-badge status-active">✓ Compliant</span></td>
                                <td>
                                    <button class="btn btn-sm" onclick="viewCallRecording('John Smith ↔ Ms. Davis', '23:45')">View</button>
                                    <button class="btn btn-sm btn-warning" onclick="downloadCallRecording('John Smith ↔ Ms. Davis', '2025-01-21')">Download</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Jan 21, 2:15 PM</td>
                                <td>Maria Garcia ↔ Mr. Wilson</td>
                                <td>18:30</td>
                                <td><span class="status-badge status-active">Available</span></td>
                                <td><span class="status-badge status-active">Generated</span></td>
                                <td><span class="status-badge status-active">✓ Compliant</span></td>
                                <td>
                                    <button class="btn btn-sm" onclick="viewCallRecording('Maria Garcia ↔ Mr. Wilson', '18:30')">View</button>
                                    <button class="btn btn-sm btn-warning" onclick="downloadCallRecording('Maria Garcia ↔ Mr. Wilson', '2025-01-21')">Download</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Jan 21, 1:00 PM</td>
                                <td>Teacher Meeting (12 participants)</td>
                                <td>45:12</td>
                                <td><span class="status-badge status-pending">Processing</span></td>
                                <td><span class="status-badge status-pending">Processing</span></td>
                                <td><span class="status-badge status-pending">Review</span></td>
                                <td>
                                    <button class="btn btn-sm" disabled>Processing...</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="chart-container">
                    <div>📹 Video Call Analytics Dashboard<br><small>Call volume trends, quality metrics, duration analysis, and usage patterns</small></div>
                </div>
            </div>

            <!-- Blockchain Audit Trail Interface -->
            <div id="audit" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Blockchain Audit Trail</h1>
                    <p class="dashboard-subtitle">Immutable security and compliance audit logs with blockchain verification</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">156,789</div>
                        <div class="stat-label">Total Audit Entries</div>
                        <div class="stat-change">Blockchain verified</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Integrity Verified</div>
                        <div class="stat-change positive">✓ All blocks valid</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Tamper Attempts</div>
                        <div class="stat-change">Secure system</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2.3s</div>
                        <div class="stat-label">Avg Block Time</div>
                        <div class="stat-change">Optimal performance</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Recent Blockchain Transactions</h3>
                        <button class="btn" onclick="verifyBlockIntegrity()" id="verifyBtn">Verify Block Integrity</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Block Hash</th>
                                <th>Timestamp</th>
                                <th>Action Type</th>
                                <th>User</th>
                                <th>Verification Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="blockchain-hash">0x7a8b9c...def123</span></td>
                                <td>2025-01-21 15:45:23</td>
                                <td>User Login</td>
                                <td><EMAIL></td>
                                <td><span style="color: #48bb78; font-weight: bold;">✓ Verified</span></td>
                                <td><button class="btn btn-sm" onclick="viewBlockDetails('0x7a8b9c...def123', 'User Login', '<EMAIL>', '2025-01-21 15:45:23')">View Details</button></td>
                            </tr>
                            <tr>
                                <td><span class="blockchain-hash">0x6f7e8d...abc456</span></td>
                                <td>2025-01-21 15:44:12</td>
                                <td>Message Sent</td>
                                <td><EMAIL></td>
                                <td><span style="color: #48bb78; font-weight: bold;">✓ Verified</span></td>
                                <td><button class="btn btn-sm" onclick="viewBlockDetails('0x6f7e8d...abc456', 'Message Sent', '<EMAIL>', '2025-01-21 15:44:12')">View Details</button></td>
                            </tr>
                            <tr>
                                <td><span class="blockchain-hash">0x5e6d7c...789def</span></td>
                                <td>2025-01-21 15:43:01</td>
                                <td>Grade Updated</td>
                                <td><EMAIL></td>
                                <td><span style="color: #48bb78; font-weight: bold;">✓ Verified</span></td>
                                <td><button class="btn btn-sm" onclick="viewBlockDetails('0x5e6d7c...789def', 'Grade Updated', '<EMAIL>', '2025-01-21 15:43:01')">View Details</button></td>
                            </tr>
                            <tr>
                                <td><span class="blockchain-hash">0x4d5c6b...456abc</span></td>
                                <td>2025-01-21 15:42:45</td>
                                <td>Video Call Started</td>
                                <td><EMAIL></td>
                                <td><span style="color: #48bb78; font-weight: bold;">✓ Verified</span></td>
                                <td><button class="btn btn-sm" onclick="viewBlockDetails('0x4d5c6b...456abc', 'Video Call Started', '<EMAIL>', '2025-01-21 15:42:45')">View Details</button></td>
                            </tr>
                            <tr>
                                <td><span class="blockchain-hash">0x3c4b5a...123fed</span></td>
                                <td>2025-01-21 15:41:30</td>
                                <td>File Upload</td>
                                <td><EMAIL></td>
                                <td><span style="color: #48bb78; font-weight: bold;">✓ Verified</span></td>
                                <td><button class="btn btn-sm" onclick="viewBlockDetails('0x3c4b5a...123fed', 'File Upload', '<EMAIL>', '2025-01-21 15:41:30')">View Details</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Blockchain Network Status</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>Current Value</th>
                                <th>Status</th>
                                <th>Last Updated</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Network Hash Rate</td>
                                <td>1.2 TH/s</td>
                                <td><span class="status-badge status-active">Healthy</span></td>
                                <td>2 minutes ago</td>
                            </tr>
                            <tr>
                                <td>Block Height</td>
                                <td>156,789</td>
                                <td><span class="status-badge status-active">Synced</span></td>
                                <td>30 seconds ago</td>
                            </tr>
                            <tr>
                                <td>Consensus</td>
                                <td>100% Agreement</td>
                                <td><span class="status-badge status-active">Stable</span></td>
                                <td>1 minute ago</td>
                            </tr>
                            <tr>
                                <td>Network Latency</td>
                                <td>45ms</td>
                                <td><span class="status-badge status-active">Optimal</span></td>
                                <td>15 seconds ago</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="chart-container">
                    <div>⛓️ Blockchain Network Analytics<br><small>Network health, transaction volume, verification times, and consensus metrics</small></div>
                </div>
            </div>     
       <!-- Advanced Analytics Dashboard -->
            <div id="analytics" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Advanced Analytics Dashboard</h1>
                    <p class="dashboard-subtitle">Deep insights into platform performance, user behavior, and business intelligence</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">Monthly Active Users</div>
                        <div class="stat-change positive">↗ +12% growth rate</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">4.8/5</div>
                        <div class="stat-label">User Satisfaction</div>
                        <div class="stat-change positive">↗ +0.3 improvement</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">67</div>
                        <div class="stat-label">Languages Active</div>
                        <div class="stat-change">Global reach expanding</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">99.8%</div>
                        <div class="stat-label">System Uptime</div>
                        <div class="stat-change positive">↗ Excellent reliability</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div class="chart-container">
                        <div>📈 User Growth & Retention<br><small>Monthly user acquisition, retention rates, and churn analysis</small></div>
                    </div>
                    <div class="chart-container">
                        <div>🌍 Global Usage Heatmap<br><small>Geographic distribution, time zone usage patterns, and regional trends</small></div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div class="chart-container">
                        <div>💬 Communication Patterns<br><small>Message volume, response times, and engagement quality metrics</small></div>
                    </div>
                    <div class="chart-container">
                        <div>📚 Educational Impact<br><small>Grade improvements, parent engagement correlation, and learning outcomes</small></div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Feature Adoption & Usage Analytics</h3>
                        <button class="btn">Generate Report</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Feature</th>
                                <th>Daily Active Users</th>
                                <th>Usage Frequency</th>
                                <th>Satisfaction Score</th>
                                <th>Adoption Rate</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>💬 Real-time Messaging</td>
                                <td>2,456</td>
                                <td>8.3 times/day</td>
                                <td>4.9/5</td>
                                <td>86.3%</td>
                                <td><span style="color: #48bb78;">↗ +15%</span></td>
                            </tr>
                            <tr>
                                <td>📹 Video Conferencing</td>
                                <td>1,234</td>
                                <td>2.1 times/week</td>
                                <td>4.7/5</td>
                                <td>43.4%</td>
                                <td><span style="color: #48bb78;">↗ +22%</span></td>
                            </tr>
                            <tr>
                                <td>📚 Grade Tracking</td>
                                <td>1,890</td>
                                <td>5.6 times/week</td>
                                <td>4.8/5</td>
                                <td>66.4%</td>
                                <td><span style="color: #48bb78;">↗ +8%</span></td>
                            </tr>
                            <tr>
                                <td>🌍 Language Translation</td>
                                <td>567</td>
                                <td>3.2 times/day</td>
                                <td>4.6/5</td>
                                <td>19.9%</td>
                                <td><span style="color: #48bb78;">↗ +35%</span></td>
                            </tr>
                            <tr>
                                <td>📅 Calendar Integration</td>
                                <td>1,456</td>
                                <td>4.1 times/week</td>
                                <td>4.5/5</td>
                                <td>51.2%</td>
                                <td><span style="color: #ed8936;">→ Stable</span></td>
                            </tr>
                            <tr>
                                <td>📁 File Sharing</td>
                                <td>789</td>
                                <td>1.8 times/week</td>
                                <td>4.4/5</td>
                                <td>27.7%</td>
                                <td><span style="color: #48bb78;">↗ +12%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Language Usage & Translation Analytics</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Language</th>
                                <th>Active Users</th>
                                <th>Messages Sent</th>
                                <th>Translations Requested</th>
                                <th>Video Calls</th>
                                <th>Growth Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>🇺🇸 English</td>
                                <td>1,456</td>
                                <td>2.1M</td>
                                <td>45K</td>
                                <td>567</td>
                                <td><span style="color: #48bb78;">+8%</span></td>
                            </tr>
                            <tr>
                                <td>🇪🇸 Spanish</td>
                                <td>678</td>
                                <td>890K</td>
                                <td>123K</td>
                                <td>234</td>
                                <td><span style="color: #48bb78;">+23%</span></td>
                            </tr>
                            <tr>
                                <td>🇸🇦 Arabic</td>
                                <td>345</td>
                                <td>456K</td>
                                <td>89K</td>
                                <td>123</td>
                                <td><span style="color: #48bb78;">+45%</span></td>
                            </tr>
                            <tr>
                                <td>🇫🇷 French</td>
                                <td>234</td>
                                <td>312K</td>
                                <td>67K</td>
                                <td>89</td>
                                <td><span style="color: #48bb78;">+12%</span></td>
                            </tr>
                            <tr>
                                <td>🇨🇳 Chinese</td>
                                <td>134</td>
                                <td>178K</td>
                                <td>34K</td>
                                <td>45</td>
                                <td><span style="color: #48bb78;">+67%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Performance & Technical Metrics</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>Current Value</th>
                                <th>Target</th>
                                <th>Status</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Average Response Time</td>
                                <td>245ms</td>
                                <td>&lt; 500ms</td>
                                <td><span class="status-badge status-active">Excellent</span></td>
                                <td><span style="color: #48bb78;">↗ Improving</span></td>
                            </tr>
                            <tr>
                                <td>System Uptime</td>
                                <td>99.8%</td>
                                <td>&gt; 99.5%</td>
                                <td><span class="status-badge status-active">Target Met</span></td>
                                <td><span style="color: #48bb78;">↗ Stable</span></td>
                            </tr>
                            <tr>
                                <td>Error Rate</td>
                                <td>0.02%</td>
                                <td>&lt; 0.1%</td>
                                <td><span class="status-badge status-active">Excellent</span></td>
                                <td><span style="color: #48bb78;">↘ Decreasing</span></td>
                            </tr>
                            <tr>
                                <td>Database Query Time</td>
                                <td>12ms</td>
                                <td>&lt; 50ms</td>
                                <td><span class="status-badge status-active">Optimal</span></td>
                                <td><span style="color: #48bb78;">→ Stable</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- User Management Section -->
            <div id="users" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">User Management</h1>
                    <p class="dashboard-subtitle">Manage parents, teachers, and student accounts across all schools</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">1,456</div>
                        <div class="stat-label">Parents</div>
                        <div class="stat-change positive">↗ +45 this week</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">234</div>
                        <div class="stat-label">Teachers</div>
                        <div class="stat-change positive">↗ +8 this month</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">23</div>
                        <div class="stat-label">Pending Approvals</div>
                        <div class="stat-change">Needs review</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">Total Active Users</div>
                        <div class="stat-change positive">↗ +12% growth</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">User Accounts</h3>
                        <button class="btn" onclick="addNewUser()">+ Add New User</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Role</th>
                                <th>School</th>
                                <th>Status</th>
                                <th>Last Active</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Sarah Johnson</td>
                                <td>Parent</td>
                                <td>Lincoln Elementary</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>2 minutes ago</td>
                                <td>
                                    <button class="btn btn-sm" onclick="editUser('Sarah Johnson', 'Parent')">Edit</button>
                                    <button class="btn btn-sm btn-warning" onclick="suspendUser('Sarah Johnson', 'Parent')">Suspend</button>
                                    <button class="btn btn-sm btn-danger" onclick="blockUser('Sarah Johnson', 'Parent')">Block</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Ms. Rodriguez</td>
                                <td>Teacher</td>
                                <td>Lincoln Elementary</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>5 minutes ago</td>
                                <td>
                                    <button class="btn btn-sm" onclick="editUser('Ms. Rodriguez', 'Teacher')">Edit</button>
                                    <button class="btn btn-sm btn-warning" onclick="suspendUser('Ms. Rodriguez', 'Teacher')">Suspend</button>
                                    <button class="btn btn-sm btn-danger" onclick="blockUser('Ms. Rodriguez', 'Teacher')">Block</button>
                                </td>
                            </tr>
                            <tr>
                                <td>John Smith</td>
                                <td>Parent</td>
                                <td>Washington Middle</td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td>Never</td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="approveUser('John Smith', 'Parent')">Approve</button>
                                    <button class="btn btn-sm btn-danger" onclick="rejectUser('John Smith', 'Parent')">Reject</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- System Settings -->
            <div id="settings" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">System Settings</h1>
                    <p class="dashboard-subtitle">Configure platform settings and system preferences</p>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div class="data-table">
                        <div class="table-header">
                            <h3 class="table-title">Security Settings</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Two-Factor Authentication</label>
                                <button class="btn btn-sm btn-success" id="twoFactorBtn" onclick="toggleTwoFactor()">✓ Enabled</button>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Session Timeout</label>
                                <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;" onchange="updateSessionTimeout(this.value)">
                                    <option>30 minutes</option>
                                    <option selected>1 hour</option>
                                    <option>2 hours</option>
                                    <option>4 hours</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Password Policy</label>
                                <button class="btn btn-sm btn-warning" onclick="configurePasswordPolicy()">Configure Policy</button>
                            </div>
                        </div>
                    </div>

                    <div class="data-table">
                        <div class="table-header">
                            <h3 class="table-title">Platform Settings</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Default Language</label>
                                <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                    <option selected>English</option>
                                    <option>Spanish</option>
                                    <option>French</option>
                                    <option>Arabic</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Time Zone</label>
                                <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                    <option selected>UTC-5 (Eastern)</option>
                                    <option>UTC-8 (Pacific)</option>
                                    <option>UTC+0 (GMT)</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Maintenance Mode</label>
                                <button class="btn btn-sm btn-danger" id="maintenanceBtn" onclick="toggleMaintenanceMode()">Disabled</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compliance Dashboard -->
            <div id="compliance" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Compliance Dashboard</h1>
                    <p class="dashboard-subtitle">FERPA, HIPAA, GDPR compliance monitoring and reporting</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card success">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">FERPA Compliance</div>
                        <div class="stat-change positive">✓ Fully compliant</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">HIPAA Compliance</div>
                        <div class="stat-change positive">✓ Fully compliant</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">GDPR Compliance</div>
                        <div class="stat-change positive">✓ Fully compliant</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Compliance Issues</div>
                        <div class="stat-change positive">✓ No issues detected</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Compliance Audit History</h3>
                        <button class="btn" onclick="scheduleNewAudit()">Schedule New Audit</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Regulation</th>
                                <th>Audit Type</th>
                                <th>Result</th>
                                <th>Actions Taken</th>
                                <th>Report</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2025-01-15</td>
                                <td>FERPA</td>
                                <td>Quarterly Review</td>
                                <td><span class="status-badge status-active">Passed</span></td>
                                <td>None required</td>
                                <td><button class="btn btn-sm" onclick="downloadComplianceReport('FERPA', '2025-01-15')">Download PDF</button></td>
                            </tr>
                            <tr>
                                <td>2025-01-10</td>
                                <td>GDPR</td>
                                <td>Data Processing Audit</td>
                                <td><span class="status-badge status-active">Passed</span></td>
                                <td>Updated privacy policy</td>
                                <td><button class="btn btn-sm" onclick="downloadComplianceReport('GDPR', '2025-01-10')">Download PDF</button></td>
                            </tr>
                            <tr>
                                <td>2025-01-05</td>
                                <td>HIPAA</td>
                                <td>Security Assessment</td>
                                <td><span class="status-badge status-active">Passed</span></td>
                                <td>Enhanced encryption protocols</td>
                                <td><button class="btn btn-sm" onclick="downloadComplianceReport('HIPAA', '2025-01-05')">Download PDF</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Blockchain Details Modal -->
    <div id="blockDetailsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">⛓️ Blockchain Transaction Details</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="detail-grid">
                    <div class="detail-section">
                        <div class="detail-label">Block Hash</div>
                        <div class="detail-value" id="modal-hash">Loading...</div>
                    </div>
                    <div class="detail-section">
                        <div class="detail-label">Timestamp</div>
                        <div class="detail-value" id="modal-timestamp">Loading...</div>
                    </div>
                    <div class="detail-section">
                        <div class="detail-label">Action Type</div>
                        <div class="detail-value" id="modal-action">Loading...</div>
                    </div>
                    <div class="detail-section">
                        <div class="detail-label">User</div>
                        <div class="detail-value" id="modal-user">Loading...</div>
                    </div>
                </div>
                
                <div class="detail-section" style="margin-bottom: 1.5rem;">
                    <div class="detail-label">Verification Status</div>
                    <div class="verification-status">
                        <span>✓</span>
                        <span>Cryptographically Verified</span>
                    </div>
                </div>
                
                <div class="detail-section" style="margin-bottom: 1.5rem;">
                    <div class="detail-label">Full Block Hash</div>
                    <div class="detail-value" id="modal-full-hash">0x7a8b9cdef123456789abcdef0123456789abcdef0123456789abcdef0123456789</div>
                </div>
                
                <div class="detail-section" style="margin-bottom: 1.5rem;">
                    <div class="detail-label">Previous Block Hash</div>
                    <div class="detail-value">0x6f7e8dabc456789def0123456789abcdef0123456789abcdef0123456789abcdef</div>
                </div>
                
                <div class="detail-section" style="margin-bottom: 1.5rem;">
                    <div class="detail-label">Merkle Root</div>
                    <div class="detail-value">0x5e6d7c789def0123456789abcdef0123456789abcdef0123456789abcdef012345</div>
                </div>
                
                <div class="detail-grid">
                    <div class="detail-section">
                        <div class="detail-label">Block Height</div>
                        <div class="detail-value">156,789</div>
                    </div>
                    <div class="detail-section">
                        <div class="detail-label">Confirmations</div>
                        <div class="detail-value">1,247</div>
                    </div>
                    <div class="detail-section">
                        <div class="detail-label">Gas Used</div>
                        <div class="detail-value">21,000</div>
                    </div>
                    <div class="detail-section">
                        <div class="detail-label">Transaction Fee</div>
                        <div class="detail-value">0.0001 ETH</div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <div class="detail-label">Compliance Notes</div>
                    <div class="detail-value">
                        ✓ FERPA Compliant - Educational record access logged<br>
                        ✓ GDPR Compliant - User consent verified<br>
                        ✓ HIPAA Ready - No health information disclosed
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });
            
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).style.display = 'block';
            
            // Add active class to clicked nav item
            event.target.closest('.nav-item').classList.add('active');
        }
        
        function viewBlockDetails(hash, action, user, timestamp) {
            // Update modal content with the specific transaction details
            document.getElementById('modal-hash').textContent = hash;
            document.getElementById('modal-timestamp').textContent = timestamp;
            document.getElementById('modal-action').textContent = action;
            document.getElementById('modal-user').textContent = user;
            
            // Generate a realistic full hash based on the short hash
            const fullHash = hash.replace('...', '') + 'def123456789abcdef0123456789abcdef0123456789abcdef0123456789';
            document.getElementById('modal-full-hash').textContent = fullHash;
            
            // Show the modal
            document.getElementById('blockDetailsModal').style.display = 'block';
        }
        
        function verifyBlockIntegrity() {
            const btn = document.getElementById('verifyBtn');
            const originalText = btn.textContent;
            
            // Start verification process
            btn.textContent = 'Verifying...';
            btn.disabled = true;
            btn.style.background = '#ed8936';
            
            // Simulate verification steps
            const steps = [
                'Connecting to blockchain network...',
                'Retrieving block headers...',
                'Verifying cryptographic hashes...',
                'Checking consensus mechanisms...',
                'Validating transaction signatures...',
                'Confirming block integrity...',
                'Verification complete!'
            ];
            
            let currentStep = 0;
            
            const verificationInterval = setInterval(() => {
                if (currentStep < steps.length - 1) {
                    btn.textContent = steps[currentStep];
                    currentStep++;
                } else {
                    clearInterval(verificationInterval);
                    
                    // Show success state
                    btn.textContent = '✓ Integrity Verified';
                    btn.style.background = '#48bb78';
                    
                    // Show verification results modal
                    showVerificationResults();
                    
                    // Reset button after 3 seconds
                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.disabled = false;
                        btn.style.background = '#667eea';
                    }, 3000);
                }
            }, 800);
        }
        
        function showVerificationResults() {
            // Create and show verification results modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">⛓️ Blockchain Integrity Verification Results</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div style="text-align: center; margin-bottom: 2rem;">
                            <div style="font-size: 4rem; color: #48bb78; margin-bottom: 1rem;">✅</div>
                            <h3 style="color: #48bb78; margin-bottom: 0.5rem;">Verification Successful</h3>
                            <p style="color: #718096;">All blockchain blocks have been verified and are cryptographically secure</p>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-section">
                                <div class="detail-label">Blocks Verified</div>
                                <div class="detail-value">156,789</div>
                            </div>
                            <div class="detail-section">
                                <div class="detail-label">Hash Verification</div>
                                <div class="detail-value" style="color: #48bb78;">✓ 100% Valid</div>
                            </div>
                            <div class="detail-section">
                                <div class="detail-label">Consensus Check</div>
                                <div class="detail-value" style="color: #48bb78;">✓ Unanimous</div>
                            </div>
                            <div class="detail-section">
                                <div class="detail-label">Tamper Detection</div>
                                <div class="detail-value" style="color: #48bb78;">✓ No Issues</div>
                            </div>
                        </div>
                        
                        <div class="detail-section" style="margin-top: 1.5rem;">
                            <div class="detail-label">Verification Summary</div>
                            <div style="background: white; padding: 1rem; border-radius: 4px; color: #2d3748;">
                                <p><strong>Network Status:</strong> Healthy and secure</p>
                                <p><strong>Last Block:</strong> 0x7a8b9c...def123 (verified)</p>
                                <p><strong>Chain Integrity:</strong> 100% validated</p>
                                <p><strong>Consensus:</strong> All nodes in agreement</p>
                                <p><strong>Security Level:</strong> Military-grade encryption</p>
                            </div>
                        </div>
                        
                        <div class="detail-section" style="margin-top: 1.5rem;">
                            <div class="detail-label">Compliance Status</div>
                            <div style="background: white; padding: 1rem; border-radius: 4px; color: #2d3748;">
                                <p style="color: #48bb78;"><strong>✓ FERPA Compliant:</strong> Educational records properly secured</p>
                                <p style="color: #48bb78;"><strong>✓ HIPAA Ready:</strong> Health information protection verified</p>
                                <p style="color: #48bb78;"><strong>✓ GDPR Compliant:</strong> Data protection standards met</p>
                                <p style="color: #48bb78;"><strong>✓ SOX Compliant:</strong> Financial controls verified</p>
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn" onclick="this.closest('.modal').remove()">Close Report</button>
                            <button class="btn btn-success" style="margin-left: 1rem;">Download Certificate</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Auto-remove modal after 30 seconds
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.remove();
                }
            }, 30000);
        }
        
        // Settings Section Functions
        function toggleTwoFactor() {
            const btn = document.getElementById('twoFactorBtn');
            const isEnabled = btn.textContent.includes('Enabled');
            
            if (isEnabled) {
                // Disable 2FA
                btn.textContent = '❌ Disabled';
                btn.className = 'btn btn-sm btn-danger';
                showNotification('Two-Factor Authentication has been disabled', 'warning');
            } else {
                // Enable 2FA
                btn.textContent = '✓ Enabled';
                btn.className = 'btn btn-sm btn-success';
                showNotification('Two-Factor Authentication has been enabled', 'success');
            }
        }
        
        function configurePasswordPolicy() {
            // Create password policy configuration modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">🔐 Password Policy Configuration</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Minimum Password Length</label>
                            <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option>6 characters</option>
                                <option>8 characters</option>
                                <option selected>12 characters</option>
                                <option>16 characters</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Password Requirements</label>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Uppercase letters (A-Z)
                                </label>
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Lowercase letters (a-z)
                                </label>
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Numbers (0-9)
                                </label>
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Special characters (!@#$%^&*)
                                </label>
                                <label style="display: flex; align-items: center;">
                                    <input type="checkbox" style="margin-right: 0.5rem;"> No dictionary words
                                </label>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Password Expiration</label>
                            <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option>Never</option>
                                <option>30 days</option>
                                <option selected>90 days</option>
                                <option>180 days</option>
                                <option>365 days</option>
                            </select>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-success" onclick="savePasswordPolicy(); this.closest('.modal').remove();">Save Policy</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function savePasswordPolicy() {
            showNotification('Password policy has been updated successfully', 'success');
        }
        
        function updateSessionTimeout(value) {
            showNotification(`Session timeout updated to ${value}`, 'info');
        }
        
        function toggleMaintenanceMode() {
            const btn = document.getElementById('maintenanceBtn');
            const isDisabled = btn.textContent.includes('Disabled');
            
            if (isDisabled) {
                // Enable maintenance mode
                btn.textContent = '🔧 Enabled';
                btn.className = 'btn btn-sm btn-warning';
                showNotification('Maintenance mode has been enabled - Users will see maintenance page', 'warning');
            } else {
                // Disable maintenance mode
                btn.textContent = 'Disabled';
                btn.className = 'btn btn-sm btn-danger';
                showNotification('Maintenance mode has been disabled - Platform is now live', 'success');
            }
        }
        
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 2000;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                transform: translateX(400px);
                transition: transform 0.3s ease;
            `;
            
            // Set background color based on type
            switch(type) {
                case 'success':
                    notification.style.background = '#48bb78';
                    break;
                case 'warning':
                    notification.style.background = '#ed8936';
                    break;
                case 'error':
                    notification.style.background = '#e53e3e';
                    break;
                default:
                    notification.style.background = '#667eea';
            }
            
            notification.textContent = message;
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // Auto remove after 4 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 4000);
        }
        
        // Compliance Dashboard Functions
        function scheduleNewAudit() {
            // Create audit scheduling modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">🛡️ Schedule New Compliance Audit</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Compliance Regulation</label>
                            <select id="auditType" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option value="FERPA">FERPA - Family Educational Rights and Privacy Act</option>
                                <option value="HIPAA">HIPAA - Health Insurance Portability and Accountability Act</option>
                                <option value="GDPR">GDPR - General Data Protection Regulation</option>
                                <option value="SOX">SOX - Sarbanes-Oxley Act</option>
                                <option value="COPPA">COPPA - Children's Online Privacy Protection Act</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Audit Type</label>
                            <select id="auditCategory" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option value="quarterly">Quarterly Review</option>
                                <option value="annual">Annual Assessment</option>
                                <option value="security">Security Audit</option>
                                <option value="data-processing">Data Processing Audit</option>
                                <option value="emergency">Emergency Compliance Check</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Scheduled Date</label>
                            <input type="date" id="auditDate" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;" min="${new Date().toISOString().split('T')[0]}">
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Auditor Assignment</label>
                            <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option>Internal Compliance Team</option>
                                <option>External Auditor - CompliancePro LLC</option>
                                <option>Legal Department Review</option>
                                <option>Third-Party Security Firm</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Priority Level</label>
                            <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option>Standard</option>
                                <option>High Priority</option>
                                <option>Critical</option>
                                <option>Routine</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Additional Notes</label>
                            <textarea style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 80px; resize: vertical;" placeholder="Enter any specific requirements or focus areas for this audit..."></textarea>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-success" onclick="confirmAuditScheduling(); this.closest('.modal').remove();">Schedule Audit</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function confirmAuditScheduling() {
            const auditType = document.getElementById('auditType')?.value || 'FERPA';
            const auditDate = document.getElementById('auditDate')?.value || 'TBD';
            
            showNotification(`${auditType} audit scheduled for ${auditDate}`, 'success');
            
            // Simulate adding new audit to the table (in real app, this would update the database)
            setTimeout(() => {
                showNotification('Audit confirmation email sent to compliance team', 'info');
            }, 2000);
        }
        
        function downloadComplianceReport(regulation, date) {
            // Simulate PDF download process
            showNotification(`Preparing ${regulation} compliance report...`, 'info');
            
            // Create a simulated download process
            setTimeout(() => {
                // Create a blob with sample PDF content (in real app, this would be actual PDF data)
                const pdfContent = `FamEduConnect ${regulation} Compliance Report - ${date}
                
COMPLIANCE AUDIT REPORT
======================

Regulation: ${regulation}
Audit Date: ${date}
Status: PASSED
Compliance Score: 100%

EXECUTIVE SUMMARY
================
This audit confirms that FamEduConnect platform maintains full compliance with ${regulation} regulations.

KEY FINDINGS
============
✓ All data protection measures are in place
✓ User privacy controls are functioning correctly
✓ Security protocols meet or exceed requirements
✓ Documentation is complete and up-to-date

RECOMMENDATIONS
===============
• Continue current compliance practices
• Schedule next review in 90 days
• Monitor regulatory updates

CERTIFICATION
=============
This report certifies that FamEduConnect is fully compliant with ${regulation} as of ${date}.

Auditor: Internal Compliance Team
Report Generated: ${new Date().toISOString().split('T')[0]}
`;
                
                // Create and trigger download
                const blob = new Blob([pdfContent], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `FamEduConnect_${regulation}_Compliance_Report_${date}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showNotification(`${regulation} compliance report downloaded successfully`, 'success');
            }, 1500);
        }
        
        // Video Call Monitoring Functions
        function emergencyOverride() {
            // Create emergency override modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header" style="background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);">
                        <h2 class="modal-title">🚨 Emergency Override</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div style="text-align: center; margin-bottom: 2rem;">
                            <div style="font-size: 4rem; color: #e53e3e; margin-bottom: 1rem;">⚠️</div>
                            <h3 style="color: #e53e3e; margin-bottom: 0.5rem;">Emergency Override Activated</h3>
                            <p style="color: #718096;">This will immediately terminate all active video calls and lock the system</p>
                        </div>
                        
                        <div class="detail-section" style="margin-bottom: 1.5rem; border-left-color: #e53e3e;">
                            <div class="detail-label">Override Actions</div>
                            <div style="background: white; padding: 1rem; border-radius: 4px; color: #2d3748;">
                                <p><strong>✓ End all active calls immediately</strong></p>
                                <p><strong>✓ Lock video call system</strong></p>
                                <p><strong>✓ Send emergency notifications</strong></p>
                                <p><strong>✓ Generate incident report</strong></p>
                                <p><strong>✓ Alert compliance team</strong></p>
                            </div>
                        </div>
                        
                        <div style="background: #fed7d7; padding: 1rem; border-radius: 4px; margin-bottom: 1.5rem;">
                            <p style="color: #742a2a; font-weight: bold;">⚠️ WARNING: This action cannot be undone and will affect all users currently in video calls.</p>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-danger" onclick="executeEmergencyOverride(); this.closest('.modal').remove();">Execute Emergency Override</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function executeEmergencyOverride() {
            showNotification('Emergency override initiated - Terminating all calls...', 'error');
            
            setTimeout(() => {
                showNotification('All video calls have been terminated', 'warning');
            }, 2000);
            
            setTimeout(() => {
                showNotification('Emergency incident report generated', 'info');
            }, 4000);
        }
        
        function monitorCall(participants, duration) {
            // Create call monitoring modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">📹 Live Call Monitoring</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="detail-grid">
                            <div class="detail-section">
                                <div class="detail-label">Participants</div>
                                <div class="detail-value" style="font-family: inherit;">${participants}</div>
                            </div>
                            <div class="detail-section">
                                <div class="detail-label">Call Duration</div>
                                <div class="detail-value" style="font-family: inherit;">${duration}</div>
                            </div>
                        </div>
                        
                        <div style="background: #000; border-radius: 8px; height: 300px; display: flex; align-items: center; justify-content: center; color: white; margin: 1.5rem 0;">
                            <div style="text-align: center;">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">📹</div>
                                <p>Live Video Feed</p>
                                <p style="font-size: 0.9rem; opacity: 0.7;">Monitoring active call in real-time</p>
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <div class="detail-label">Call Quality Metrics</div>
                            <div style="background: white; padding: 1rem; border-radius: 4px; color: #2d3748;">
                                <p><strong>Video Quality:</strong> HD (1080p)</p>
                                <p><strong>Audio Quality:</strong> Clear (48kHz)</p>
                                <p><strong>Connection:</strong> Stable (98% uptime)</p>
                                <p><strong>Latency:</strong> 45ms (Excellent)</p>
                                <p><strong>Translation:</strong> Active (EN → ES)</p>
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-warning" onclick="joinCall('${participants}'); this.closest('.modal').remove();">Join Call</button>
                            <button class="btn btn-danger" onclick="endCall('${participants}'); this.closest('.modal').remove();" style="margin-left: 1rem;">End Call</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Close Monitor</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function joinCall(participants) {
            showNotification(`Joining call with ${participants}...`, 'info');
            
            setTimeout(() => {
                showNotification('Successfully joined the video call as administrator', 'success');
            }, 2000);
        }
        
        function endCall(participants) {
            showNotification(`Ending call with ${participants}...`, 'warning');
            
            setTimeout(() => {
                showNotification('Video call has been terminated by administrator', 'success');
            }, 1500);
        }
        
        function viewCallRecording(participants, duration) {
            // Create call recording viewer modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">📹 Call Recording Viewer</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="detail-grid">
                            <div class="detail-section">
                                <div class="detail-label">Participants</div>
                                <div class="detail-value" style="font-family: inherit;">${participants}</div>
                            </div>
                            <div class="detail-section">
                                <div class="detail-label">Recording Duration</div>
                                <div class="detail-value" style="font-family: inherit;">${duration}</div>
                            </div>
                        </div>
                        
                        <div style="background: #000; border-radius: 8px; height: 300px; display: flex; align-items: center; justify-content: center; color: white; margin: 1.5rem 0; position: relative;">
                            <div style="text-align: center;">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">▶️</div>
                                <p>Recorded Video Playback</p>
                                <p style="font-size: 0.9rem; opacity: 0.7;">Click to play recording</p>
                            </div>
                            <div style="position: absolute; bottom: 10px; left: 10px; right: 10px; background: rgba(0,0,0,0.7); padding: 0.5rem; border-radius: 4px;">
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <button style="background: none; border: none; color: white; font-size: 1.2rem;">⏮️</button>
                                    <button style="background: none; border: none; color: white; font-size: 1.5rem;">▶️</button>
                                    <button style="background: none; border: none; color: white; font-size: 1.2rem;">⏭️</button>
                                    <div style="flex: 1; height: 4px; background: #333; border-radius: 2px; margin: 0 1rem;">
                                        <div style="width: 35%; height: 100%; background: #667eea; border-radius: 2px;"></div>
                                    </div>
                                    <span style="font-size: 0.8rem;">08:32 / ${duration}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <div class="detail-label">Recording Information</div>
                            <div style="background: white; padding: 1rem; border-radius: 4px; color: #2d3748;">
                                <p><strong>Recording Quality:</strong> HD (1080p)</p>
                                <p><strong>File Size:</strong> 245 MB</p>
                                <p><strong>Format:</strong> MP4 (H.264)</p>
                                <p><strong>Transcript:</strong> Available</p>
                                <p><strong>Compliance:</strong> ✓ FERPA Compliant</p>
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-success" onclick="downloadCallRecording('${participants}', '${new Date().toISOString().split('T')[0]}'); this.closest('.modal').remove();">Download Recording</button>
                            <button class="btn btn-warning" onclick="showNotification('Transcript downloaded successfully', 'success');" style="margin-left: 1rem;">Download Transcript</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Close</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function downloadCallRecording(participants, date) {
            showNotification(`Preparing video recording for ${participants}...`, 'info');
            
            setTimeout(() => {
                // Create a simulated video file download
                const videoContent = `FamEduConnect Video Call Recording
                
CALL RECORDING DETAILS
=====================

Participants: ${participants}
Date: ${date}
Duration: 23:45
Quality: HD (1080p)
File Format: MP4

RECORDING METADATA
==================
Start Time: ${date} 15:30:00
End Time: ${date} 15:53:45
Recording Size: 245 MB
Audio Channels: Stereo
Video Codec: H.264
Audio Codec: AAC

COMPLIANCE INFORMATION
=====================
✓ FERPA Compliant - Educational content protected
✓ Recording consent obtained from all participants
✓ Secure storage with encryption
✓ Access logged for audit purposes

TRANSCRIPT SUMMARY
==================
[This would contain the full conversation transcript]
- Discussion about student progress
- Parent concerns addressed
- Action items identified
- Follow-up scheduled

Generated by FamEduConnect Admin System
Report Date: ${new Date().toISOString().split('T')[0]}
`;
                
                const blob = new Blob([videoContent], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `FamEduConnect_Call_Recording_${participants.replace(/[^a-zA-Z0-9]/g, '_')}_${date}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showNotification('Video call recording downloaded successfully', 'success');
            }, 2000);
        }
        
        // Usage Reports Functions
        function exportUsageReport() {
            // Create export options modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">📊 Export Usage Report</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Report Type</label>
                            <select id="reportType" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option value="comprehensive">Comprehensive Usage Report</option>
                                <option value="school-performance">School Performance Report</option>
                                <option value="feature-analytics">Feature Usage Analytics</option>
                                <option value="user-engagement">User Engagement Report</option>
                                <option value="language-usage">Language Usage Statistics</option>
                                <option value="compliance-summary">Compliance Summary Report</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Date Range</label>
                            <select id="dateRange" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option value="last-7-days">Last 7 Days</option>
                                <option value="last-30-days" selected>Last 30 Days</option>
                                <option value="last-90-days">Last 90 Days</option>
                                <option value="last-year">Last Year</option>
                                <option value="custom">Custom Date Range</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Export Format</label>
                            <select id="exportFormat" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option value="pdf">PDF Report</option>
                                <option value="excel">Excel Spreadsheet (.xlsx)</option>
                                <option value="csv">CSV Data File</option>
                                <option value="json">JSON Data Export</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Include Sections</label>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Executive Summary
                                </label>
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Usage Statistics
                                </label>
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> School Performance Data
                                </label>
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Feature Analytics
                                </label>
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" style="margin-right: 0.5rem;"> Raw Data Tables
                                </label>
                                <label style="display: flex; align-items: center;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Charts and Visualizations
                                </label>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Report Recipients</label>
                            <textarea style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 60px; resize: vertical;" placeholder="Enter email addresses separated by commas (optional)"></textarea>
                        </div>
                        
                        <div style="background: #e6fffa; padding: 1rem; border-radius: 4px; margin-bottom: 1.5rem;">
                            <p style="color: #234e52; font-size: 0.9rem;"><strong>📊 Report Preview:</strong> This report will include data from 156 schools, 2,847 users, and 45,678 messages over the selected time period.</p>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-success" onclick="generateUsageReport(); this.closest('.modal').remove();">Generate & Export Report</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function generateUsageReport() {
            const reportType = document.getElementById('reportType')?.value || 'comprehensive';
            const dateRange = document.getElementById('dateRange')?.value || 'last-30-days';
            const exportFormat = document.getElementById('exportFormat')?.value || 'pdf';
            
            showNotification(`Generating ${reportType} report for ${dateRange}...`, 'info');
            
            // Simulate report generation process
            setTimeout(() => {
                showNotification('Compiling usage statistics...', 'info');
            }, 1000);
            
            setTimeout(() => {
                showNotification('Processing school performance data...', 'info');
            }, 2000);
            
            setTimeout(() => {
                showNotification('Generating charts and visualizations...', 'info');
            }, 3000);
            
            setTimeout(() => {
                // Create comprehensive usage report content
                const reportContent = `FAMEDUCONNECT USAGE REPORT
==========================

Report Type: ${reportType.replace('-', ' ').toUpperCase()}
Date Range: ${dateRange.replace('-', ' ').toUpperCase()}
Generated: ${new Date().toISOString().split('T')[0]}
Export Format: ${exportFormat.toUpperCase()}

EXECUTIVE SUMMARY
================
This comprehensive usage report provides detailed insights into the FamEduConnect platform performance and user engagement metrics.

KEY METRICS
===========
• Total Active Users: 2,847 (+12% growth)
• Active Schools: 156 (+3 new schools)
• Messages Sent: 45,678 (+15% this week)
• Video Calls: 1,234 (+22% this week)
• User Satisfaction: 89.5% (+2.3% improvement)
• Languages Used: 67 (global reach)

SCHOOL PERFORMANCE RANKINGS
===========================
1. Lincoln Elementary - 97% engagement (485/500 users)
2. Washington Middle - 94% engagement (672/710 users)
3. Roosevelt High - 89% engagement (1,234/1,300 users)

FEATURE USAGE ANALYTICS
======================
• Real-time Messaging: 2,456 daily users (8.3 times/day) - 4.9/5 satisfaction
• Video Conferencing: 1,234 daily users (2.1 times/week) - 4.7/5 satisfaction
• Grade Tracking: 1,890 daily users (5.6 times/week) - 4.8/5 satisfaction
• Language Translation: 567 daily users (3.2 times/day) - 4.6/5 satisfaction

LANGUAGE USAGE STATISTICS
=========================
• English: 1,456 users (2.1M messages, 567 video calls) +8% growth
• Spanish: 678 users (890K messages, 234 video calls) +23% growth
• Arabic: 345 users (456K messages, 123 video calls) +45% growth
• French: 234 users (312K messages, 89 video calls) +12% growth
• Chinese: 134 users (178K messages, 45 video calls) +67% growth

PERFORMANCE METRICS
==================
• Average Response Time: 245ms (Target: <500ms) ✓ Excellent
• System Uptime: 99.8% (Target: >99.5%) ✓ Target Met
• Error Rate: 0.02% (Target: <0.1%) ✓ Excellent
• Database Query Time: 12ms (Target: <50ms) ✓ Optimal

COMPLIANCE STATUS
================
✓ FERPA Compliant - 100% educational privacy protection
✓ HIPAA Ready - 100% health information security
✓ GDPR Compliant - 100% data protection compliance
✓ Zero compliance violations detected

RECOMMENDATIONS
===============
• Continue current growth trajectory
• Focus on expanding language translation usage
• Maintain excellent performance metrics
• Schedule quarterly compliance reviews
• Consider expanding to additional school districts

TECHNICAL DETAILS
=================
• Report Generated By: FamEduConnect Admin System
• Data Sources: Production database, analytics engine, compliance monitor
• Verification: All metrics verified against blockchain audit trail
• Next Report: Scheduled for ${new Date(Date.now() + 30*24*60*60*1000).toISOString().split('T')[0]}

© 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc. – NPO: 2023-001341848
Creative Director: Na'imah Barnes
`;
                
                // Create and trigger download
                const blob = new Blob([reportContent], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `FamEduConnect_Usage_Report_${reportType}_${dateRange}_${new Date().toISOString().split('T')[0]}.${exportFormat === 'pdf' ? 'txt' : exportFormat}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                showNotification(`${reportType.replace('-', ' ')} report exported successfully as ${exportFormat.toUpperCase()}`, 'success');
            }, 4000);
        }
        
        // Content Moderation Functions
        function showBulkActions() {
            // Create bulk actions modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">🛡️ Bulk Content Moderation Actions</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Select Action</label>
                            <select id="bulkAction" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option value="approve-all">Approve All Flagged Content</option>
                                <option value="block-all">Block All Flagged Content</option>
                                <option value="approve-low">Approve All Low Severity Items</option>
                                <option value="block-high">Block All High Severity Items</option>
                                <option value="warn-users">Send Warning to All Users</option>
                                <option value="escalate">Escalate to Human Review</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Filter by Severity</label>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> High Severity (1 item)
                                </label>
                                <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Medium Severity (1 item)
                                </label>
                                <label style="display: flex; align-items: center;">
                                    <input type="checkbox" checked style="margin-right: 0.5rem;"> Low Severity (1 item)
                                </label>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Reason for Bulk Action</label>
                            <textarea style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 80px; resize: vertical;" placeholder="Enter reason for this bulk moderation action..."></textarea>
                        </div>
                        
                        <div style="background: #fef2f2; padding: 1rem; border-radius: 4px; margin-bottom: 1.5rem;">
                            <p style="color: #742a2a; font-weight: bold;">⚠️ WARNING: Bulk actions will affect 3 flagged messages and cannot be undone.</p>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-warning" onclick="executeBulkAction(); this.closest('.modal').remove();">Execute Bulk Action</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function executeBulkAction() {
            const action = document.getElementById('bulkAction')?.value || 'approve-all';
            showNotification(`Executing bulk action: ${action.replace('-', ' ')}...`, 'info');
            
            setTimeout(() => {
                showNotification('Bulk moderation action completed successfully', 'success');
            }, 2000);
        }
        
        function approveContent(from, to, reason) {
            showNotification(`Approving message from ${from} to ${to}...`, 'info');
            
            setTimeout(() => {
                showNotification(`Message approved and delivered to ${to}`, 'success');
            }, 1000);
        }
        
        function blockContent(from, to, reason) {
            showNotification(`Blocking message from ${from} to ${to}...`, 'warning');
            
            setTimeout(() => {
                showNotification(`Message blocked and ${from} has been notified`, 'success');
            }, 1000);
        }
        
        function editAndSend(from, to, content) {
            // Create edit message modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">✏️ Edit Message Content</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="detail-grid">
                            <div class="detail-section">
                                <div class="detail-label">From</div>
                                <div class="detail-value" style="font-family: inherit;">${from}</div>
                            </div>
                            <div class="detail-section">
                                <div class="detail-label">To</div>
                                <div class="detail-value" style="font-family: inherit;">${to}</div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Original Message</label>
                            <div style="background: #fed7d7; padding: 1rem; border-radius: 4px; color: #742a2a;">
                                ${content}
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Edited Message</label>
                            <textarea id="editedMessage" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 100px; resize: vertical;" placeholder="Enter the edited version of this message...">${content.replace('completely unacceptable', 'concerning')}</textarea>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Moderation Note</label>
                            <textarea style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 60px; resize: vertical;" placeholder="Note to sender about the edit...">Your message has been edited to maintain a respectful tone in educational communications.</textarea>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-success" onclick="sendEditedMessage('${from}', '${to}'); this.closest('.modal').remove();">Send Edited Message</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function sendEditedMessage(from, to) {
            showNotification(`Sending edited message from ${from} to ${to}...`, 'info');
            
            setTimeout(() => {
                showNotification('Edited message sent successfully with moderation note', 'success');
            }, 1500);
        }
        
        function suggestEdit(from, to, content) {
            // Create suggest edit modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">💡 Suggest Message Edit</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="detail-grid">
                            <div class="detail-section">
                                <div class="detail-label">From</div>
                                <div class="detail-value" style="font-family: inherit;">${from}</div>
                            </div>
                            <div class="detail-section">
                                <div class="detail-label">To</div>
                                <div class="detail-value" style="font-family: inherit;">${to}</div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Original Message</label>
                            <div style="background: #feebc8; padding: 1rem; border-radius: 4px; color: #744210;">
                                ${content}
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Suggested Alternative</label>
                            <textarea style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 80px; resize: vertical;">Let's schedule a meeting at the school to discuss this matter further.</textarea>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Suggestion Reason</label>
                            <textarea style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 60px; resize: vertical;">For safety and professional standards, we recommend meeting at the school rather than private residences.</textarea>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-warning" onclick="sendSuggestion('${from}', '${to}'); this.closest('.modal').remove();">Send Suggestion</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function sendSuggestion(from, to) {
            showNotification(`Sending edit suggestion to ${from}...`, 'info');
            
            setTimeout(() => {
                showNotification('Edit suggestion sent successfully', 'success');
            }, 1500);
        }
        
        function redactInfo(from, to, content) {
            // Create redact information modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">🔒 Redact Personal Information</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="detail-grid">
                            <div class="detail-section">
                                <div class="detail-label">From</div>
                                <div class="detail-value" style="font-family: inherit;">${from}</div>
                            </div>
                            <div class="detail-section">
                                <div class="detail-label">To</div>
                                <div class="detail-value" style="font-family: inherit;">${to}</div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Original Message</label>
                            <div style="background: #e6fffa; padding: 1rem; border-radius: 4px; color: #234e52;">
                                ${content}
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Detected Personal Information</label>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
                                <p><strong>Phone Number:</strong> 555-1234</p>
                                <p><strong>Email Address:</strong> <EMAIL></p>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Redacted Message</label>
                            <textarea style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 80px; resize: vertical;">You can reach me at [PHONE REDACTED] or [EMAIL REDACTED]</textarea>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Redaction Reason</label>
                            <textarea style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 60px; resize: vertical;">Personal contact information redacted for privacy protection in accordance with platform policies.</textarea>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-warning" onclick="executeRedaction('${from}', '${to}'); this.closest('.modal').remove();">Execute Redaction</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function executeRedaction(from, to) {
            showNotification(`Redacting personal information from ${from}'s message...`, 'info');
            
            setTimeout(() => {
                showNotification('Personal information redacted and message delivered safely', 'success');
            }, 1500);
        }
        
        // User Management Functions
        function addNewUser() {
            // Create add user modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">👤 Add New User</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">User Type</label>
                            <select id="userType" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option value="parent">Parent</option>
                                <option value="teacher">Teacher</option>
                                <option value="admin">Administrator</option>
                                <option value="student">Student</option>
                            </select>
                        </div>
                        
                        <div class="detail-grid">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">First Name</label>
                                <input type="text" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;" placeholder="Enter first name">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Last Name</label>
                                <input type="text" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;" placeholder="Enter last name">
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Email Address</label>
                            <input type="email" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;" placeholder="<EMAIL>">
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">School Assignment</label>
                            <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option>Lincoln Elementary School</option>
                                <option>Washington Middle School</option>
                                <option>Roosevelt High School</option>
                                <option>Jefferson Elementary</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Initial Status</label>
                            <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option>Active</option>
                                <option>Pending Approval</option>
                                <option>Inactive</option>
                            </select>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-success" onclick="createNewUser(); this.closest('.modal').remove();">Create User</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function createNewUser() {
            const userType = document.getElementById('userType')?.value || 'parent';
            showNotification(`Creating new ${userType} account...`, 'info');
            
            setTimeout(() => {
                showNotification(`New ${userType} account created successfully`, 'success');
            }, 2000);
        }
        
        function editUser(name, role) {
            showNotification(`Opening ${name}'s profile for editing...`, 'info');
            
            setTimeout(() => {
                showNotification(`${name}'s profile updated successfully`, 'success');
            }, 1500);
        }
        
        function suspendUser(name, role) {
            showNotification(`Suspending ${name} (${role})...`, 'warning');
            
            setTimeout(() => {
                showNotification(`${name} has been suspended and notified`, 'success');
            }, 1500);
        }
        
        function blockUser(name, role) {
            showNotification(`Blocking ${name} (${role})...`, 'error');
            
            setTimeout(() => {
                showNotification(`${name} has been blocked from the platform`, 'success');
            }, 1500);
        }
        
        function approveUser(name, role) {
            showNotification(`Approving ${name} (${role})...`, 'info');
            
            setTimeout(() => {
                showNotification(`${name} has been approved and can now access the platform`, 'success');
            }, 1500);
        }
        
        function rejectUser(name, role) {
            showNotification(`Rejecting ${name} (${role})...`, 'warning');
            
            setTimeout(() => {
                showNotification(`${name}'s registration has been rejected`, 'success');
            }, 1500);
        }
        
        // School Administration Functions
        function addNewSchool() {
            // Create add school modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">🏫 Add New School</h2>
                        <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">School Name</label>
                            <input type="text" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;" placeholder="Enter school name">
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">School District</label>
                            <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                <option>Metro District</option>
                                <option>East District</option>
                                <option>West District</option>
                                <option>North District</option>
                                <option>Create New District</option>
                            </select>
                        </div>
                        
                        <div class="detail-grid">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">School Type</label>
                                <select style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;">
                                    <option>Elementary School</option>
                                    <option>Middle School</option>
                                    <option>High School</option>
                                    <option>K-12 School</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Grade Levels</label>
                                <input type="text" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;" placeholder="e.g., K-5, 6-8, 9-12">
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">School Address</label>
                            <textarea style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%; height: 80px; resize: vertical;" placeholder="Enter complete school address"></textarea>
                        </div>
                        
                        <div class="detail-grid">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Principal Name</label>
                                <input type="text" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;" placeholder="Principal's full name">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Contact Phone</label>
                                <input type="tel" style="padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 4px; width: 100%;" placeholder="(*************">
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button class="btn btn-success" onclick="createNewSchool(); this.closest('.modal').remove();">Create School</button>
                            <button class="btn" onclick="this.closest('.modal').remove()" style="margin-left: 1rem;">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function createNewSchool() {
            showNotification('Creating new school profile...', 'info');
            
            setTimeout(() => {
                showNotification('New school added successfully to the platform', 'success');
            }, 2000);
        }
        
        function editSchool(schoolName) {
            showNotification(`Opening ${schoolName} for editing...`, 'info');
            
            setTimeout(() => {
                showNotification(`${schoolName} profile updated successfully`, 'success');
            }, 1500);
        }
        
        function schoolSettings(schoolName) {
            showNotification(`Opening settings for ${schoolName}...`, 'info');
            
            setTimeout(() => {
                showNotification(`${schoolName} settings configured successfully`, 'success');
            }, 1500);
        }
        
        function schoolReports(schoolName) {
            showNotification(`Generating reports for ${schoolName}...`, 'info');
            
            setTimeout(() => {
                showNotification(`${schoolName} performance report downloaded`, 'success');
            }, 2000);
        }
        
        function completeSchoolSetup(schoolName) {
            showNotification(`Completing setup for ${schoolName}...`, 'info');
            
            setTimeout(() => {
                showNotification(`${schoolName} setup completed and activated`, 'success');
            }, 2500);
        }
        
        function removeSchool(schoolName) {
            showNotification(`Removing ${schoolName} from platform...`, 'warning');
            
            setTimeout(() => {
                showNotification(`${schoolName} has been removed from the platform`, 'success');
            }, 2000);
        }
        
        // Dashboard Functions
        function monitorActivity(user, action) {
            showNotification(`Monitoring ${user}'s ${action}...`, 'info');
            
            setTimeout(() => {
                showNotification('Activity monitoring initiated successfully', 'success');
            }, 1000);
        }
        
        function approveRegistration(school) {
            showNotification(`Approving registration for ${school}...`, 'info');
            
            setTimeout(() => {
                showNotification(`${school} registration approved successfully`, 'success');
            }, 1500);
        }
        
        function viewSystemReport() {
            showNotification('Generating system security report...', 'info');
            
            setTimeout(() => {
                showNotification('System security report downloaded successfully', 'success');
            }, 2000);
        }
        
        // Advanced Analytics Functions
        function generateAnalyticsReport() {
            showNotification('Generating advanced analytics report...', 'info');
            
            setTimeout(() => {
                showNotification('Advanced analytics report exported successfully', 'success');
            }, 3000);
        }
        
        function closeModal() {
            document.getElementById('blockDetailsModal').style.display = 'none';
        }
        
        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('blockDetailsModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>