apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: fameduconnect
data:
  POSTGRES_DB: fameduconnect_prod
  POSTGRES_USER: fameduconnect
  POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
  POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
  POSTGRES_SHARED_PRELOAD_LIBRARIES: "pg_stat_statements,auto_explain"
  POSTGRES_MAX_CONNECTIONS: "200"
  POSTGRES_SHARED_BUFFERS: "256MB"
  POSTGRES_EFFECTIVE_CACHE_SIZE: "1GB"
  POSTGRES_WORK_MEM: "4MB"
  POSTGRES_MAINTENANCE_WORK_MEM: "64MB"
  POSTGRES_CHECKPOINT_SEGMENTS: "32"
  POSTGRES_CHECKPOINT_COMPLETION_TARGET: "0.9"
  POSTGRES_WAL_BUFFERS: "16MB"
  POSTGRES_DEFAULT_STATISTICS_TARGET: "100"
  POSTGRES_RANDOM_PAGE_COST: "1.1"
  POSTGRES_EFFECTIVE_IO_CONCURRENCY: "200"
  POSTGRES_WORK_MEM: "4MB"
  POSTGRES_MAINTENANCE_WORK_MEM: "64MB"
  POSTGRES_AUTOVACUUM_MAX_WORKERS: "3"
  POSTGRES_AUTOVACUUM_NAPTIME: "1min"
  POSTGRES_LOG_STATEMENT: "all"
  POSTGRES_LOG_MIN_DURATION_STATEMENT: "1000"
  POSTGRES_LOG_CHECKPOINTS: "on"
  POSTGRES_LOG_CONNECTIONS: "on"
  POSTGRES_LOG_DISCONNECTIONS: "on"
  POSTGRES_LOG_LOCK_WAITS: "on"
  POSTGRES_LOG_TEMP_FILES: "0"
  POSTGRES_LOG_LINE_PREFIX: "%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h "
  POSTGRES_LOG_TIMEZONE: "UTC"
  POSTGRES_DATE_STYLE: "ISO, MDY"
  POSTGRES_TIMEZONE: "UTC"
  POSTGRES_LC_MESSAGES: "C"
  POSTGRES_LC_MONETARY: "C"
  POSTGRES_LC_NUMERIC: "C"
  POSTGRES_LC_TIME: "C"
  POSTGRES_DEFAULT_TEXT_SEARCH_CONFIG: "pg_catalog.english"
---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: fameduconnect
type: Opaque
data:
  POSTGRES_PASSWORD: ${BASE64_ENCODED_POSTGRES_PASSWORD}
  POSTGRES_REPLICATION_PASSWORD: ${BASE64_ENCODED_REPLICATION_PASSWORD}
  POSTGRES_BACKUP_PASSWORD: ${BASE64_ENCODED_BACKUP_PASSWORD}
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres-primary
  namespace: fameduconnect
  labels:
    app: postgres-primary
    component: database
spec:
  serviceName: postgres-primary
  replicas: 1
  selector:
    matchLabels:
      app: postgres-primary
  template:
    metadata:
      labels:
        app: postgres-primary
        component: database
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: postgres
        image: postgres:15.4
        ports:
        - containerPort: 5432
          name: postgres
        envFrom:
        - configMapRef:
            name: postgres-config
        - secretRef:
            name: postgres-secret
        env:
        - name: POSTGRES_HOST_AUTH_METHOD
          value: "scram-sha-256"
        - name: POSTGRES_INITDB_ARGS
          value: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
        - name: POSTGRES_REPLICATION_MODE
          value: "master"
        - name: POSTGRES_REPLICATION_USER
          value: "repl_user"
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_REPLICATION_PASSWORD
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: postgres-config-volume
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        - name: postgres-init
          mountPath: /docker-entrypoint-initdb.d
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - fameduconnect
            - -d
            - fameduconnect_prod
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - fameduconnect
            - -d
            - fameduconnect_prod
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: postgres-config-volume
        configMap:
          name: postgres-config
      - name: postgres-init
        configMap:
          name: postgres-init-scripts
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "gp3-encrypted"
      resources:
        requests:
          storage: 100Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres-replica
  namespace: fameduconnect
  labels:
    app: postgres-replica
    component: database
spec:
  serviceName: postgres-replica
  replicas: 3
  selector:
    matchLabels:
      app: postgres-replica
  template:
    metadata:
      labels:
        app: postgres-replica
        component: database
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: postgres
        image: postgres:15.4
        ports:
        - containerPort: 5432
          name: postgres
        envFrom:
        - configMapRef:
            name: postgres-config
        - secretRef:
            name: postgres-secret
        env:
        - name: POSTGRES_HOST_AUTH_METHOD
          value: "scram-sha-256"
        - name: POSTGRES_REPLICATION_MODE
          value: "slave"
        - name: POSTGRES_REPLICATION_USER
          value: "repl_user"
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_REPLICATION_PASSWORD
        - name: POSTGRES_MASTER_HOST
          value: "postgres-primary"
        - name: POSTGRES_MASTER_PORT_NUMBER
          value: "5432"
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: postgres-config-volume
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - fameduconnect
            - -d
            - fameduconnect_prod
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - fameduconnect
            - -d
            - fameduconnect_prod
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: postgres-config-volume
        configMap:
          name: postgres-config
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "gp3-encrypted"
      resources:
        requests:
          storage: 50Gi
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-primary
  namespace: fameduconnect
  labels:
    app: postgres-primary
    component: database
spec:
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: postgres-primary
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-replica
  namespace: fameduconnect
  labels:
    app: postgres-replica
    component: database
spec:
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: postgres-replica
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-read
  namespace: fameduconnect
  labels:
    app: postgres-read
    component: database
spec:
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: postgres-replica
  type: ClusterIP 