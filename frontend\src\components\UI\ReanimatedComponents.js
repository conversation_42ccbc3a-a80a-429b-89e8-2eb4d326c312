import React from 'react';
import { motion, useMotionValue, useTransform, useSpring, useAnimation, AnimatePresence } from 'framer-motion';

// React Native Reanimated-style components using Framer Motion

export const ReanimatedView = ({ children, style, ...props }) => {
  return (
    <motion.div
      style={style}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ 
        type: "spring", 
        stiffness: 300, 
        damping: 30,
        mass: 0.8
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export const ReanimatedText = ({ children, style, ...props }) => {
  const opacity = useMotionValue(0);
  const y = useMotionValue(20);
  
  React.useEffect(() => {
    opacity.set(1);
    y.set(0);
  }, [opacity, y]);

  return (
    <motion.div
      style={{ 
        ...style, 
        opacity, 
        y,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export const ReanimatedButton = ({ children, onPress, style, ...props }) => {
  const scale = useMotionValue(1);
  const rotate = useMotionValue(0);
  
  const handlePress = () => {
    scale.set(0.95);
    rotate.set(5);
    setTimeout(() => {
      scale.set(1);
      rotate.set(0);
    }, 150);
    onPress?.();
  };

  return (
    <motion.button
      style={{ 
        ...style, 
        scale, 
        rotate,
        cursor: 'pointer'
      }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={handlePress}
      {...props}
    >
      {children}
    </motion.button>
  );
};

export const ReanimatedCard = ({ children, style, ...props }) => {
  const y = useMotionValue(0);
  const rotateX = useMotionValue(0);
  const rotateY = useMotionValue(0);
  
  const handleMouseMove = (event) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const mouseX = event.clientX - centerX;
    const mouseY = event.clientY - centerY;
    
    rotateX.set(-mouseY / 10);
    rotateY.set(mouseX / 10);
  };
  
  const handleMouseLeave = () => {
    rotateX.set(0);
    rotateY.set(0);
  };

  return (
    <motion.div
      style={{ 
        ...style, 
        y, 
        rotateX, 
        rotateY,
        transformStyle: 'preserve-3d',
        perspective: 1000
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export const ReanimatedScrollView = ({ children, style, ...props }) => {
  const scrollY = useMotionValue(0);
  const opacity = useTransform(scrollY, [0, 100], [1, 0.8]);
  const scale = useTransform(scrollY, [0, 100], [1, 0.95]);

  return (
    <motion.div
      style={{ 
        ...style, 
        opacity, 
        scale,
        overflow: 'auto'
      }}
      onScroll={(e) => scrollY.set(e.target.scrollTop)}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export const ReanimatedModal = ({ isVisible, children, onClose, style, ...props }) => {
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };
  
  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8, 
      y: 50,
      rotateX: -15
    },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      rotateX: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30,
        mass: 0.8
      }
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}
          variants={backdropVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          <motion.div
            style={style}
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            onClick={(e) => e.stopPropagation()}
            {...props}
          >
            {children}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export const ReanimatedList = ({ items, renderItem, style, ...props }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <motion.div
      style={style}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      {...props}
    >
      {items.map((item, index) => (
        <motion.div
          key={index}
          variants={itemVariants}
        >
          {renderItem(item, index)}
        </motion.div>
      ))}
    </motion.div>
  );
};

export const ReanimatedProgress = ({ progress, style, ...props }) => {
  const progressSpring = useSpring(progress, {
    stiffness: 100,
    damping: 30
  });

  return (
    <motion.div
      style={{
        ...style,
        width: '100%',
        height: '8px',
        backgroundColor: '#e5e7eb',
        borderRadius: '4px',
        overflow: 'hidden'
      }}
      {...props}
    >
      <motion.div
        style={{
          width: progressSpring,
          height: '100%',
          backgroundColor: '#3b82f6',
          borderRadius: '4px'
        }}
      />
    </motion.div>
  );
};

export const ReanimatedSwitch = ({ isOn, onToggle, style, ...props }) => {
  const x = useTransform(isOn ? 1 : 0, [0, 1], [2, 22]);
  const backgroundColor = useTransform(isOn ? 1 : 0, [0, 1], ['#e5e7eb', '#3b82f6']);

  return (
    <motion.div
      style={{
        ...style,
        width: '44px',
        height: '24px',
        backgroundColor,
        borderRadius: '12px',
        padding: '2px',
        cursor: 'pointer'
      }}
      onClick={onToggle}
      whileTap={{ scale: 0.95 }}
      {...props}
    >
      <motion.div
        style={{
          width: '20px',
          height: '20px',
          backgroundColor: 'white',
          borderRadius: '10px',
          x
        }}
      />
    </motion.div>
  );
};

export const ReanimatedSlider = ({ value, onValueChange, min = 0, max = 100, style, ...props }) => {
  const percentage = ((value - min) / (max - min)) * 100;
  const x = useTransform(percentage, [0, 100], [0, 100]);

  return (
    <motion.div
      style={{
        ...style,
        width: '100%',
        height: '40px',
        display: 'flex',
        alignItems: 'center',
        position: 'relative'
      }}
      {...props}
    >
      <motion.div
        style={{
          width: '100%',
          height: '4px',
          backgroundColor: '#e5e7eb',
          borderRadius: '2px'
        }}
      >
        <motion.div
          style={{
            width: x,
            height: '100%',
            backgroundColor: '#3b82f6',
            borderRadius: '2px'
          }}
        />
      </motion.div>
      <motion.div
        style={{
          position: 'absolute',
          left: x,
          width: '20px',
          height: '20px',
          backgroundColor: '#3b82f6',
          borderRadius: '10px',
          x: '-10px'
        }}
        drag="x"
        dragConstraints={{ left: 0, right: 100 }}
        dragElastic={0}
        onDragEnd={(event, info) => {
          const newPercentage = Math.max(0, Math.min(100, info.point.x));
          const newValue = min + (newPercentage / 100) * (max - min);
          onValueChange?.(newValue);
        }}
      />
    </motion.div>
  );
};

export default {
  ReanimatedView,
  ReanimatedText,
  ReanimatedButton,
  ReanimatedCard,
  ReanimatedScrollView,
  ReanimatedModal,
  ReanimatedList,
  ReanimatedProgress,
  ReanimatedSwitch,
  ReanimatedSlider
}; 