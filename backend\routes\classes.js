const express = require('express');
const { Class, User, Student, Message, VideoCall } = require('../models');
const { body, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');
const { authorize } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

// Get classes (role-based access)
router.get('/', authMiddleware, async (req, res) => {
  try {
    const { page = 1, limit = 20, grade, schoolYear, search } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = {};
    let includeClause = [
      {
        model: User,
        as: 'teacher',
        attributes: ['id', 'firstName', 'lastName', 'email']
      }
    ];

    // Role-based filtering
    if (req.user.role === 'teacher') {
      whereClause.teacherId = req.user.userId;
    } else if (req.user.role === 'parent') {
      // Get classes where parent's children are enrolled
      const children = await Student.findAll({
        where: { parentId: req.user.userId },
        attributes: ['classId']
      });
      const classIds = children.map(child => child.classId);
      whereClause.id = { [Op.in]: classIds };
    }

    // Additional filters
    if (grade) {
      whereClause.grade = grade;
    }

    if (schoolYear) {
      whereClause.schoolYear = schoolYear;
    }

    if (search) {
      whereClause[Op.or] = [
        { className: { [Op.iLike]: `%${search}%` } },
        { classCode: { [Op.iLike]: `%${search}%` } },
        { subject: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Add student count for all roles
    includeClause.push({
      model: Student,
      as: 'students',
      attributes: ['id'],
      required: false
    });

    const classes = await Class.findAndCountAll({
      where: whereClause,
      include: includeClause,
      order: [['className', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Format response with student counts
    const formattedClasses = classes.rows.map(cls => ({
      ...cls.toJSON(),
      studentCount: cls.students ? cls.students.length : 0,
      students: undefined // Remove students array from response
    }));

    res.json({
      classes: formattedClasses,
      totalCount: classes.count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(classes.count / limit)
    });
  } catch (error) {
    console.error('Get classes error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get specific class
router.get('/:classId', authMiddleware, async (req, res) => {
  try {
    const { classId } = req.params;

    const classRecord = await Class.findByPk(classId, {
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        },
        {
          model: Student,
          as: 'students',
          include: [
            {
              model: User,
              as: 'parent',
              attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
            }
          ]
        }
      ]
    });

    if (!classRecord) {
      return res.status(404).json({ message: 'Class not found' });
    }

    // Check access permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      classRecord.teacherId === req.user.userId ||
      (req.user.role === 'parent' && classRecord.students.some(student => student.parentId === req.user.userId));

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Filter student data based on role
    if (req.user.role === 'parent') {
      classRecord.students = classRecord.students.filter(student => student.parentId === req.user.userId);
    }

    res.json(classRecord);
  } catch (error) {
    console.error('Get class error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create class (admin/teacher only)
router.post('/', authMiddleware, authorize(['admin', 'teacher']), [
  body('className').trim().isLength({ min: 1, max: 100 }),
  body('classCode').trim().isLength({ min: 3, max: 20 }),
  body('grade').isIn(['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']),
  body('schoolYear').matches(/^\d{4}-\d{4}$/),
  body('teacherId').optional().isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      className,
      classCode,
      grade,
      subject,
      schoolYear,
      semester,
      maxStudents,
      schedule,
      classroom,
      description,
      syllabus
    } = req.body;

    let { teacherId } = req.body;

    // If no teacherId provided and user is teacher, assign to current user
    if (!teacherId && req.user.role === 'teacher') {
      teacherId = req.user.userId;
    }

    // Check if class code already exists
    const existingClass = await Class.findOne({ where: { classCode } });
    if (existingClass) {
      return res.status(400).json({ message: 'Class code already exists' });
    }

    // Verify teacher exists
    if (teacherId) {
      const teacher = await User.findByPk(teacherId);
      if (!teacher || teacher.role !== 'teacher') {
        return res.status(400).json({ message: 'Invalid teacher ID' });
      }
    }

    const classRecord = await Class.create({
      className,
      classCode,
      grade,
      subject,
      teacherId,
      schoolYear,
      semester: semester || 'Full Year',
      maxStudents: maxStudents || 30,
      schedule: schedule || {},
      classroom,
      description,
      syllabus
    });

    // Load full class data
    const fullClass = await Class.findByPk(classRecord.id, {
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    res.status(201).json(fullClass);
  } catch (error) {
    console.error('Create class error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update class
router.patch('/:classId', authMiddleware, async (req, res) => {
  try {
    const { classId } = req.params;
    const updates = req.body;

    const classRecord = await Class.findByPk(classId);
    if (!classRecord) {
      return res.status(404).json({ message: 'Class not found' });
    }

    // Check permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      classRecord.teacherId === req.user.userId;

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    await classRecord.update(updates);

    const updatedClass = await Class.findByPk(classId, {
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    res.json(updatedClass);
  } catch (error) {
    console.error('Update class error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get class students
router.get('/:classId/students', authMiddleware, async (req, res) => {
  try {
    const { classId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    const classRecord = await Class.findByPk(classId);
    if (!classRecord) {
      return res.status(404).json({ message: 'Class not found' });
    }

    // Check access permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      classRecord.teacherId === req.user.userId ||
      req.user.role === 'parent'; // Parents can see class roster

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const students = await Student.findAndCountAll({
      where: { classId },
      include: [
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        }
      ],
      order: [['lastName', 'ASC'], ['firstName', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      students: students.rows,
      totalCount: students.count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(students.count / limit)
    });
  } catch (error) {
    console.error('Get class students error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Add student to class (admin/teacher only)
router.post('/:classId/students', authMiddleware, authorize(['admin', 'teacher']), [
  body('studentId').isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { classId } = req.params;
    const { studentId } = req.body;

    const classRecord = await Class.findByPk(classId);
    if (!classRecord) {
      return res.status(404).json({ message: 'Class not found' });
    }

    const student = await Student.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Check if class is full
    if (classRecord.currentEnrollment >= classRecord.maxStudents) {
      return res.status(400).json({ message: 'Class is full' });
    }

    // Update student's class
    await student.update({ classId });

    // Update class enrollment count
    await classRecord.update({
      currentEnrollment: classRecord.currentEnrollment + 1
    });

    res.json({ message: 'Student added to class successfully' });
  } catch (error) {
    console.error('Add student to class error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Remove student from class (admin/teacher only)
router.delete('/:classId/students/:studentId', authMiddleware, authorize(['admin', 'teacher']), async (req, res) => {
  try {
    const { classId, studentId } = req.params;

    const classRecord = await Class.findByPk(classId);
    if (!classRecord) {
      return res.status(404).json({ message: 'Class not found' });
    }

    const student = await Student.findOne({
      where: { id: studentId, classId }
    });

    if (!student) {
      return res.status(404).json({ message: 'Student not found in this class' });
    }

    // Remove student from class (set classId to null or move to another class)
    await student.update({ classId: null });

    // Update class enrollment count
    await classRecord.update({
      currentEnrollment: Math.max(0, classRecord.currentEnrollment - 1)
    });

    res.json({ message: 'Student removed from class successfully' });
  } catch (error) {
    console.error('Remove student from class error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get class messages
router.get('/:classId/messages', authMiddleware, async (req, res) => {
  try {
    const { classId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const classRecord = await Class.findByPk(classId);
    if (!classRecord) {
      return res.status(404).json({ message: 'Class not found' });
    }

    // Check access permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      classRecord.teacherId === req.user.userId ||
      (req.user.role === 'parent' && await Student.findOne({
        where: { classId, parentId: req.user.userId }
      }));

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const messages = await Message.findAndCountAll({
      where: { classId },
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      messages: messages.rows,
      totalCount: messages.count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(messages.count / limit)
    });
  } catch (error) {
    console.error('Get class messages error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get class video calls
router.get('/:classId/video-calls', authMiddleware, async (req, res) => {
  try {
    const { classId } = req.params;
    const { status, upcoming } = req.query;

    const classRecord = await Class.findByPk(classId);
    if (!classRecord) {
      return res.status(404).json({ message: 'Class not found' });
    }

    // Check access permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      classRecord.teacherId === req.user.userId ||
      (req.user.role === 'parent' && await Student.findOne({
        where: { classId, parentId: req.user.userId }
      }));

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    let whereClause = { classId };

    if (status) {
      whereClause.status = status;
    }

    if (upcoming === 'true') {
      whereClause.scheduledStartTime = {
        [Op.gte]: new Date()
      };
    }

    const videoCalls = await VideoCall.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'host',
          attributes: ['id', 'firstName', 'lastName']
        }
      ],
      order: [['scheduledStartTime', 'ASC']]
    });

    res.json(videoCalls);
  } catch (error) {
    console.error('Get class video calls error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get class analytics (teacher/admin only)
router.get('/:classId/analytics', authMiddleware, authorize(['teacher', 'admin']), async (req, res) => {
  try {
    const { classId } = req.params;

    const classRecord = await Class.findByPk(classId);
    if (!classRecord) {
      return res.status(404).json({ message: 'Class not found' });
    }

    // Check permissions
    if (req.user.role === 'teacher' && classRecord.teacherId !== req.user.userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Get class statistics
    const students = await Student.findAll({
      where: { classId },
      attributes: ['id', 'attendanceRate', 'performanceScore']
    });

    const analytics = {
      totalStudents: students.length,
      averageAttendance: students.length > 0 
        ? (students.reduce((sum, student) => sum + parseFloat(student.attendanceRate || 0), 0) / students.length).toFixed(2)
        : 0,
      averagePerformance: students.length > 0
        ? (students.reduce((sum, student) => sum + parseFloat(student.performanceScore || 0), 0) / students.length).toFixed(2)
        : 0,
      enrollmentRate: ((students.length / classRecord.maxStudents) * 100).toFixed(2)
    };

    res.json(analytics);
  } catch (error) {
    console.error('Get class analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;