const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const morgan = require('morgan');
const redis = require('redis');
const NodeCache = require('node-cache');

// 🚨 CRITICAL: Load environment variables FIRST before anything else
require('dotenv').config();

// Environment variables with fallbacks
const PORT = process.env.PORT || 3002;
const NODE_ENV = process.env.NODE_ENV || 'development';
const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret-key-change-in-production';
const DATABASE_URL = process.env.DATABASE_URL || 'sqlite:./dev-database.sqlite';

// Initialize caching systems
let redisClient = null;
const memoryCache = new NodeCache({
  stdTTL: 600, // 10 minutes default TTL
  checkperiod: 120, // Check for expired keys every 2 minutes
  useClones: false // Better performance
});

// Cache helper functions
const cache = {
  async get(key) {
    try {
      if (redisClient) {
        const value = await redisClient.get(key);
        return value ? JSON.parse(value) : null;
      }
      return memoryCache.get(key) || null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  },

  async set(key, value, ttl = 600) {
    try {
      if (redisClient) {
        await redisClient.setEx(key, ttl, JSON.stringify(value));
      } else {
        memoryCache.set(key, value, ttl);
      }
    } catch (error) {
      console.error('Cache set error:', error);
    }
  },

  async del(key) {
    try {
      if (redisClient) {
        await redisClient.del(key);
      } else {
        memoryCache.del(key);
      }
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }
};

const app = express();
const server = http.createServer(app);

// Bulletproof CORS configuration for development
const corsOptions = {
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:3000', 'http://127.0.0.1:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Accept-Language', 'Origin'],
  optionsSuccessStatus: 200
};

const io = socketIo(server, {
  cors: corsOptions
});

// Security Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "wss:", "https:"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Enhanced Rate Limiting with different tiers
const generalLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 200, // Increased from 100
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000) / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/api/health';
  }
});

// Stricter rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 auth requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: 900
  },
  standardHeaders: true,
  legacyHeaders: false
});

// More lenient rate limiting for health checks
const healthLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Allow 100 health checks per minute
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', generalLimiter);
app.use('/api/auth', authLimiter);
app.use('/api/health', healthLimiter);

// Compression and logging
app.use(compression());
app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));

// CORS and body parsing
app.use(cors(corsOptions));
app.use(express.json({ 
  limit: process.env.MAX_FILE_SIZE || '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialize Redis
async function initializeRedis() {
  try {
    if (process.env.REDIS_URL) {
      redisClient = redis.createClient({
        url: process.env.REDIS_URL,
        retry_strategy: (options) => {
          if (options.error && options.error.code === 'ECONNREFUSED') {
            console.log('Redis server connection refused.');
            return new Error('Redis server connection refused');
          }
          if (options.total_retry_time > 1000 * 60 * 60) {
            return new Error('Redis retry time exhausted');
          }
          if (options.attempt > 10) {
            return undefined;
          }
          return Math.min(options.attempt * 100, 3000);
        }
      });

      await redisClient.connect();
      console.log('✅ Redis connected successfully');
    } else {
      console.log('⚠️  Redis URL not provided, using memory cache only');
    }
  } catch (error) {
    console.log('⚠️  Redis connection failed, falling back to memory cache:', error.message);
    redisClient = null;
  }
}

// Enhanced Database connection with better pooling
let sequelize;
async function initializeDatabase() {
  try {
    if (NODE_ENV === 'development') {
      const { Sequelize } = require('sequelize');
      sequelize = new Sequelize({
        dialect: 'sqlite',
        storage: './dev-database.sqlite',
        logging: false, // Disable logging to reduce noise
        pool: {
          max: 10, // Increased from 5
          min: 2,  // Increased from 0
          acquire: 30000,
          idle: 10000
        },
        retry: {
          match: [
            /SQLITE_BUSY/,
          ],
          max: 3
        }
      });

      await sequelize.authenticate();
      console.log('✅ Database connection established (SQLite with enhanced pooling)');
    } else {
      const { sequelize: db } = require('./models');
      sequelize = db;
      console.log('✅ Database connection established (Production)');
    }
  } catch (error) {
    console.warn('⚠️  Database connection failed, running in test mode:', error.message);
    sequelize = null;
  }
}

// Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/messages', require('./routes/messages'));
app.use('/api/users', require('./routes/users'));
app.use('/api/students', require('./routes/students'));
app.use('/api/classes', require('./routes/classes'));
app.use('/api/webrtc', require('./routes/webrtc'));
app.use('/api/admin', require('./routes/admin'));

// Enhanced Health check route with cache status
app.get('/api/health', async (req, res) => {
  const healthData = {
    status: 'healthy',
    message: 'Backend is running!',
    timestamp: new Date().toISOString(),
    database: sequelize ? 'Connected' : 'Not connected',
    cache: redisClient ? 'Redis Connected' : 'Memory Cache Only',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: NODE_ENV
  };

  // Test cache functionality
  try {
    await cache.set('health_check', Date.now(), 60);
    const testValue = await cache.get('health_check');
    healthData.cacheTest = testValue ? 'Working' : 'Failed';
  } catch (error) {
    healthData.cacheTest = 'Error: ' + error.message;
  }

  res.json(healthData);
});

// Test route
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Backend is working!',
    timestamp: new Date().toISOString(),
    database: sequelize ? 'Connected' : 'Not connected'
  });
});

// Socket.IO connection handler
io.on('connection', (socket) => {
  console.log('Socket.IO handler loaded');
  
  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ 
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler - more helpful
app.use('*', (req, res) => {
  console.log(`404 - Route not found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ 
    message: 'Route not found',
    path: req.originalUrl,
    method: req.method,
    availableEndpoints: [
      '/api/health',
      '/api/test',
      '/api/auth/*',
      '/api/users/*',
      '/api/messages/*',
      '/api/students/*',
      '/api/classes/*',
      '/api/webrtc/*',
      '/api/admin/*'
    ]
  });
});


// Startup function to initialize all services
async function startServer() {
  try {
    console.log('🚀 Starting FamEduConnect Backend...');

    // Initialize Redis
    await initializeRedis();

    // Initialize Database
    await initializeDatabase();

    // Start the server
    server.listen(PORT, () => {
      console.log('🎉 FamEduConnect Backend Started Successfully!');
      console.log(`✅ Server running on port ${PORT}`);
      console.log(`🌍 Environment: ${NODE_ENV}`);
      console.log(`🗄️  Database: ${sequelize ? 'Connected' : 'Not connected'}`);
      console.log(`💾 Cache: ${redisClient ? 'Redis Connected' : 'Memory Cache Only'}`);
      console.log(`🔧 Port Source: ${process.env.PORT ? '.env file' : 'default (3002)'}`);
      console.log(`📡 Health Check: http://localhost:${PORT}/api/health`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Only start server if not running tests
if (process.env.NODE_ENV !== 'test') {
  startServer();
}

module.exports = { app, server, io };