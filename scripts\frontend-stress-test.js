const axios = require('axios');
const fs = require('fs');

// Configuration for frontend stress test
const CONFIG = {
  FRONTEND_URL: 'http://localhost:3000',
  TEST_DURATION: 60000, // 1 minute
  CONCURRENT_USERS: 15,
  REQUESTS_PER_SECOND: 25,
  TIMEOUT: 8000
};

// Test Results
const results = {
  pages: { success: 0, failed: 0, errors: [] },
  static: { success: 0, failed: 0, errors: [] },
  performance: { 
    responseTimes: [],
    avgResponseTime: 0,
    maxResponseTime: 0,
    minResponseTime: Infinity
  },
  startTime: Date.now()
};

// Frontend pages to test
const FRONTEND_PAGES = [
  '/',
  '/login',
  '/dashboard',
  '/messages',
  '/profile',
  '/classes',
  '/assignments'
];

// Static resources to test
const STATIC_RESOURCES = [
  '/static/css/main.css',
  '/static/js/main.js',
  '/favicon.ico',
  '/manifest.json'
];

// Performance metrics
function updatePerformanceMetrics(responseTime) {
  results.performance.responseTimes.push(responseTime);
  const times = results.performance.responseTimes;
  results.performance.avgResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
  results.performance.maxResponseTime = Math.max(results.performance.maxResponseTime, responseTime);
  results.performance.minResponseTime = Math.min(results.performance.minResponseTime, responseTime);
}

// Test frontend pages
async function testFrontendPages() {
  console.log('📱 Testing Frontend Pages...');
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    setInterval(async () => {
      try {
        const page = FRONTEND_PAGES[Math.floor(Math.random() * FRONTEND_PAGES.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.FRONTEND_URL}${page}`, {
          timeout: CONFIG.TIMEOUT,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
          }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.pages.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.pages.failed++;
          results.pages.errors.push(`Page ${page} failed: ${response.status}`);
        }
      } catch (error) {
        results.pages.failed++;
        results.pages.errors.push(`Page error: ${error.message}`);
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Test static resources
async function testStaticResources() {
  console.log('🎨 Testing Static Resources...');
  
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 2); i++) {
    setInterval(async () => {
      try {
        const resource = STATIC_RESOURCES[Math.floor(Math.random() * STATIC_RESOURCES.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.FRONTEND_URL}${resource}`, {
          timeout: CONFIG.TIMEOUT,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 400) {
          results.static.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.static.failed++;
        }
      } catch (error) {
        results.static.failed++;
      }
    }, 1000 / (CONFIG.REQUESTS_PER_SECOND / 2));
  }
}

// Check frontend health
async function checkFrontendHealth() {
  console.log('🏥 Checking Frontend Health...');
  
  try {
    const response = await axios.get(`${CONFIG.FRONTEND_URL}/`, {
      timeout: 5000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    if (response.status === 200) {
      console.log('✅ Frontend is healthy');
      console.log(`📊 Response size: ${response.data.length} bytes`);
      console.log(`📊 Content type: ${response.headers['content-type']}`);
      return true;
    } else {
      console.log(`❌ Frontend health check failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Frontend health check failed: ${error.message}`);
    return false;
  }
}

// Main frontend stress test
async function runFrontendStressTest() {
  console.log('🚀 Starting Frontend Stress Test...');
  console.log(`⏱️  Duration: ${CONFIG.TEST_DURATION / 1000}s`);
  console.log(`👥 Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`📡 Requests/Second: ${CONFIG.REQUESTS_PER_SECOND}`);
  console.log('');
  
  // Health check
  const isHealthy = await checkFrontendHealth();
  if (!isHealthy) {
    console.log('❌ Frontend is not healthy. Aborting stress test.');
    process.exit(1);
  }
  console.log('');
  
  // Start tests
  testFrontendPages();
  testStaticResources();
  
  // Progress updates
  const progressInterval = setInterval(() => {
    const elapsed = Date.now() - results.startTime;
    const progress = Math.round((elapsed / CONFIG.TEST_DURATION) * 100);
    const totalRequests = results.pages.success + results.pages.failed + 
                         results.static.success + results.static.failed;
    console.log(`📊 Progress: ${progress}% | Requests: ${totalRequests} | Avg Response: ${Math.round(results.performance.avgResponseTime)}ms`);
  }, 15000);
  
  // Complete test after duration
  setTimeout(() => {
    clearInterval(progressInterval);
    
    const totalTime = Date.now() - results.startTime;
    const totalRequests = results.pages.success + results.pages.failed + 
                         results.static.success + results.static.failed;
    const successfulRequests = results.pages.success + results.static.success;
    
    console.log('\n' + '='.repeat(60));
    console.log('📈 FRONTEND STRESS TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`⏱️  Duration: ${Math.round(totalTime / 1000)}s`);
    console.log(`📡 Total Requests: ${totalRequests}`);
    console.log(`⚡ Requests/Second: ${Math.round(totalRequests / (totalTime / 1000))}`);
    console.log(`✅ Success Rate: ${Math.round((successfulRequests / totalRequests) * 100)}%`);
    console.log('');
    
    console.log('📱 Frontend Pages:');
    console.log(`   ✅ Success: ${results.pages.success}`);
    console.log(`   ❌ Failed: ${results.pages.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.pages.success / (results.pages.success + results.pages.failed)) * 100)}%`);
    console.log('');
    
    console.log('🎨 Static Resources:');
    console.log(`   ✅ Success: ${results.static.success}`);
    console.log(`   ❌ Failed: ${results.static.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.static.success / (results.static.success + results.static.failed)) * 100)}%`);
    console.log('');
    
    console.log('⚡ Performance:');
    console.log(`   📊 Avg Response Time: ${Math.round(results.performance.avgResponseTime)}ms`);
    console.log(`   🐌 Min Response Time: ${Math.round(results.performance.minResponseTime)}ms`);
    console.log(`   🚀 Max Response Time: ${Math.round(results.performance.maxResponseTime)}ms`);
    console.log('');
    
    // Show some errors if any
    if (results.pages.errors.length > 0) {
      console.log('⚠️  Sample Page Errors:');
      results.pages.errors.slice(0, 3).forEach(error => console.log(`   • ${error}`));
      if (results.pages.errors.length > 3) {
        console.log(`   • ... and ${results.pages.errors.length - 3} more errors`);
      }
      console.log('');
    }
    
    const overallSuccessRate = Math.round((successfulRequests / totalRequests) * 100);
    if (overallSuccessRate >= 95) {
      console.log('✅ Frontend is performing excellently under stress!');
    } else if (overallSuccessRate >= 80) {
      console.log('⚠️  Frontend is performing well but could be optimized.');
    } else {
      console.log('❌ Frontend needs optimization - high failure rate.');
    }
    
    console.log('\n' + '='.repeat(60));
    
    // Save report
    const report = {
      timestamp: new Date().toISOString(),
      duration: Math.round(totalTime / 1000),
      totalRequests,
      successRate: overallSuccessRate,
      results,
      performance: {
        avgResponseTime: Math.round(results.performance.avgResponseTime),
        minResponseTime: Math.round(results.performance.minResponseTime),
        maxResponseTime: Math.round(results.performance.maxResponseTime)
      }
    };
    
    fs.writeFileSync('frontend-stress-test-report.json', JSON.stringify(report, null, 2));
    console.log('📄 Report saved to: frontend-stress-test-report.json');
    
    process.exit(0);
  }, CONFIG.TEST_DURATION);
}

// Run the test
runFrontendStressTest().catch(console.error);
