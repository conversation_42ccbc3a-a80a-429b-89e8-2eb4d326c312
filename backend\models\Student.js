module.exports = (sequelize, DataTypes) => {
  const Student = sequelize.define('Student', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    studentId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 20]
      }
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 50]
      }
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 50]
      }
    },
    grade: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isIn: [['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']]
      }
    },
    dateOfBirth: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        isDate: true,
        isBefore: new Date().toISOString()
      }
    },
    parentId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    classId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Classes',
        key: 'id'
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    attendanceRate: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 100.00,
      validate: {
        min: 0,
        max: 100
      }
    },
    performanceScore: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0.00,
      validate: {
        min: 0,
        max: 100
      }
    },
    specialNeeds: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    medicalInfo: {
      type: DataTypes.JSON,
      defaultValue: {
        allergies: [],
        medications: [],
        conditions: [],
        emergencyContacts: []
      }
    },
    emergencyContact: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    profilePicture: {
      type: DataTypes.STRING,
      allowNull: true
    },
    enrollmentDate: {
      type: DataTypes.DATEONLY,
      defaultValue: DataTypes.NOW
    },
    graduationDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    behaviorNotes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    academicGoals: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    extracurriculars: {
      type: DataTypes.JSON,
      defaultValue: []
    }
  }, {
    indexes: [
      { fields: ['studentId'] },
      { fields: ['parentId'] },
      { fields: ['classId'] },
      { fields: ['grade'] },
      { fields: ['isActive'] }
    ]
  });

  Student.associate = function(models) {
    Student.belongsTo(models.User, {
      foreignKey: 'parentId',
      as: 'parent'
    });
    Student.belongsTo(models.Class, {
      foreignKey: 'classId',
      as: 'class'
    });
    Student.hasMany(models.Attendance, {
      foreignKey: 'studentId',
      as: 'attendanceRecords'
    });
    Student.hasMany(models.Performance, {
      foreignKey: 'studentId',
      as: 'performanceRecords'
    });
  };

  return Student;
};