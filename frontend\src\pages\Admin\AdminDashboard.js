import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  CogIcon,
  UserGroupIcon,
  AcademicCapIcon,
  ChartBarIcon,
  BellIcon,
  CalendarIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    // Set greeting based on time of day
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('Good morning');
    } else if (hour < 17) {
      setGreeting('Good afternoon');
    } else {
      setGreeting('Good evening');
    }
  }, []);

  const handleSettingsClick = () => {
    navigate('/admin/settings');
  };

  const handleCompleteOnboarding = () => {
    navigate('/onboarding');
  };

  const getDashboardCards = () => [
    {
      title: 'Total Users',
      value: '1,234',
      subtitle: 'Registered users',
      icon: UserGroupIcon,
      color: 'blue',
      href: '/admin/users'
    },
    {
      title: 'Active Classes',
      value: '56',
      subtitle: 'Running classes',
      icon: AcademicCapIcon,
      color: 'green',
      href: '/admin/classes'
    },
    {
      title: 'Messages Today',
      value: '89',
      subtitle: 'System messages',
      icon: BellIcon,
      color: 'purple',
      href: '/messages'
    },
    {
      title: 'System Status',
      value: 'Online',
      subtitle: 'All systems operational',
      icon: ChartBarIcon,
      color: 'green',
      href: '/admin/reports'
    }
  ];

  const getQuickActions = () => [
    {
      title: 'Manage Users',
      description: 'View and manage user accounts',
      icon: UserGroupIcon,
      href: '/admin/users',
      color: 'blue'
    },
    {
      title: 'Class Management',
      description: 'Oversee class creation and management',
      icon: AcademicCapIcon,
      href: '/admin/classes',
      color: 'green'
    },
    {
      title: 'System Reports',
      description: 'View analytics and reports',
      icon: ChartBarIcon,
      href: '/admin/reports',
      color: 'purple'
    },
    {
      title: 'Admin Settings',
      description: 'Configure system settings',
      icon: CogIcon,
      href: '/admin/settings',
      color: 'yellow'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {greeting}, {user?.firstName || 'Administrator'}!
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Welcome to your admin dashboard
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleCompleteOnboarding}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <CogIcon className="w-4 h-4 mr-2" />
                Complete Onboarding
              </button>
              <button
                onClick={handleSettingsClick}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <CogIcon className="w-4 h-4 mr-2" />
                Admin Settings
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {getDashboardCards().map((card, index) => (
            <motion.div
              key={card.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white overflow-hidden shadow rounded-lg cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate(card.href)}
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <card.icon className={`h-6 w-6 text-${card.color}-600`} />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {card.title}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {card.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-5 py-3">
                <div className="text-sm">
                  <span className="text-gray-500">{card.subtitle}</span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {getQuickActions().map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md transition-all duration-200 hover:border-blue-300"
                onClick={() => navigate(action.href)}
              >
                <div className="flex items-center">
                  <div className={`flex-shrink-0 w-10 h-10 bg-${action.color}-100 rounded-lg flex items-center justify-center`}>
                    <action.icon className={`w-5 h-5 text-${action.color}-600`} />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-900">{action.title}</h3>
                    <p className="text-sm text-gray-500">{action.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard; 