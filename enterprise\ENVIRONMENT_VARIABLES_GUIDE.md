# FamEduConnect Enterprise Environment Variables Guide

## Overview
This guide provides comprehensive instructions for setting up all environment variables and secrets required for the FamEduConnect enterprise deployment. It covers database configuration, monitoring, security, external services, and application-specific settings.

## 🎯 Environment Variables Setup Status: READY TO CONFIGURE

### ✅ What You Need to Configure

#### 1. **Database Configuration**
**Required Variables:**
```bash
# PostgreSQL Database
DATABASE_URL=postgresql://user:password@host:port/database
DB_HOST=postgres-primary.fameduconnect.svc.cluster.local
DB_PORT=5432
DB_NAME=fameduconnect_prod
DB_USER=fameduconnect
DB_PASSWORD=<secure-password>
DB_REPLICATION_PASSWORD=<secure-password>
DB_BACKUP_PASSWORD=<secure-password>

# Redis Cache
REDIS_URL=redis://:password@host:port
REDIS_HOST=redis-cluster.fameduconnect.svc.cluster.local
REDIS_PORT=6379
REDIS_PASSWORD=<secure-password>
REDIS_CLUSTER_MODE=true
```

**Purpose:** Database connectivity, caching, and session management
**Security:** All passwords should be securely generated and base64 encoded for Kubernetes

#### 2. **JWT & Security Configuration**
**Required Variables:**
```bash
# JWT Authentication
JWT_SECRET=<secure-random-string>
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=<secure-random-string>
COOKIE_SECRET=<secure-random-string>
CSRF_SECRET=<secure-random-string>
```

**Purpose:** User authentication, session management, and security
**Security:** Use cryptographically secure random strings

#### 3. **Email Configuration**
**Required Variables:**
```bash
# SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_TEMPLATES_PATH=./email-templates
```

**Purpose:** Email notifications, password resets, and user communications
**Security:** Use app-specific passwords, not regular account passwords

#### 4. **Storage Configuration**
**Required Variables:**
```bash
# AWS S3 Storage
S3_BUCKET=fameduconnect-storage
S3_REGION=us-east-1
S3_ACCESS_KEY=your-access-key
S3_SECRET_KEY=your-secret-key
S3_ENDPOINT=https://s3.us-east-1.amazonaws.com
```

**Purpose:** File uploads, document storage, and media files
**Security:** Use IAM roles with minimal required permissions

#### 5. **Monitoring Configuration**
**Required Variables:**
```bash
# Elasticsearch
ELASTICSEARCH_HOST=elasticsearch-master.fameduconnect.svc.cluster.local
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=<secure-password>

# Monitoring URLs
GRAFANA_URL=https://grafana.fameduconnect.com
PROMETHEUS_URL=https://prometheus.fameduconnect.com
```

**Purpose:** Log aggregation, metrics collection, and monitoring dashboards
**Security:** Secure passwords and HTTPS endpoints

#### 6. **External Services**
**Optional Variables:**
```bash
# Error Tracking
SENTRY_DSN=https://your-sentry-dsn

# SMS Services
TWILIO_ACCOUNT_SID=your-account-sid
TWILIO_AUTH_TOKEN=your-auth-token

# Translation Services
GOOGLE_TRANSLATE_API_KEY=your-api-key
```

**Purpose:** Error tracking, SMS notifications, and translation features
**Security:** Use environment-specific API keys

#### 7. **SSO Configuration (Optional)**
**Required Variables (if using SSO):**
```bash
# SAML Configuration
SSO_PROVIDER=saml
SSO_ENTITY_ID=https://api.fameduconnect.com/saml/metadata
SSO_ACS_URL=https://api.fameduconnect.com/saml/acs
SSO_IDP_SSO_URL=https://your-idp.com/sso
SSO_IDP_CERT=<base64-encoded-certificate>

# OIDC Configuration
SSO_CLIENT_ID=your-client-id
SSO_CLIENT_SECRET=your-client-secret
```

**Purpose:** Single Sign-On integration with enterprise identity providers
**Security:** Secure certificates and client secrets

#### 8. **Application Configuration**
**Required Variables:**
```bash
# Server Settings
NODE_ENV=production
PORT=5555
HOST=0.0.0.0
TRUST_PROXY=true
API_VERSION=v1

# Application URLs
APP_URL=https://app.fameduconnect.com
API_URL=https://api.fameduconnect.com
ADMIN_URL=https://admin.fameduconnect.com
CORS_ORIGIN=https://app.fameduconnect.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_ENABLED=true
```

**Purpose:** Application server configuration and security settings
**Security:** Use HTTPS URLs and proper CORS configuration

#### 9. **Feature Flags**
**Configuration Variables:**
```bash
# Feature Toggles
ENABLE_VIDEO_CALLS=true
ENABLE_TRANSLATION=true
ENABLE_AI_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
```

**Purpose:** Enable/disable specific features based on environment
**Security:** Control feature access and capabilities

#### 10. **Logging Configuration**
**Required Variables:**
```bash
# Logging Settings
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=100mb
LOG_MAX_FILES=10
```

**Purpose:** Application logging and debugging
**Security:** Configure appropriate log levels for production

## 🔧 Setup Instructions

### Step 1: Generate Secure Passwords
Use the provided scripts to generate secure passwords:

```bash
# For Linux/macOS
cd FamEduConnect_Full_Codebase
./enterprise/scripts/setup-environment-variables.sh

# For Windows PowerShell
cd FamEduConnect_Full_Codebase
.\enterprise\scripts\setup-environment-variables.ps1
```

### Step 2: Configure Environment-Specific Values
The scripts will prompt you for:
- Database credentials
- Email settings
- Storage configuration
- Monitoring endpoints
- External service API keys
- SSO provider details

### Step 3: Review Generated Files
After running the script, review:
- `enterprise/environment-variables/{environment}/backend.env`
- `enterprise/environment-variables/{environment}/frontend.env`
- `enterprise/environment-variables/{environment}/mobile.env`
- `enterprise/environment-variables/{environment}/k8s-secrets/`
- `enterprise/environment-variables/{environment}/k8s-configmaps/`

### Step 4: Deploy to Kubernetes
```bash
# Navigate to your environment directory
cd enterprise/environment-variables/production

# Deploy secrets and configmaps
./deploy-env.sh  # Linux/macOS
# or
.\deploy-env.ps1  # Windows PowerShell
```

## 🔒 Security Best Practices

### Password Generation
- Use cryptographically secure random generators
- Minimum 25 characters for database passwords
- Different passwords for each service
- Regular password rotation

### Secret Management
- Never commit secrets to version control
- Use Kubernetes secrets for sensitive data
- Base64 encode all secrets for Kubernetes
- Implement secret rotation procedures

### Access Control
- Use IAM roles with minimal permissions
- Implement proper RBAC in Kubernetes
- Regular access reviews and audits
- Monitor secret access and usage

### Network Security
- Use HTTPS for all external communications
- Implement proper CORS policies
- Use VPN for database access
- Monitor network traffic and connections

## 📋 Environment-Specific Configurations

### Development Environment
```bash
NODE_ENV=development
LOG_LEVEL=debug
ENABLE_AI_FEATURES=false
RATE_LIMIT_ENABLED=false
```

### Staging Environment
```bash
NODE_ENV=staging
LOG_LEVEL=info
ENABLE_AI_FEATURES=true
RATE_LIMIT_ENABLED=true
```

### Production Environment
```bash
NODE_ENV=production
LOG_LEVEL=warn
ENABLE_AI_FEATURES=true
RATE_LIMIT_ENABLED=true
```

## 🚨 Critical Security Checklist

### Pre-Deployment
- [ ] All passwords are securely generated
- [ ] Secrets are base64 encoded for Kubernetes
- [ ] No secrets are committed to version control
- [ ] SSL certificates are valid and properly configured
- [ ] CORS policies are properly configured
- [ ] Rate limiting is enabled for production

### Post-Deployment
- [ ] Verify secrets are properly applied to Kubernetes
- [ ] Test database connectivity
- [ ] Verify email functionality
- [ ] Test file upload/download functionality
- [ ] Verify monitoring is working
- [ ] Test SSO integration (if configured)

## 🛠️ Troubleshooting

### Common Issues

#### 1. Database Connection Errors
```bash
# Check database connectivity
kubectl exec -it <pod-name> -- pg_isready -h postgres-primary -p 5432

# Verify secrets
kubectl get secrets -n fameduconnect
kubectl describe secret postgres-secret -n fameduconnect
```

#### 2. Redis Connection Issues
```bash
# Check Redis connectivity
kubectl exec -it <pod-name> -- redis-cli -h redis-cluster -p 6379 ping

# Verify Redis secrets
kubectl describe secret redis-secret -n fameduconnect
```

#### 3. Email Configuration Problems
```bash
# Test SMTP connection
kubectl exec -it <pod-name> -- telnet smtp.gmail.com 587

# Verify email secrets
kubectl describe secret email-secret -n fameduconnect
```

#### 4. Storage Access Issues
```bash
# Test S3 connectivity
kubectl exec -it <pod-name> -- aws s3 ls s3://your-bucket

# Verify storage secrets
kubectl describe secret storage-secret -n fameduconnect
```

### Useful Commands
```bash
# Check all secrets
kubectl get secrets -n fameduconnect

# Check all configmaps
kubectl get configmaps -n fameduconnect

# View secret details
kubectl describe secret <secret-name> -n fameduconnect

# Decode base64 secret
echo "base64-string" | base64 -d

# Check pod environment variables
kubectl exec -it <pod-name> -- env | grep -E "(DB_|REDIS_|JWT_)"
```

## 📞 Support & Documentation

### Available Resources
- `enterprise/ENTERPRISE_DEPLOYMENT_GUIDE.md` - Complete deployment guide
- `enterprise/CONFIGURATION_REVIEW_GUIDE.md` - Configuration review guide
- `enterprise/CONFIGURATION_SUMMARY.md` - Configuration summary

### Generated Files
After running the setup scripts, you'll have:
- Environment-specific `.env` files
- Kubernetes secret YAML files
- Kubernetes ConfigMap YAML files
- Deployment scripts
- Summary documentation

---

## 🎉 Ready for Deployment!

Your environment variables are now configured and ready for deployment. The next step is to test the functionality and verify that all components are working correctly.

**Next Action**: Run the automated DR tests and health checks to verify your configuration. 