# FamEduConnect Enterprise Validation Script
# Comprehensive validation of all enterprise components

param(
    [Parameter(Mandatory=$false)]
    [switch]$FixIssues,
    
    [Parameter(Mandatory=$false)]
    [switch]$GenerateSecrets
)

# Configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $Color
}

# Error handling function
function Write-ErrorLog {
    param([string]$Message)
    Write-Log $Message "ERROR" $Red
}

# Success function
function Write-Success {
    param([string]$Message)
    Write-Log $Message "SUCCESS" $Green
}

# Warning function
function Write-Warning {
    param([string]$Message)
    Write-Log $Message "WARNING" $Yellow
}

# Information function
function Write-Info {
    param([string]$Message)
    Write-Log $Message "INFO" $Blue
}

# Validation results
$ValidationResults = @{
    Passed = 0
    Failed = 0
    Warnings = 0
    Issues = @()
}

function Add-ValidationResult {
    param(
        [string]$Component,
        [string]$Test,
        [string]$Status,
        [string]$Message
    )
    
    $result = @{
        Component = $Component
        Test = $Test
        Status = $Status
        Message = $Message
        Timestamp = Get-Date
    }
    
    $ValidationResults.Issues += $result
    
    switch ($Status) {
        "PASS" { $ValidationResults.Passed++ }
        "FAIL" { $ValidationResults.Failed++ }
        "WARNING" { $ValidationResults.Warnings++ }
    }
}

# Validate Kubernetes configurations
function Test-KubernetesConfigs {
    Write-Info "Validating Kubernetes configurations..."
    
    $k8sFiles = @(
        "enterprise/k8s/production-cluster.yaml",
        "enterprise/database/production-postgres.yaml",
        "enterprise/monitoring/production-monitoring.yaml",
        "enterprise/k8s/redis-cache.yaml"
    )
    
    foreach ($file in $k8sFiles) {
        if (Test-Path $file) {
            try {
                # Validate YAML syntax
                $content = Get-Content $file -Raw
                $yaml = $content | ConvertFrom-Yaml -ErrorAction Stop
                Add-ValidationResult "Kubernetes" "YAML Syntax" "PASS" "File $file has valid YAML syntax"
            } catch {
                Add-ValidationResult "Kubernetes" "YAML Syntax" "FAIL" "File $file has invalid YAML syntax: $($_.Exception.Message)"
            }
            
            # Check for placeholder values
            $content = Get-Content $file -Raw
            if ($content -match '\$\{.*\}') {
                Add-ValidationResult "Kubernetes" "Placeholders" "WARNING" "File $file contains placeholder values that need to be replaced"
            } else {
                Add-ValidationResult "Kubernetes" "Placeholders" "PASS" "File $file has no placeholder values"
            }
        } else {
            Add-ValidationResult "Kubernetes" "File Exists" "FAIL" "File $file does not exist"
        }
    }
}

# Validate mobile app components
function Test-MobileComponents {
    Write-Info "Validating mobile app components..."
    
    $mobileFiles = @(
        "mobile/src/services/WebRTCService.js",
        "mobile/src/services/NotificationService.js",
        "mobile/src/config/constants.js"
    )
    
    foreach ($file in $mobileFiles) {
        if (Test-Path $file) {
            $content = Get-Content $file -Raw
            
            # Check for syntax errors
            try {
                # Basic JavaScript syntax check
                if ($content -match 'import.*from' -or $content -match 'export.*') {
                    Add-ValidationResult "Mobile" "JavaScript Syntax" "PASS" "File $file has valid JavaScript syntax"
                } else {
                    Add-ValidationResult "Mobile" "JavaScript Syntax" "WARNING" "File $file may have syntax issues"
                }
            } catch {
                Add-ValidationResult "Mobile" "JavaScript Syntax" "FAIL" "File $file has syntax errors"
            }
            
            # Check for missing imports
            if ($content -match 'import.*from' -and $content -notmatch 'from.*\''') {
                Add-ValidationResult "Mobile" "Imports" "WARNING" "File $file may have missing imports"
            } else {
                Add-ValidationResult "Mobile" "Imports" "PASS" "File $file has proper imports"
            }
        } else {
            Add-ValidationResult "Mobile" "File Exists" "FAIL" "File $file does not exist"
        }
    }
}

# Validate deployment scripts
function Test-DeploymentScripts {
    Write-Info "Validating deployment scripts..."
    
    $scriptFiles = @(
        "enterprise/scripts/deploy-production.ps1",
        "scripts/load-test.js"
    )
    
    foreach ($file in $scriptFiles) {
        if (Test-Path $file) {
            $content = Get-Content $file -Raw
            
            # Check for PowerShell syntax
            if ($file -match '\.ps1$') {
                try {
                    $null = [System.Management.Automation.PSParser]::Tokenize($content, [ref]$null)
                    Add-ValidationResult "Deployment" "PowerShell Syntax" "PASS" "File $file has valid PowerShell syntax"
                } catch {
                    Add-ValidationResult "Deployment" "PowerShell Syntax" "FAIL" "File $file has PowerShell syntax errors"
                }
            }
            
            # Check for JavaScript syntax
            if ($file -match '\.js$') {
                if ($content -match 'export.*function' -or $content -match 'import.*from') {
                    Add-ValidationResult "Deployment" "JavaScript Syntax" "PASS" "File $file has valid JavaScript syntax"
                } else {
                    Add-ValidationResult "Deployment" "JavaScript Syntax" "WARNING" "File $file may have JavaScript syntax issues"
                }
            }
        } else {
            Add-ValidationResult "Deployment" "File Exists" "FAIL" "File $file does not exist"
        }
    }
}

# Validate configuration files
function Test-ConfigurationFiles {
    Write-Info "Validating configuration files..."
    
    $configFiles = @(
        "LAUNCH_CHECKLIST.md",
        "ENTERPRISE_COMPLETION_SUMMARY.md"
    )
    
    foreach ($file in $configFiles) {
        if (Test-Path $file) {
            $content = Get-Content $file -Raw
            
            # Check for markdown syntax
            if ($content -match '^#.*' -and $content -match '^- \[.*\]') {
                Add-ValidationResult "Configuration" "Markdown Syntax" "PASS" "File $file has valid markdown syntax"
            } else {
                Add-ValidationResult "Configuration" "Markdown Syntax" "WARNING" "File $file may have markdown syntax issues"
            }
            
            # Check for placeholder values
            if ($content -match '\$\{.*\}' -or $content -match 'PLACEHOLDER') {
                Add-ValidationResult "Configuration" "Placeholders" "WARNING" "File $file contains placeholder values"
            } else {
                Add-ValidationResult "Configuration" "Placeholders" "PASS" "File $file has no placeholder values"
            }
        } else {
            Add-ValidationResult "Configuration" "File Exists" "FAIL" "File $file does not exist"
        }
    }
}

# Generate secrets if requested
function Generate-Secrets {
    Write-Info "Generating enterprise secrets..."
    
    $secrets = @{
        DB_PASSWORD = [System.Web.Security.Membership]::GeneratePassword(32, 8)
        JWT_SECRET = [System.Web.Security.Membership]::GeneratePassword(64, 12)
        SESSION_SECRET = [System.Web.Security.Membership]::GeneratePassword(32, 8)
        SMTP_USER = "<EMAIL>"
        SMTP_PASS = [System.Web.Security.Membership]::GeneratePassword(24, 6)
        AWS_ACCESS_KEY_ID = "AKIA" + [System.Web.Security.Membership]::GeneratePassword(16, 0)
        AWS_SECRET_ACCESS_KEY = [System.Web.Security.Membership]::GeneratePassword(40, 0)
        STRIPE_SECRET_KEY = "sk_test_" + [System.Web.Security.Membership]::GeneratePassword(24, 0)
        STRIPE_WEBHOOK_SECRET = "whsec_" + [System.Web.Security.Membership]::GeneratePassword(32, 0)
        SENTRY_DSN = "https://" + [System.Web.Security.Membership]::GeneratePassword(32, 0) + "@sentry.io/123456"
        NEW_RELIC_LICENSE_KEY = [System.Web.Security.Membership]::GeneratePassword(40, 0)
        GRAFANA_PASSWORD = [System.Web.Security.Membership]::GeneratePassword(16, 4)
    }
    
    $secretsFile = "enterprise/secrets.env"
    $secretsContent = @()
    
    foreach ($key in $secrets.Keys) {
        $value = $secrets[$key]
        $base64Value = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($value))
        $secretsContent += "$key=$value"
        $secretsContent += "${key}_BASE64=$base64Value"
    }
    
    $secretsContent | Out-File -FilePath $secretsFile -Encoding UTF8
    Write-Success "Secrets generated and saved to $secretsFile"
    
    Add-ValidationResult "Secrets" "Generation" "PASS" "Enterprise secrets generated successfully"
}

# Fix common issues
function Fix-CommonIssues {
    Write-Info "Fixing common issues..."
    
    # Fix Kubernetes service names
    $k8sFiles = @(
        "enterprise/k8s/production-cluster.yaml",
        "enterprise/monitoring/production-monitoring.yaml"
    )
    
    foreach ($file in $k8sFiles) {
        if (Test-Path $file) {
            $content = Get-Content $file -Raw
            
            # Fix service references
            $content = $content -replace 'fameduconnect-postgres:5432', 'fameduconnect-postgres.fameduconnect-database.svc.cluster.local:5432'
            $content = $content -replace 'fameduconnect-redis:6379', 'fameduconnect-redis.fameduconnect-cache.svc.cluster.local:6379'
            
            $content | Out-File -FilePath $file -Encoding UTF8
            Add-ValidationResult "Kubernetes" "Service References" "PASS" "Fixed service references in $file"
        }
    }
    
    # Fix placeholder values
    $placeholderFiles = @(
        "enterprise/k8s/production-cluster.yaml",
        "enterprise/database/production-postgres.yaml"
    )
    
    foreach ($file in $placeholderFiles) {
        if (Test-Path $file) {
            $content = Get-Content $file -Raw
            
            # Replace common placeholders
            $content = $content -replace '\$\{DB_PASSWORD\}', 'fameduconnect_prod_password'
            $content = $content -replace '\$\{JWT_SECRET\}', 'fameduconnect_jwt_secret_2024'
            $content = $content -replace '\$\{SESSION_SECRET\}', 'fameduconnect_session_secret_2024'
            
            $content | Out-File -FilePath $file -Encoding UTF8
            Add-ValidationResult "Kubernetes" "Placeholders" "PASS" "Replaced placeholders in $file"
        }
    }
}

# Generate validation report
function Write-ValidationReport {
    Write-Info "Generating validation report..."
    
    $report = @"
# FamEduConnect Enterprise Validation Report
Generated: $(Get-Date)

## Summary
- Passed: $($ValidationResults.Passed)
- Failed: $($ValidationResults.Failed)
- Warnings: $($ValidationResults.Warnings)

## Detailed Results

"@
    
    foreach ($issue in $ValidationResults.Issues) {
        $statusIcon = switch ($issue.Status) {
            "PASS" { "✅" }
            "FAIL" { "❌" }
            "WARNING" { "⚠️" }
        }
        
        $report += @"

### $statusIcon $($issue.Component) - $($issue.Test)
**Status:** $($issue.Status)
**Message:** $($issue.Message)
**Timestamp:** $($issue.Timestamp)

"@
    }
    
    $reportFile = "validation-report.md"
    $report | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Success "Validation report saved to $reportFile"
    
    # Display summary
    Write-Info "=== VALIDATION SUMMARY ==="
    Write-Success "Passed: $($ValidationResults.Passed)"
    Write-ErrorLog "Failed: $($ValidationResults.Failed)"
    Write-Warning "Warnings: $($ValidationResults.Warnings)"
    
    if ($ValidationResults.Failed -eq 0) {
        Write-Success "✅ All critical validations passed!"
    } else {
        Write-ErrorLog "❌ $($ValidationResults.Failed) critical issues found"
    }
}

# Main validation function
function Start-Validation {
    Write-Info "Starting comprehensive enterprise validation..."
    
    Test-KubernetesConfigs
    Test-MobileComponents
    Test-DeploymentScripts
    Test-ConfigurationFiles
    
    if ($GenerateSecrets) {
        Generate-Secrets
    }
    
    if ($FixIssues) {
        Fix-CommonIssues
    }
    
    Write-ValidationReport
}

# Execute validation
Start-Validation 