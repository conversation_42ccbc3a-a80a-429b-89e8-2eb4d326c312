const request = require('supertest');
const { app } = require('../server');
const { sequelize } = require('../models');
const dropAllEnums = require('./dropEnums');

describe('API Integration Tests', () => {
  let authToken;
  let testUser;

  beforeAll(async () => {
    await dropAllEnums(sequelize);
    // Sync database for testing
    await sequelize.sync({ force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('Authentication Endpoints', () => {
    test('POST /api/auth/register - should register a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        role: 'parent'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('token');
      expect(response.body.user).toHaveProperty('email', userData.email);
      expect(response.body.user).toHaveProperty('firstName', userData.firstName);
      expect(response.body.user).toHaveProperty('role', userData.role);
    });

    test('POST /api/auth/login - should login existing user', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('token');
      authToken = response.body.token;
      testUser = response.body.user;
    });

    test('GET /api/auth/me - should get current user profile', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('email', '<EMAIL>');
    });
  });

  describe('User Management Endpoints', () => {
    test('PUT /api/auth/profile - should update user profile', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        phone: '1234567890'
      };

      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('firstName', updateData.firstName);
      expect(response.body).toHaveProperty('lastName', updateData.lastName);
      expect(response.body).toHaveProperty('phone', updateData.phone);
    });

    test('GET /api/users - should get users list (admin only)', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('Messages Endpoints', () => {
    test('POST /api/messages - should create a new message', async () => {
      const messageData = {
        recipientId: testUser.id,
        content: 'Test message content',
        type: 'text'
      };

      const response = await request(app)
        .post('/api/messages')
        .set('Authorization', `Bearer ${authToken}`)
        .send(messageData)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('content', messageData.content);
    });

    test('GET /api/messages - should get user messages', async () => {
      const response = await request(app)
        .get('/api/messages')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('Classes Endpoints', () => {
    test('POST /api/classes - should create a new class', async () => {
      const classData = {
        name: 'Test Class',
        description: 'A test class for integration testing',
        subject: 'Mathematics',
        grade: '10th Grade'
      };

      const response = await request(app)
        .post('/api/classes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(classData)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('name', classData.name);
    });

    test('GET /api/classes - should get classes list', async () => {
      const response = await request(app)
        .get('/api/classes')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('GET /api/nonexistent - should return 404', async () => {
      await request(app)
        .get('/api/nonexistent')
        .expect(404);
    });

    test('GET /api/auth/me without token - should return 401', async () => {
      await request(app)
        .get('/api/auth/me')
        .expect(401);
    });

    test('POST /api/auth/login with invalid credentials - should return 401', async () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      await request(app)
        .post('/api/auth/login')
        .send(invalidData)
        .expect(401);
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce rate limiting on API endpoints', async () => {
      const requests = Array(105).fill().map(() => 
        request(app)
          .get('/api/test')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const responses = await Promise.all(requests);
      const tooManyRequests = responses.filter(res => res.status === 429);
      
      expect(tooManyRequests.length).toBeGreaterThan(0);
    });
  });
});