version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: fameduconnect_dev
      POSTGRES_USER: famedu_user
      POSTGRES_PASSWORD: famedu_password_123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - famedu_network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - famedu_network

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: famedu_user
      RABBITMQ_DEFAULT_PASS: famedu_password_123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - famedu_network

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:

networks:
  famedu_network:
    driver: bridge
