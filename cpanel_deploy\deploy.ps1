# FamEduConnect Frontend - cPanel Deployment Script

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "FamEduConnect Frontend - cPanel Deploy" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    # Step 1: Build frontend
    Write-Host "[1/4] Building frontend..." -ForegroundColor Yellow
    Set-Location "../frontend"
    
    if (!(Test-Path "node_modules")) {
        Write-Host "Installing dependencies..." -ForegroundColor Blue
        npm install
        if ($LASTEXITCODE -ne 0) { throw "npm install failed" }
    }
    
    npm run build
    if ($LASTEXITCODE -ne 0) { throw "Frontend build failed" }
    
    # Step 2: Copy build files
    Write-Host "[2/4] Copying build files..." -ForegroundColor Yellow
    Set-Location "../cpanel_deploy"
    
    if (Test-Path "static") {
        Remove-Item "static" -Recurse -Force
    }
    New-Item -ItemType Directory -Path "static" -Force | Out-Null
    
    Copy-Item "../frontend/build/*" "static/" -Recurse -Force
    
    # Step 3: Create deployment package
    Write-Host "[3/4] Creating deployment package..." -ForegroundColor Yellow
    
    if (Test-Path "fameduconnect-frontend.zip") {
        Remove-Item "fameduconnect-frontend.zip" -Force
    }
    
    $filesToZip = @(
        "static",
        ".htaccess",
        "config.js",
        "DEPLOYMENT_INSTRUCTIONS.md"
    )
    
    Compress-Archive -Path $filesToZip -DestinationPath "fameduconnect-frontend.zip" -Force
    
    # Step 4: Success message
    Write-Host "[4/4] Deployment package created!" -ForegroundColor Green
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "DEPLOYMENT PACKAGE READY" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "File: fameduconnect-frontend.zip" -ForegroundColor White
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Upload the zip file to your cPanel File Manager" -ForegroundColor White
    Write-Host "2. Extract it to your domain's document root" -ForegroundColor White
    Write-Host "3. Edit config.js with your production settings" -ForegroundColor White
    Write-Host "4. Test your deployment" -ForegroundColor White
    Write-Host ""
    Write-Host "See DEPLOYMENT_INSTRUCTIONS.md for detailed steps" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Green
    
} catch {
    Write-Host ""
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Deployment failed!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")