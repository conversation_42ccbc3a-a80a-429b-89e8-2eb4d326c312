import React from 'react';
import { motion } from 'framer-motion';
import { format, parseISO, isToday, isYesterday } from 'date-fns';
import { 
  UserIcon, 
  UserGroupIcon, 
  CheckCircleIcon,
  ExclamationCircleIcon 
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../UI/LoadingSpinner';

const ConversationList = ({ conversations, selectedConversation, onSelect, loading }) => {
  const formatLastMessageTime = (dateString) => {
    if (!dateString) return '';
    
    const date = parseISO(dateString);
    
    if (isToday(date)) {
      return format(date, 'HH:mm');
    } else if (isYesterday(date)) {
      return 'Yesterday';
    } else {
      return format(date, 'MMM dd');
    }
  };

  const truncateMessage = (message, maxLength = 50) => {
    if (!message) return '';
    return message.length > maxLength ? `${message.substring(0, maxLength)}...` : message;
  };

  const getConversationIcon = (conversation) => {
    if (conversation.type === 'class') {
      return <UserGroupIcon className="h-6 w-6" />;
    }
    return <UserIcon className="h-6 w-6" />;
  };

  const getStatusIndicator = (conversation) => {
    if (conversation.lastMessage?.priority === 'urgent') {
      return <ExclamationCircleIcon className="h-4 w-4 text-red-500" />;
    }
    if (conversation.isOnline) {
      return <div className="w-3 h-3 bg-green-500 rounded-full" />;
    }
    return null;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="md" text="Loading conversations..." />
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
          <UserGroupIcon className="h-8 w-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No conversations yet
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Start a conversation with teachers, parents, or classmates
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-200 dark:divide-gray-700">
      {conversations.map((conversation, index) => {
        const isSelected = selectedConversation?.id === conversation.id;
        const hasUnread = conversation.unreadCount > 0;

        return (
          <motion.button
            key={conversation.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: index * 0.05 }}
            onClick={() => onSelect(conversation)}
            className={`
              w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700 
              focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700
              transition-colors duration-150
              ${isSelected ? 'bg-indigo-50 dark:bg-indigo-900/20 border-r-2 border-indigo-500' : ''}
            `}
          >
            <div className="flex items-center space-x-3">
              {/* Avatar/Icon */}
              <div className="relative flex-shrink-0">
                <div className={`
                  w-12 h-12 rounded-full flex items-center justify-center
                  ${conversation.type === 'class' 
                    ? 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400'
                    : 'bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
                  }
                `}>
                  {conversation.avatar ? (
                    <img
                      src={conversation.avatar}
                      alt={conversation.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    getConversationIcon(conversation)
                  )}
                </div>
                
                {/* Status indicator */}
                <div className="absolute -bottom-1 -right-1">
                  {getStatusIndicator(conversation)}
                </div>
              </div>

              {/* Conversation details */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className={`
                    text-sm font-medium truncate
                    ${hasUnread 
                      ? 'text-gray-900 dark:text-white font-semibold' 
                      : 'text-gray-700 dark:text-gray-300'
                    }
                  `}>
                    {conversation.name}
                  </h3>
                  
                  <div className="flex items-center space-x-2">
                    {conversation.lastMessage && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {formatLastMessageTime(conversation.lastMessage.createdAt)}
                      </span>
                    )}
                    
                    {hasUnread && (
                      <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-indigo-600 rounded-full">
                        {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
                      </span>
                    )}
                  </div>
                </div>

                {/* Last message preview */}
                <div className="flex items-center space-x-2">
                  {conversation.lastMessage && (
                    <>
                      {conversation.lastMessage.messageType !== 'text' && (
                        <span className="text-xs text-gray-400">
                          {conversation.lastMessage.messageType === 'image' && '📷'}
                          {conversation.lastMessage.messageType === 'voice' && '🎵'}
                          {conversation.lastMessage.messageType === 'file' && '📎'}
                          {conversation.lastMessage.messageType === 'video' && '🎥'}
                        </span>
                      )}
                      
                      <p className={`
                        text-sm truncate
                        ${hasUnread 
                          ? 'text-gray-900 dark:text-white font-medium' 
                          : 'text-gray-500 dark:text-gray-400'
                        }
                      `}>
                        {conversation.lastMessage.senderId === conversation.currentUserId && (
                          <span className="text-gray-400">You: </span>
                        )}
                        {truncateMessage(conversation.lastMessage.content)}
                      </p>
                    </>
                  )}
                </div>

                {/* Conversation metadata */}
                <div className="flex items-center justify-between mt-1">
                  <div className="flex items-center space-x-2">
                    {conversation.type === 'class' && (
                      <span className="text-xs text-gray-400">
                        {conversation.participantCount} participants
                      </span>
                    )}
                    
                    {conversation.lastMessage?.isRead === false && conversation.lastMessage?.senderId !== conversation.currentUserId && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    )}
                  </div>

                  {/* Priority indicator */}
                  {conversation.lastMessage?.priority === 'urgent' && (
                    <ExclamationCircleIcon className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
            </div>
          </motion.button>
        );
      })}
    </div>
  );
};

export default ConversationList;