@echo off
echo ========================================
echo 🚀 FamEduConnect - PERFECT STARTUP
echo ========================================
echo.

echo [1/6] Killing any existing processes...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
timeout /t 3 /nobreak >nul

echo [2/6] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies.
    pause
    exit /b 1
)

echo [3/6] Starting backend FIRST...
echo Starting backend on port 3002...
start "Backend" cmd /k "cd backend && npm run dev"

echo [4/6] Waiting for backend to be ready...
timeout /t 20 /nobreak >nul

echo [5/6] Testing backend connection...
curl -s http://localhost:3002/api/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Backend not responding, waiting longer...
    timeout /t 15 /nobreak >nul
    curl -s http://localhost:3002/api/health >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Backend still not responding. Please check backend manually.
        echo Testing with curl: curl http://localhost:3002/api/health
        pause
        exit /b 1
    )
)

echo ✅ Backend is ready!

echo [6/6] Starting frontend and admin...
echo Starting frontend on port 3000...
start "Frontend" cmd /k "cd frontend && npm start"

echo Starting admin on port 3001...
start "Admin" cmd /k "cd admin && npm start"

echo.
echo ========================================
echo 🎉 ALL SERVERS STARTING!
echo ========================================
echo.
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend:  http://localhost:3002/api
echo 👨‍💼 Admin:    http://localhost:3001
echo.
echo 📋 Test Credentials:
echo    Admin:    <EMAIL> / password123
echo    Teacher:  <EMAIL> / password123
echo    Parent:   <EMAIL> / password123
echo    Student:  <EMAIL> / password123
echo.
echo ⏳ Please wait 30-60 seconds for all servers to fully start...
echo.
echo 🔍 Connection Test:
echo    Backend Health: http://localhost:3002/api/health
echo    Backend Test:   http://localhost:3002/api/test
echo.
echo 🚀 If you get connection errors:
echo    1. Wait 60 seconds for all servers to start
echo    2. Try refreshing the browser
echo    3. Check the connection indicator in the top-right corner
echo.
pause 