# FamEduConnect Frontend - cPanel Deployment Instructions

## Overview
This package contains the production-ready frontend build for FamEduConnect, optimized for cPanel hosting environments.

## Package Contents
- `static/` - Complete production build files
- `.htaccess` - Apache configuration for routing and security
- `config.js` - Environment configuration template
- `DEPLOYMENT_INSTRUCTIONS.md` - This file

## Pre-Deployment Checklist

### 1. Domain & Hosting Setup
- [ ] Domain purchased and configured
- [ ] cPanel hosting account active
- [ ] SSL certificate installed
- [ ] Subdomain created (if needed)

### 2. Backend API Setup
- [ ] Backend API deployed and accessible
- [ ] API endpoints tested and working
- [ ] CORS configured for your domain
- [ ] Database connected and migrations run

## Deployment Steps

### Step 1: Upload Files
1. Access your cPanel File Manager
2. Navigate to `public_html` (or your domain's document root)
3. Upload all files from the `static/` folder to your web root
4. Upload the `.htaccess` file to your web root
5. Ensure file permissions are set correctly (644 for files, 755 for directories)

### Step 2: Configure Environment
1. Copy `config.js` to your web root
2. Edit `config.js` with your production settings:
   ```javascript
   window.ENV = {
     API_BASE_URL: 'https://your-api-domain.com/api',
     SOCKET_URL: 'https://your-api-domain.com',
     ENVIRONMENT: 'production'
   };
   ```

### Step 3: Update API Configuration
Update your backend's CORS settings to include your frontend domain:
```javascript
// In your backend CORS configuration
const corsOptions = {
  origin: [
    'https://your-domain.com',
    'https://www.your-domain.com'
  ],
  credentials: true
};
```

### Step 4: Test Deployment
1. Visit your domain in a web browser
2. Test user registration and login
3. Verify all features work correctly
4. Check browser console for any errors
5. Test on mobile devices

## Post-Deployment Configuration

### SSL/HTTPS Setup
Ensure your site is accessible via HTTPS:
- Install SSL certificate through cPanel
- Force HTTPS redirects in .htaccess (already configured)

### Performance Optimization
- Enable Gzip compression (configured in .htaccess)
- Set up CDN if needed
- Monitor loading times

### Security Considerations
- Keep your cPanel credentials secure
- Regularly update your hosting environment
- Monitor for security updates
- Set up regular backups

## Troubleshooting

### Common Issues

**404 Errors on Page Refresh**
- Ensure `.htaccess` file is uploaded and mod_rewrite is enabled
- Check that the RewriteRule is correctly configured

**API Connection Issues**
- Verify API_BASE_URL in config.js
- Check CORS configuration on backend
- Ensure SSL certificates are valid

**Static Assets Not Loading**
- Check file permissions (644 for files, 755 for directories)
- Verify all files were uploaded correctly
- Check browser console for 404 errors

**Performance Issues**
- Enable Gzip compression
- Optimize images
- Consider using a CDN

### Getting Help
If you encounter issues:
1. Check cPanel error logs
2. Review browser console errors
3. Verify backend API is accessible
4. Contact your hosting provider for server-specific issues

## Maintenance

### Regular Tasks
- Monitor application performance
- Check for and apply security updates
- Review error logs regularly
- Backup your files regularly

### Updates
To deploy updates:
1. Build new version locally
2. Upload new files to replace existing ones
3. Clear any caches
4. Test functionality

## Support
For technical support with FamEduConnect:
- Check documentation
- Review error logs
- Test in development environment first