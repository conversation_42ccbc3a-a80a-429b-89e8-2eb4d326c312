const axios = require('axios');

console.log('🔧 FamEduConnect Debug Test');
console.log('=' * 50);

async function runDebugTest() {
  try {
    // Test 1: Backend Health
    console.log('\n1️⃣ Testing Backend Health...');
    const healthResponse = await axios.get('http://localhost:5555/api/test');
    console.log('✅ Backend Health:', healthResponse.data);

    // Test 2: Direct Login with Axios
    console.log('\n2️⃣ Testing Direct Login with Axios...');
    const loginData = {
      email: '<EMAIL>',
      password: 'AdminDemo2025!'
    };
    
    console.log('📤 Sending login data:', JSON.stringify(loginData, null, 2));
    
    const loginResponse = await axios.post('http://localhost:5555/api/auth/login', loginData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    console.log('✅ Login Response Status:', loginResponse.status);
    console.log('✅ Login Response Data:', JSON.stringify(loginResponse.data, null, 2));

    // Test 3: Test with Fetch API (simulating browser)
    console.log('\n3️⃣ Testing with Fetch API simulation...');
    const fetchResponse = await fetch('http://localhost:5555/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(loginData)
    });
    
    const fetchData = await fetchResponse.json();
    console.log('✅ Fetch Response Status:', fetchResponse.status);
    console.log('✅ Fetch Response Data:', JSON.stringify(fetchData, null, 2));

    // Test 4: Test CORS headers
    console.log('\n4️⃣ Testing CORS Headers...');
    const corsResponse = await axios.get('http://localhost:5555/api/test', {
      headers: {
        'Origin': 'http://localhost:3000'
      }
    });
    console.log('✅ CORS Test Response:', corsResponse.status);

    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Headers:', error.response.headers);
      console.error('Response Data:', error.response.data);
    }
    
    if (error.request) {
      console.error('Request Error:', error.request);
    }
  }
}

runDebugTest(); 