# Deployment Guide

## Production Deployment

### Prerequisites
- Ubuntu 20.04+ server
- Node.js 18+
- PostgreSQL 15+
- Nginx
- PM2 (for process management)
- SSL certificate

### Server Setup

1. **Install dependencies:**
```bash
sudo apt update
sudo apt install nodejs npm postgresql nginx
npm install -g pm2
```

2. **Database setup:**
```bash
sudo -u postgres createdb fameduconnect
sudo -u postgres createuser fameduconnect_user
```

3. **Clone and build:**
```bash
git clone <repository-url>
cd fameduconnect
npm install
npm run build
```

4. **Environment configuration:**
```bash
cp .env.example .env
# Edit .env with production values
```

5. **Start services:**
```bash
pm2 start backend/server.js --name fameduconnect-backend
pm2 startup
pm2 save
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        root /var/www/fameduconnect;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /socket.io {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### SSL Setup with Let's Encrypt

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

## Docker Deployment

### Production Docker Compose

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: fameduconnect
      POSTGRES_USER: fameduconnect_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  backend:
    build: ./backend
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
    depends_on:
      - postgres
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
```

### Deployment Commands

```bash
# Build and deploy
docker-compose -f docker-compose.prod.yml up -d

# Update deployment
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d --force-recreate
```

## Monitoring

### Health Checks
- Backend: `GET /health`
- Database connectivity
- Socket.IO connection status

### Logging
- Application logs: PM2 logs
- Nginx access/error logs
- Database logs

### Backup Strategy
- Daily database backups
- File upload backups
- Configuration backups