import React from 'react';
import { motion } from 'framer-motion';
import { 
  MicrophoneIcon, 
  VideoCameraIcon, 
  ComputerDesktopIcon,
  SpeakerXMarkIcon,
  VideoCameraSlashIcon 
} from '@heroicons/react/24/outline';

const ParticipantGrid = ({ participants, localStream, isScreenSharing, currentUserId }) => {
  const getGridLayout = (count) => {
    if (count <= 1) return 'grid-cols-1';
    if (count <= 4) return 'grid-cols-2';
    if (count <= 9) return 'grid-cols-3';
    return 'grid-cols-4';
  };

  const ParticipantVideo = ({ participant, isLocal = false }) => {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="relative bg-gray-900 rounded-lg overflow-hidden aspect-video"
      >
        {/* Video Element */}
        <video
          autoPlay
          muted={isLocal}
          playsInline
          className="w-full h-full object-cover"
          ref={(video) => {
            if (video && participant.stream) {
              video.srcObject = participant.stream;
            }
          }}
        />

        {/* Participant Info Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-white text-sm font-medium">
                {isLocal ? 'You' : `${participant.firstName} ${participant.lastName}`}
              </span>
              {participant.isHost && (
                <span className="text-xs bg-yellow-600 text-white px-2 py-1 rounded">
                  Host
                </span>
              )}
            </div>
            
            <div className="flex items-center space-x-1">
              {participant.isMuted && (
                <div className="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center">
                  <SpeakerXMarkIcon className="h-3 w-3 text-white" />
                </div>
              )}
              {participant.isVideoOff && (
                <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
                  <VideoCameraSlashIcon className="h-3 w-3 text-white" />
                </div>
              )}
              {participant.isScreenSharing && (
                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                  <ComputerDesktopIcon className="h-3 w-3 text-white" />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* No Video Placeholder */}
        {(participant.isVideoOff || !participant.stream) && (
          <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mb-2">
                <span className="text-white text-xl font-semibold">
                  {isLocal ? 'Y' : participant.firstName?.[0] || 'U'}
                </span>
              </div>
              <p className="text-white text-sm">
                {isLocal ? 'You' : `${participant.firstName} ${participant.lastName}`}
              </p>
            </div>
          </div>
        )}

        {/* Screen Share Indicator */}
        {participant.isScreenSharing && (
          <div className="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
            Sharing Screen
          </div>
        )}

        {/* Connection Quality Indicator */}
        <div className="absolute top-2 right-2">
          <div className={`w-3 h-3 rounded-full ${
            participant.connectionQuality === 'good' ? 'bg-green-500' :
            participant.connectionQuality === 'fair' ? 'bg-yellow-500' : 'bg-red-500'
          }`} />
        </div>
      </motion.div>
    );
  };

  // Create participant list including local user
  const allParticipants = [
    {
      id: currentUserId,
      firstName: 'You',
      lastName: '',
      stream: localStream,
      isLocal: true,
      isMuted: false,
      isVideoOff: false,
      connectionQuality: 'good'
    },
    ...participants
  ];

  return (
    <div className="h-full p-4">
      {isScreenSharing ? (
        // Screen sharing layout
        <div className="h-full flex space-x-4">
          {/* Main screen share area */}
          <div className="flex-1 bg-gray-900 rounded-lg overflow-hidden">
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center text-white">
                <ComputerDesktopIcon className="mx-auto h-16 w-16 mb-4" />
                <p className="text-lg">Screen sharing in progress</p>
              </div>
            </div>
          </div>
          
          {/* Participant thumbnails */}
          <div className="w-64 space-y-2 overflow-y-auto">
            {allParticipants.map((participant) => (
              <div key={participant.id} className="h-36">
                <ParticipantVideo 
                  participant={participant} 
                  isLocal={participant.isLocal}
                />
              </div>
            ))}
          </div>
        </div>
      ) : (
        // Regular grid layout
        <div className={`grid ${getGridLayout(allParticipants.length)} gap-4 h-full`}>
          {allParticipants.map((participant) => (
            <ParticipantVideo 
              key={participant.id}
              participant={participant} 
              isLocal={participant.isLocal}
            />
          ))}
        </div>
      )}

      {/* Empty state */}
      {allParticipants.length === 1 && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center text-white">
            <div className="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mb-4 mx-auto">
              <VideoCameraIcon className="h-12 w-12" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Waiting for others to join</h3>
            <p className="text-gray-300">Share the meeting link to invite participants</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ParticipantGrid;