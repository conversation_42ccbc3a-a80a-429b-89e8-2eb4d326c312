{"name": "fameduconnect-admin", "version": "1.0.0", "description": "FamEduConnect Admin Dashboard", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "@reduxjs/toolkit": "^1.9.5", "react-redux": "^8.1.2", "axios": "^1.5.0", "tailwindcss": "^3.3.3", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-table": "^7.8.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.15", "postcss": "^8.4.29"}, "proxy": "http://localhost:5555"}