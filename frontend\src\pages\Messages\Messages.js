import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PaperAirplaneIcon,
  PaperClipIcon,
  FaceSmileIcon,
  MicrophoneIcon,
  StopIcon,
  LanguageIcon as TranslateIcon,
  MagnifyingGlassIcon,
  UserGroupIcon,
  UserIcon,
  EllipsisVerticalIcon,
  ChatBubbleLeftIcon as ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import MessageList from '../../components/Messages/MessageList';
import ConversationList from '../../components/Messages/ConversationList';
import MessageInput from '../../components/Messages/MessageInput';
import TranslationModal from '../../components/Messages/TranslationModal';
import EmojiPicker from '../../components/UI/EmojiPicker';
import FileUpload from '../../components/UI/FileUpload';
import VoiceRecorder from '../../components/Messages/VoiceRecorder';
import { 
  fetchMessages, 
  sendMessage, 
  markAsRead,
  selectConversation 
} from '../../store/slices/messagesSlice';

const Messages = () => {
  const dispatch = useDispatch();
  const { 
    conversations, 
    messages, 
    selectedConversation, 
    loading,
    sending 
  } = useSelector((state) => state.messages);
  const { user } = useSelector((state) => state.auth);
  const { socket } = useSelector((state) => state.socket);

  const [messageText, setMessageText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [showTranslation, setShowTranslation] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showMobileConversations, setShowMobileConversations] = useState(true);
  
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);

  useEffect(() => {
    // Fetch conversations on component mount
    dispatch(fetchMessages());
  }, [dispatch]);

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Mark messages as read when conversation is selected
    if (selectedConversation && messages.length > 0) {
      const unreadMessages = messages.filter(msg => 
        !msg.isRead && msg.recipientId === user.id
      );
      
      unreadMessages.forEach(msg => {
        dispatch(markAsRead(msg.id));
      });
    }
  }, [selectedConversation, messages, user.id, dispatch]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (messageData) => {
    if (!selectedConversation) return;

    try {
      await dispatch(sendMessage({
        ...messageData,
        recipientId: selectedConversation.type === 'direct' 
          ? selectedConversation.participantId 
          : null,
        classId: selectedConversation.type === 'class' 
          ? selectedConversation.classId 
          : null
      })).unwrap();

      setMessageText('');
      setShowEmojiPicker(false);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleTextSubmit = (e) => {
    e.preventDefault();
    if (messageText.trim()) {
      handleSendMessage({
        content: messageText.trim(),
        messageType: 'text'
      });
    }
  };

  const handleFileUpload = (files) => {
    if (files.length > 0) {
      handleSendMessage({
        content: `Shared ${files.length} file(s)`,
        messageType: files[0].type.startsWith('image/') ? 'image' : 'file',
        attachments: files
      });
    }
    setShowFileUpload(false);
  };

  const handleVoiceMessage = (audioBlob) => {
    handleSendMessage({
      content: 'Voice message',
      messageType: 'voice',
      attachments: [audioBlob]
    });
    setIsRecording(false);
  };

  const handleEmojiSelect = (emoji) => {
    setMessageText(prev => prev + emoji.native);
    setShowEmojiPicker(false);
  };

  const handleConversationSelect = (conversation) => {
    dispatch(selectConversation(conversation));
    setShowMobileConversations(false);
  };

  const filteredConversations = conversations.filter(conv =>
    conv.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.lastMessage?.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="h-[calc(100vh-8rem)] flex bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden">
      {/* Conversations Sidebar */}
      <div className={`${
        showMobileConversations ? 'block' : 'hidden'
      } lg:block w-full lg:w-80 border-r border-gray-200 dark:border-gray-700 flex flex-col`}>
        {/* Search Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto">
          <ConversationList
            conversations={filteredConversations}
            selectedConversation={selectedConversation}
            onSelect={handleConversationSelect}
            loading={loading}
          />
        </div>
      </div>

      {/* Chat Area */}
      <div className={`${
        showMobileConversations ? 'hidden' : 'flex'
      } lg:flex flex-1 flex-col`}>
        {selectedConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* Back button for mobile */}
                  <button
                    onClick={() => setShowMobileConversations(true)}
                    className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>

                  {/* Conversation Info */}
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      {selectedConversation.type === 'class' ? (
                        <UserGroupIcon className="h-6 w-6 text-gray-600 dark:text-gray-300" />
                      ) : (
                        <UserIcon className="h-6 w-6 text-gray-600 dark:text-gray-300" />
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {selectedConversation.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {selectedConversation.type === 'class' 
                          ? `${selectedConversation.participantCount} participants`
                          : selectedConversation.isOnline ? 'Online' : 'Offline'
                        }
                      </p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowTranslation(true)}
                    className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    title="Translate messages"
                  >
                    <TranslateIcon className="h-5 w-5" />
                  </button>
                  
                  <button className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <EllipsisVerticalIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              <MessageList
                messages={messages}
                currentUserId={user.id}
                loading={loading}
              />
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input Area */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <form onSubmit={handleTextSubmit} className="flex items-end space-x-2">
                {/* File Upload Button */}
                <button
                  type="button"
                  onClick={() => setShowFileUpload(true)}
                  className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  title="Attach file"
                >
                  <PaperClipIcon className="h-5 w-5" />
                </button>

                {/* Message Input */}
                <div className="flex-1 relative">
                  <textarea
                    value={messageText}
                    onChange={(e) => setMessageText(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleTextSubmit(e);
                      }
                    }}
                    placeholder="Type your message..."
                    rows={1}
                    className="w-full px-4 py-2 pr-20 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none"
                    style={{ minHeight: '40px', maxHeight: '120px' }}
                  />
                  
                  {/* Emoji Button */}
                  <button
                    type="button"
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    className="absolute right-12 top-1/2 transform -translate-y-1/2 p-1 rounded-md text-gray-400 hover:text-gray-500 transition-colors"
                    title="Add emoji"
                  >
                    <FaceSmileIcon className="h-5 w-5" />
                  </button>

                  {/* Voice Recording Button */}
                  <button
                    type="button"
                    onClick={() => setIsRecording(!isRecording)}
                    className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-md transition-colors ${
                      isRecording 
                        ? 'text-red-500 hover:text-red-600' 
                        : 'text-gray-400 hover:text-gray-500'
                    }`}
                    title={isRecording ? 'Stop recording' : 'Record voice message'}
                  >
                    {isRecording ? (
                      <StopIcon className="h-5 w-5" />
                    ) : (
                      <MicrophoneIcon className="h-5 w-5" />
                    )}
                  </button>
                </div>

                {/* Send Button */}
                <button
                  type="submit"
                  disabled={!messageText.trim() || sending}
                  className="p-2 rounded-md bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  title="Send message"
                >
                  <PaperAirplaneIcon className="h-5 w-5" />
                </button>
              </form>

              {/* Emoji Picker */}
              <AnimatePresence>
                {showEmojiPicker && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="absolute bottom-20 right-4 z-50"
                  >
                    <EmojiPicker
                      onEmojiSelect={handleEmojiSelect}
                      onClose={() => setShowEmojiPicker(false)}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </>
        ) : (
          /* No Conversation Selected */
          <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-800">
            <div className="text-center">
              <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">
                Select a conversation
              </h3>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Choose a conversation from the sidebar to start messaging
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <FileUpload
        isOpen={showFileUpload}
        onClose={() => setShowFileUpload(false)}
        onUpload={handleFileUpload}
        accept="image/*,audio/*,video/*,.pdf,.doc,.docx,.txt"
        maxFiles={5}
        maxSize={10 * 1024 * 1024} // 10MB
      />

      <VoiceRecorder
        isRecording={isRecording}
        onStop={handleVoiceMessage}
        onCancel={() => setIsRecording(false)}
      />

      <TranslationModal
        isOpen={showTranslation}
        onClose={() => setShowTranslation(false)}
        messages={messages}
        targetLanguage={user?.preferredLanguage || 'en'}
      />
    </div>
  );
};

export default Messages;