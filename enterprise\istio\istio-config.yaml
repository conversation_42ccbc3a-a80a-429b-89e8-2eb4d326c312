apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  namespace: istio-system
  name: fameduconnect-istio-control-plane
spec:
  profile: production
  components:
    pilot:
      k8s:
        resources:
          requests:
            cpu: 500m
            memory: 2048Mi
          limits:
            cpu: 1000m
            memory: 4096Mi
        hpaSpec:
          maxReplicas: 5
          minReplicas: 2
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
        hpaSpec:
          maxReplicas: 5
          minReplicas: 2
        service:
          ports:
          - name: http2
            port: 80
            targetPort: 8080
          - name: https
            port: 443
            targetPort: 8443
    egressGateways:
    - name: istio-egressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 256Mi
        hpaSpec:
          maxReplicas: 5
          minReplicas: 2
  values:
    global:
      proxy:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
      defaultResources:
        requests:
          cpu: 500m
          memory: 512Mi
        limits:
          cpu: 1000m
          memory: 1Gi
      mtls:
        enabled: true
      sds:
        enabled: true
      jwtPolicy: third-party-jwt
      imagePullPolicy: IfNotPresent
      logAsJson: true
      logging:
        level: "default:info"
      meshNetworks:
        networks:
          network1:
            endpoints:
            - fromRegistry: Kubernetes
            gateways:
            - registryServiceName: istio-ingressgateway.istio-system.svc.cluster.local
              port: 15443
    pilot:
      autoscaleEnabled: true
      autoscaleMin: 2
      autoscaleMax: 5
      resources:
        requests:
          cpu: 500m
          memory: 2048Mi
        limits:
          cpu: 1000m
          memory: 4096Mi
      configSource:
        subscribedResources:
        - "type.googleapis.com/istio.networking.v1alpha3.VirtualService"
        - "type.googleapis.com/istio.networking.v1alpha3.DestinationRule"
        - "type.googleapis.com/istio.networking.v1alpha3.Gateway"
        - "type.googleapis.com/istio.networking.v1alpha3.ServiceEntry"
        - "type.googleapis.com/istio.networking.v1alpha3.Sidecar"
        - "type.googleapis.com/istio.security.v1beta1.AuthorizationPolicy"
        - "type.googleapis.com/istio.security.v1beta1.PeerAuthentication"
    gateways:
      istio-ingressgateway:
        autoscaleEnabled: true
        autoscaleMin: 2
        autoscaleMax: 5
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
        type: LoadBalancer
        ports:
        - name: http2
          port: 80
          targetPort: 8080
        - name: https
          port: 443
          targetPort: 8443
        - name: tcp
          port: 31400
          targetPort: 31400
        - name: tls
          port: 15443
          targetPort: 15443
        env:
          ISTIO_META_ROUTER_MODE: "sni-dnat"
          ISTIO_META_REQUESTED_NETWORK_VIEW: "network1"
        secretVolumes:
        - name: ingressgateway-certs
          secretName: istio-ingressgateway-certs
          mountPath: /etc/istio/ingressgateway-certs
        - name: ingressgateway-ca-certs
          secretName: istio-ingressgateway-ca-certs
          mountPath: /etc/istio/ingressgateway-ca-certs
      istio-egressgateway:
        autoscaleEnabled: true
        autoscaleMin: 2
        autoscaleMax: 5
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 256Mi
        type: ClusterIP
        ports:
        - name: http2
          port: 80
          targetPort: 8080
        - name: https
          port: 443
          targetPort: 8443
        env:
          ISTIO_META_ROUTER_MODE: "sni-dnat"
          ISTIO_META_REQUESTED_NETWORK_VIEW: "network1"
    security:
      selfSigned: false
    sidecarInjectorWebhook:
      enableNamespacesByDefault: true
      rewriteAppHTTPProbe: true
    global:
      imagePullSecrets:
      - name: istio-image-pull-secrets
---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: istio-system
spec:
  mtls:
    mode: STRICT
---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: fameduconnect-peer-auth
  namespace: fameduconnect
spec:
  mtls:
    mode: STRICT
  portLevelMtls:
    5555:
      mode: STRICT
    3000:
      mode: PERMISSIVE
    3001:
      mode: PERMISSIVE
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: fameduconnect-auth-policy
  namespace: fameduconnect
spec:
  selector:
    matchLabels:
      app: fameduconnect-backend
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/fameduconnect/sa/fameduconnect-frontend"]
    to:
    - operation:
        methods: ["GET", "POST"]
        paths: ["/api/auth/*", "/api/users/*"]
  - from:
    - source:
        principals: ["cluster.local/ns/fameduconnect/sa/fameduconnect-admin"]
    to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE"]
        paths: ["/api/admin/*"]
  - from:
    - source:
        namespaces: ["fameduconnect"]
    to:
    - operation:
        methods: ["GET"]
        paths: ["/health", "/api/test"]
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: fameduconnect-vs
  namespace: fameduconnect
spec:
  hosts:
  - "app.fameduconnect.xyz"
  - "admin.fameduconnect.xyz"
  - "api.fameduconnect.xyz"
  gateways:
  - fameduconnect-gateway
  http:
  - match:
    - uri:
        prefix: "/api"
      authority:
        exact: "api.fameduconnect.xyz"
    route:
    - destination:
        host: fameduconnect-backend
        port:
          number: 5555
      weight: 100
  - match:
    - uri:
        prefix: "/admin"
      authority:
        exact: "admin.fameduconnect.xyz"
    route:
    - destination:
        host: fameduconnect-admin
        port:
          number: 3001
      weight: 100
  - match:
    - authority:
        exact: "app.fameduconnect.xyz"
    route:
    - destination:
        host: fameduconnect-frontend
        port:
          number: 3000
      weight: 100
---
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: fameduconnect-gateway
  namespace: fameduconnect
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "app.fameduconnect.xyz"
    - "admin.fameduconnect.xyz"
    - "api.fameduconnect.xyz"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - "app.fameduconnect.xyz"
    - "admin.fameduconnect.xyz"
    - "api.fameduconnect.xyz"
    tls:
      mode: SIMPLE
      credentialName: fameduconnect-tls-secret
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: fameduconnect-destination-rule
  namespace: fameduconnect
spec:
  host: fameduconnect-backend
  trafficPolicy:
    loadBalancer:
      simple: LEAST_CONN
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 30ms
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 10
        maxRetries: 3
    outlierDetection:
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 10
  subsets:
  - name: v1
    labels:
      version: v1
    trafficPolicy:
      loadBalancer:
        simple: ROUND_ROBIN 