#!/bin/bash
# FamEduConnect Backend Deployment Script
# This script deploys the backend application to a cloud provider

# Set error handling
set -e

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_DIR=$(pwd)
BACKEND_DIR="$PROJECT_DIR/backend"
ENV_CONFIG_DIR="$PROJECT_DIR/env-configs"
DEPLOYMENT_LOG="$PROJECT_DIR/deployment-logs/backend-deployment-$(date +%Y%m%d-%H%M%S).log"
DEPLOYMENT_TARGET=${2:-heroku} # Options: heroku, digitalocean, aws

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/deployment-logs"

# Log function
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message" | tee -a "$DEPLOYMENT_LOG"
}

# Check if backend directory exists
if [ ! -d "$BACKEND_DIR" ]; then
  log "ERROR: Backend directory not found at $BACKEND_DIR"
  exit 1
fi

# Load environment variables
log "Loading environment variables for $ENVIRONMENT environment"
if [ -f "$ENV_CONFIG_DIR/backend.env" ]; then
  source "$ENV_CONFIG_DIR/backend.env"
  log "Environment variables loaded successfully"
else
  log "WARNING: Environment file not found at $ENV_CONFIG_DIR/backend.env"
  log "Continuing with default environment variables"
fi

# Navigate to backend directory
log "Changing to backend directory: $BACKEND_DIR"
cd "$BACKEND_DIR"

# Install dependencies
log "Installing dependencies..."
npm ci

# Run tests
log "Running tests..."
npm test -- --passWithNoTests

# Build the application
log "Building the application..."
npm run build

# Deploy based on target platform
log "Deploying to $DEPLOYMENT_TARGET ($ENVIRONMENT environment)..."

case $DEPLOYMENT_TARGET in
  heroku)
    # Check if Heroku CLI is installed
    if ! command -v heroku &> /dev/null; then
      log "ERROR: Heroku CLI not found. Please install it."
      exit 1
    fi
    
    # Check if user is logged in to Heroku
    heroku whoami &> /dev/null || {
      log "You are not logged in to Heroku. Please login first."
      heroku login
    }
    
    # Set Heroku app name
    HEROKU_APP="fameduconnect-api"
    if [ "$ENVIRONMENT" != "production" ]; then
      HEROKU_APP="fameduconnect-api-$ENVIRONMENT"
    fi
    
    # Check if app exists, create if not
    heroku apps:info --app $HEROKU_APP &> /dev/null || {
      log "Creating Heroku app: $HEROKU_APP"
      heroku apps:create $HEROKU_APP
    }
    
    # Set environment variables
    log "Setting environment variables on Heroku..."
    heroku config:set NODE_ENV=$ENVIRONMENT --app $HEROKU_APP
    
    # Set other environment variables from backend.env
    if [ -f "$ENV_CONFIG_DIR/backend.env" ]; then
      while IFS= read -r line || [[ -n "$line" ]]; do
        # Skip comments and empty lines
        [[ $line =~ ^#.*$ ]] && continue
        [[ -z $line ]] && continue
        
        # Extract variable name and value
        if [[ $line =~ ^([A-Za-z0-9_]+)=(.*)$ ]]; then
          VAR_NAME=${BASH_REMATCH[1]}
          VAR_VALUE=${BASH_REMATCH[2]}
          
          # Remove quotes if present
          VAR_VALUE=${VAR_VALUE//\"/}
          VAR_VALUE=${VAR_VALUE//\'/}
          
          # Skip variables with placeholders
          if [[ $VAR_VALUE == *"\${"* ]]; then
            log "Skipping variable with placeholder: $VAR_NAME"
            continue
          fi
          
          log "Setting $VAR_NAME"
          heroku config:set "$VAR_NAME=$VAR_VALUE" --app $HEROKU_APP
        fi
      done < "$ENV_CONFIG_DIR/backend.env"
    fi
    
    # Deploy to Heroku
    log "Pushing code to Heroku..."
    git push https://git.heroku.com/$HEROKU_APP.git HEAD:main
    
    # Get deployment URL
    DEPLOYMENT_URL="$HEROKU_APP.herokuapp.com"
    log "Deployment URL: https://$DEPLOYMENT_URL"
    ;;
    
  digitalocean)
    # Check if doctl is installed
    if ! command -v doctl &> /dev/null; then
      log "ERROR: DigitalOcean CLI (doctl) not found. Please install it."
      exit 1
    fi
    
    # Check if user is authenticated with DigitalOcean
    doctl account get &> /dev/null || {
      log "You are not authenticated with DigitalOcean. Please authenticate first."
      doctl auth init
    }
    
    # Set app name
    DO_APP="fameduconnect-api"
    if [ "$ENVIRONMENT" != "production" ]; then
      DO_APP="fameduconnect-api-$ENVIRONMENT"
    fi
    
    # Check if app spec exists
    if [ ! -f ".do/app.yaml" ]; then
      log "ERROR: DigitalOcean app spec not found at .do/app.yaml"
      exit 1
    fi
    
    # Deploy to DigitalOcean App Platform
    log "Deploying to DigitalOcean App Platform..."
    doctl apps create --spec .do/app.yaml || doctl apps update $DO_APP --spec .do/app.yaml
    
    # Get deployment URL
    DEPLOYMENT_URL=$(doctl apps get $DO_APP --format DefaultDomain --no-header)
    log "Deployment URL: https://$DEPLOYMENT_URL"
    ;;
    
  aws)
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
      log "ERROR: AWS CLI not found. Please install it."
      exit 1
    fi
    
    # Check if user is configured with AWS
    aws sts get-caller-identity &> /dev/null || {
      log "You are not configured with AWS. Please configure first."
      aws configure
    }
    
    # Check if Elastic Beanstalk CLI is installed
    if ! command -v eb &> /dev/null; then
      log "ERROR: AWS Elastic Beanstalk CLI not found. Please install it."
      exit 1
    }
    
    # Initialize Elastic Beanstalk if not already initialized
    if [ ! -d ".elasticbeanstalk" ]; then
      log "Initializing Elastic Beanstalk..."
      eb init fameduconnect-api --platform node.js --region us-east-1
    fi
    
    # Set environment name
    EB_ENV="fameduconnect-api-$ENVIRONMENT"
    
    # Create or update environment
    if eb status $EB_ENV &> /dev/null; then
      log "Updating existing environment: $EB_ENV"
      eb deploy $EB_ENV
    else
      log "Creating new environment: $EB_ENV"
      eb create $EB_ENV --single --timeout 20
    fi
    
    # Get deployment URL
    DEPLOYMENT_URL=$(eb status $EB_ENV | grep CNAME | awk '{print $2}')
    log "Deployment URL: http://$DEPLOYMENT_URL"
    ;;
    
  *)
    log "ERROR: Unsupported deployment target: $DEPLOYMENT_TARGET"
    log "Supported targets: heroku, digitalocean, aws"
    exit 1
    ;;
esac

# Run post-deployment checks
log "Running post-deployment checks..."

# Check if API is accessible
sleep 10 # Wait for deployment to stabilize
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$DEPLOYMENT_URL/api/health")
if [ "$HTTP_STATUS" = "200" ]; then
  log "API is accessible (HTTP 200)"
else
  log "WARNING: API returned HTTP status $HTTP_STATUS"
fi

# Return to project directory
cd "$PROJECT_DIR"

# Update deployment status file
echo "{\"backend\": {\"lastDeployed\": \"$(date +%Y-%m-%dT%H:%M:%S%z)\", \"environment\": \"$ENVIRONMENT\", \"url\": \"https://$DEPLOYMENT_URL\", \"status\": \"success\"}}" > "$PROJECT_DIR/deployment-status-backend.json"

log "Backend deployment script completed"
exit 0