import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchAnalytics } from '../../store/slices/analyticsSlice';
import StatsCard from '../../components/Dashboard/StatsCard';
import Chart from '../../components/Dashboard/Chart';

const Dashboard = () => {
  const dispatch = useDispatch();
  const { data } = useSelector((state) => state.analytics);

  useEffect(() => {
    dispatch(fetchAnalytics());
  }, [dispatch]);

  const stats = [
    { name: 'Total Users', value: data?.totalUsers || 0, change: '+12%' },
    { name: 'Active Classes', value: data?.activeClasses || 0, change: '+8%' },
    { name: 'Messages Today', value: data?.messagesToday || 0, change: '+23%' },
    { name: 'Video Calls', value: data?.videoCalls || 0, change: '+15%' },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <StatsCard key={stat.name} {...stat} />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Chart title="User Growth" data={data?.userGrowth || []} />
        <Chart title="Message Activity" data={data?.messageActivity || []} />
      </div>
    </div>
  );
};

export default Dashboard;