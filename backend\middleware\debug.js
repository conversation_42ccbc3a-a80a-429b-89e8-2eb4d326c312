const debugMiddleware = (req, res, next) => {
  console.log('\n🔍 DEBUG REQUEST:', {
    method: req.method,
    url: req.url,
    headers: {
      'content-type': req.headers['content-type'],
      'origin': req.headers['origin'],
      'user-agent': req.headers['user-agent']
    },
    body: req.body,
    rawBody: req.rawBody ? req.rawBody.toString() : 'No raw body'
  });

  // Log the original request body before parsing
  let data = '';
  req.on('data', chunk => {
    data += chunk;
  });

  req.on('end', () => {
    console.log('📦 RAW REQUEST BODY:', data);
    console.log('📦 PARSED BODY:', req.body);
  });

  next();
};

module.exports = debugMiddleware; 