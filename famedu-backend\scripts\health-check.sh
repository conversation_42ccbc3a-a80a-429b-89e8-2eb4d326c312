#!/bin/bash

echo "Checking FamEduConnect Backend Health..."

# Check if services are running
echo "Checking PostgreSQL..."
pg_isready -h localhost -p 5432 -U famedu_user || echo "PostgreSQL not ready"

echo "Checking Redis..."
redis-cli -h localhost -p 6379 ping || echo "Redis not ready"

echo "Checking RabbitMQ..."
curl -s http://localhost:15672/api/overview -u famedu_user:famedu_password_123 || echo "RabbitMQ not ready"

echo "Checking NestJS Backend..."
curl -s http://localhost:3000/health || echo "NestJS Backend not ready"

echo "Health check complete!"
