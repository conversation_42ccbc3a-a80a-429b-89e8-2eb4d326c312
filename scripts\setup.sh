#!/bin/bash

echo "Setting up FamEduConnect development environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "PostgreSQL is not installed. Please install PostgreSQL 15+ first."
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
npm install

# Install backend dependencies
echo "Installing backend dependencies..."
cd backend && npm install && cd ..

# Install frontend dependencies
echo "Installing frontend dependencies..."
cd frontend && npm install --legacy-peer-deps && cd ..

# Install admin dependencies
echo "Installing admin dependencies..."
cd admin && npm install --legacy-peer-deps && cd ..

# Install mobile dependencies
echo "Installing mobile dependencies..."
cd mobile && npm install --legacy-peer-deps && cd ..

# Create environment files
echo "Creating environment files..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Created .env file. Please update with your configuration."
fi

if [ ! -f frontend/.env ]; then
    cp frontend/.env.example frontend/.env
    echo "Created frontend/.env file."
fi

# Setup database
echo "Setting up database..."
createdb fameduconnect 2>/dev/null || echo "Database might already exist"

echo "Setup completed! Run 'npm run dev' to start development."