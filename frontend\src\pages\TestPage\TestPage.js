import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { 
  UserIcon, 
  AcademicCapIcon, 
  CogIcon, 
  ShieldCheckIcon,
  ArrowRightIcon,
  PlayIcon,
  StopIcon
} from '@heroicons/react/24/outline';
import { login, updateUserProfile } from '../../store/slices/authSlice';
import { toast } from 'react-hot-toast';

const TestPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useSelector(state => state.auth);
  
  const [testMode, setTestMode] = useState(false);
  const [selectedRole, setSelectedRole] = useState('parent');

  const testUsers = {
    parent: {
      email: '<EMAIL>',
      password: 'TestPass123!',
      role: 'parent',
      firstName: '<PERSON>',
      lastName: 'Parent'
    },
    teacher: {
      email: '<EMAIL>',
      password: 'TestPass123!',
      role: 'teacher',
      firstName: 'Sarah',
      lastName: 'Teacher'
    },
    student: {
      email: '<EMAIL>',
      password: 'TestPass123!',
      role: 'student',
      firstName: 'Alex',
      lastName: 'Student'
    },
    admin: {
      email: '<EMAIL>',
      password: 'AdminDemo2025!',
      role: 'admin',
      firstName: 'Admin',
      lastName: 'User'
    }
  };

  const handleTestLogin = async (role) => {
    setTestMode(true);
    const testUser = testUsers[role];
    
    try {
      await dispatch(login({
        email: testUser.email,
        password: testUser.password
      })).unwrap();
      
      toast.success(`Logged in as ${testUser.role}`);
      navigate('/dashboard');
    } catch (error) {
      toast.error('Test login failed. Using admin credentials.');
      // Fallback to admin login
      try {
        await dispatch(login({
          email: '<EMAIL>',
          password: 'AdminDemo2025!'
        })).unwrap();
        
        // Update user role for testing
        await dispatch(updateUserProfile({
          role: testUser.role,
          firstName: testUser.firstName,
          lastName: testUser.lastName,
          hasCompletedOnboarding: false
        })).unwrap();
        
        toast.success(`Now testing as ${testUser.role}`);
        navigate('/dashboard');
      } catch (fallbackError) {
        toast.error('All login attempts failed');
      }
    }
  };

  const handleTestOnboarding = () => {
    navigate('/onboarding');
  };

  const handleTestDashboard = () => {
    navigate('/dashboard');
  };

  const handleTestAdmin = () => {
    navigate('/admin');
  };

  const handleResetTest = async () => {
    try {
      await dispatch(updateUserProfile({
        hasCompletedOnboarding: false,
        role: 'parent'
      })).unwrap();
      toast.success('Test reset - onboarding incomplete');
    } catch (error) {
      toast.error('Failed to reset test state');
    }
  };

  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-3xl font-bold text-gray-900">🧪 Test Dashboard</h1>
              <div className="flex items-center space-x-2">
                <PlayIcon className="w-5 h-5 text-green-600" />
                <span className="text-sm text-green-600 font-medium">TEST MODE ACTIVE</span>
              </div>
            </div>

            {/* Current User Info */}
            <div className="bg-blue-50 p-4 rounded-lg mb-6">
              <h2 className="text-lg font-semibold text-blue-900 mb-2">Current Test User</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-blue-800">Name:</span>
                  <p className="text-blue-700">{user?.firstName} {user?.lastName}</p>
                </div>
                <div>
                  <span className="font-medium text-blue-800">Role:</span>
                  <p className="text-blue-700 capitalize">{user?.role}</p>
                </div>
                <div>
                  <span className="font-medium text-blue-800">Onboarding:</span>
                  <p className="text-blue-700">
                    {user?.hasCompletedOnboarding ? '✅ Complete' : '⚠️ Incomplete'}
                  </p>
                </div>
              </div>
            </div>

            {/* Test Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              <button
                onClick={handleTestOnboarding}
                className="p-4 bg-yellow-100 border border-yellow-300 rounded-lg hover:bg-yellow-200 transition-colors"
              >
                <UserIcon className="w-8 h-8 text-yellow-600 mb-2" />
                <h3 className="font-semibold text-yellow-800">Test Onboarding</h3>
                <p className="text-sm text-yellow-700">Go through onboarding flow</p>
              </button>

              <button
                onClick={handleTestDashboard}
                className="p-4 bg-blue-100 border border-blue-300 rounded-lg hover:bg-blue-200 transition-colors"
              >
                <AcademicCapIcon className="w-8 h-8 text-blue-600 mb-2" />
                <h3 className="font-semibold text-blue-800">Test Dashboard</h3>
                <p className="text-sm text-blue-700">View role-specific dashboard</p>
              </button>

              <button
                onClick={handleTestAdmin}
                className="p-4 bg-purple-100 border border-purple-300 rounded-lg hover:bg-purple-200 transition-colors"
              >
                <ShieldCheckIcon className="w-8 h-8 text-purple-600 mb-2" />
                <h3 className="font-semibold text-purple-800">Test Admin Panel</h3>
                <p className="text-sm text-purple-700">Access admin dashboard</p>
              </button>
            </div>

            {/* Role Testing */}
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Different Roles</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {Object.entries(testUsers).map(([role, userData]) => (
                  <button
                    key={role}
                    onClick={() => handleTestLogin(role)}
                    className={`p-3 rounded-lg border transition-colors ${
                      user?.role === role
                        ? 'bg-green-100 border-green-300 text-green-800'
                        : 'bg-gray-100 border-gray-300 hover:bg-gray-200'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <span className="capitalize font-medium">{role}</span>
                      {user?.role === role && (
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      )}
                    </div>
                    <p className="text-xs text-gray-600 mt-1">{userData.firstName} {userData.lastName}</p>
                  </button>
                ))}
              </div>
            </div>

            {/* Reset Test */}
            <div className="border-t border-gray-200 pt-6">
              <button
                onClick={handleResetTest}
                className="flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                <StopIcon className="w-4 h-4 mr-2" />
                Reset Test State
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
      <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🧪 FamEduConnect Test Suite</h1>
          <p className="text-gray-600">Test different user roles and scenarios</p>
        </div>

        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Select a Test Role:</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(testUsers).map(([role, userData]) => (
              <button
                key={role}
                onClick={() => handleTestLogin(role)}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-left"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-gray-900 capitalize">{role}</h3>
                    <p className="text-sm text-gray-600">{userData.firstName} {userData.lastName}</p>
                    <p className="text-xs text-gray-500">{userData.email}</p>
                  </div>
                  <ArrowRightIcon className="w-5 h-5 text-gray-400" />
                </div>
              </button>
            ))}
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Test Instructions:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Select a role to test different user experiences</li>
              <li>• Complete onboarding to test the full flow</li>
              <li>• Navigate through different dashboard sections</li>
              <li>• Test the AI assistant and voice features</li>
              <li>• Try the "Skip for now" functionality</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestPage; 