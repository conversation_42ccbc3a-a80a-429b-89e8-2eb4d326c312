import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  AcademicCapIcon,
  BellIcon,
  CalendarIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import DashboardCard from '../../components/Dashboard/DashboardCard';
import RecentMessages from '../../components/Dashboard/RecentMessages';
import UpcomingEvents from '../../components/Dashboard/UpcomingEvents';
import QuickActions from '../../components/Dashboard/QuickActions';
import PerformanceOverview from '../../components/Dashboard/PerformanceOverview';
import AttendanceWidget from '../../components/Dashboard/AttendanceWidget';
import WeatherWidget from '../../components/Dashboard/WeatherWidget';
import { fetchDashboardData } from '../../store/slices/dashboardSlice';

const Dashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const { 
    stats, 
    recentMessages, 
    upcomingEvents, 
    alerts,
    loading 
  } = useSelector((state) => state.dashboard);

  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    // Set greeting based on time of day
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('Good morning');
    } else if (hour < 17) {
      setGreeting('Good afternoon');
    } else {
      setGreeting('Good evening');
    }

    // Fetch dashboard data
    dispatch(fetchDashboardData());
  }, [dispatch]);

  const handleSettingsClick = () => {
    navigate('/settings');
  };

  const getDashboardCards = () => {
    const baseCards = [
      {
        title: 'Messages',
        value: stats?.unreadMessages || 0,
        subtitle: 'Unread messages',
        icon: ChatBubbleLeftRightIcon,
        color: 'blue',
        href: '/messages'
      },
      {
        title: 'Video Calls',
        value: stats?.upcomingCalls || 0,
        subtitle: 'Scheduled today',
        icon: VideoCameraIcon,
        color: 'green',
        href: '/video-call'
      }
    ];

    // Role-specific cards
    if (user?.role === 'parent') {
      baseCards.push(
        {
          title: 'Children',
          value: stats?.childrenCount || 0,
          subtitle: 'Enrolled students',
          icon: AcademicCapIcon,
          color: 'purple',
          href: '/students'
        },
        {
          title: 'Attendance',
          value: `${stats?.attendanceRate || 0}%`,
          subtitle: 'This month',
          icon: CheckCircleIcon,
          color: stats?.attendanceRate >= 95 ? 'green' : stats?.attendanceRate >= 85 ? 'yellow' : 'red',
          href: '/students'
        }
      );
    } else if (user?.role === 'teacher') {
      baseCards.push(
        {
          title: 'Classes',
          value: stats?.classCount || 0,
          subtitle: 'Active classes',
          icon: AcademicCapIcon,
          color: 'purple',
          href: '/classes'
        },
        {
          title: 'Students',
          value: stats?.studentCount || 0,
          subtitle: 'Total students',
          icon: AcademicCapIcon,
          color: 'blue',
          href: '/students'
        }
      );
    } else if (user?.role === 'student') {
      baseCards.push(
        {
          title: 'Assignments',
          value: stats?.pendingAssignments || 0,
          subtitle: 'Due this week',
          icon: AcademicCapIcon,
          color: 'orange',
          href: '/classes'
        },
        {
          title: 'Grades',
          value: `${stats?.averageGrade || 0}%`,
          subtitle: 'Current average',
          icon: ChartBarIcon,
          color: stats?.averageGrade >= 90 ? 'green' : stats?.averageGrade >= 80 ? 'yellow' : 'red',
          href: '/profile'
        }
      );
    } else if (user?.role === 'admin') {
      baseCards.push(
        {
          title: 'Users',
          value: stats?.totalUsers || 0,
          subtitle: 'Total users',
          icon: AcademicCapIcon,
          color: 'purple',
          href: '/admin/users'
        },
        {
          title: 'System',
          value: 'Online',
          subtitle: 'Status',
          icon: CheckCircleIcon,
          color: 'green',
          href: '/admin/settings'
        }
      );
    }

    return baseCards;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {greeting}, {user?.firstName || 'User'}!
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Welcome to your {user?.role === 'admin' ? 'administrative' : user?.role} dashboard
          </p>
        </div>
        
        {/* Settings Button */}
        <div className="flex items-center space-x-3">
          {/* Onboarding Reminder */}
          {!user?.hasCompletedOnboarding && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg px-4 py-2">
              <p className="text-sm text-yellow-800">
                ⚠️ Complete your profile setup in settings
              </p>
            </div>
          )}
          
          <button
            onClick={handleSettingsClick}
            className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Cog6ToothIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Settings
            </span>
          </button>
        </div>
      </div>

      {/* Alerts */}
      {alerts && alerts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-2"
        >
          {alerts.map((alert, index) => (
            <div
              key={index}
              className="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg"
            >
              <ExclamationTriangleIcon className="w-5 h-5 text-red-400 mr-3" />
              <span className="text-red-800">{alert.message}</span>
            </div>
          ))}
        </motion.div>
      )}

      {/* Dashboard Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        {getDashboardCards().map((card, index) => (
          <DashboardCard
            key={card.title}
            {...card}
            delay={index * 0.1}
          />
        ))}
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">
          {/* Recent Messages */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <RecentMessages messages={recentMessages} />
          </motion.div>

          {/* Performance Overview (for parents and teachers) */}
          {(user?.role === 'parent' || user?.role === 'teacher') && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <PerformanceOverview />
            </motion.div>
          )}

          {/* Attendance Widget (for parents and teachers) */}
          {(user?.role === 'parent' || user?.role === 'teacher') && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <AttendanceWidget />
            </motion.div>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <QuickActions userRole={user?.role} />
          </motion.div>

          {/* Upcoming Events */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <UpcomingEvents events={upcomingEvents} />
          </motion.div>

          {/* Calendar Widget */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Calendar
              </h3>
              <CalendarIcon className="h-5 w-5 text-gray-400" />
            </div>
            <div className="text-center py-8">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Calendar widget coming soon
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Mobile Weather Widget */}
      <div className="sm:hidden">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <WeatherWidget />
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;