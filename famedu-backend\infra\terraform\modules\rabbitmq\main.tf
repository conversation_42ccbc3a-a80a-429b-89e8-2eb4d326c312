# Amazon MQ Broker
resource "aws_mq_broker" "main" {
  broker_name = "famedu-${var.environment}-rabbitmq"

  engine_type        = "RabbitMQ"
  engine_version     = "3.11.20"
  host_instance_type = var.mq_instance_type
  security_groups    = var.security_groups
  subnet_ids         = var.subnet_ids

  user {
    username = var.mq_username
    password = var.mq_password
  }

  logs {
    general = true
    audit   = true
  }

  maintenance_window_start_time {
    day_of_week = "SUNDAY"
    time_of_day = "03:00"
    time_zone   = "UTC"
  }

  encryption_options {
    use_aws_owned_key = false
    kms_key_id        = aws_kms_key.mq.arn
  }

  tags = {
    Name = "famedu-${var.environment}-rabbitmq"
  }
}

# KMS Key for encryption
resource "aws_kms_key" "mq" {
  description             = "KMS key for Amazon MQ encryption"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  tags = {
    Name = "famedu-${var.environment}-mq-kms-key"
  }
}

resource "aws_kms_alias" "mq" {
  name          = "alias/famedu-${var.environment}-mq"
  target_key_id = aws_kms_key.mq.key_id
} 