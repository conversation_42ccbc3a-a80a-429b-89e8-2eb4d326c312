import React from 'react';

const SimpleTest = () => {
  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f3f4f6',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ 
        maxWidth: '400px', 
        width: '100%', 
        padding: '2rem',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}>
        <h1 style={{ 
          textAlign: 'center', 
          marginBottom: '2rem',
          color: '#1f2937'
        }}>
          Simple Test
        </h1>
        <p style={{ 
          textAlign: 'center',
          color: '#6b7280'
        }}>
          If you can see this, React is working!
        </p>
        <div style={{ 
          marginTop: '2rem',
          padding: '1rem',
          backgroundColor: '#fef3c7',
          borderRadius: '4px',
          border: '1px solid #f59e0b'
        }}>
          <p style={{ 
            margin: 0,
            color: '#92400e',
            fontSize: '14px'
          }}>
            Backend is running on port 5555
          </p>
        </div>
      </div>
    </div>
  );
};

export default SimpleTest; 