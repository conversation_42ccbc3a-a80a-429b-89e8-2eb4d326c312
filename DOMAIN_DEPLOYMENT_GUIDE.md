# 🌐 FamEduConnect Domain Deployment Guide

## Domain Configuration
Your three domains from Namecheap are configured as follows:

### 🏠 Landing Page: `fameduconnect.com`
- **File**: `landing.html`
- **Purpose**: Professional marketing site
- **Vercel Config**: `vercel-landing.json`
- **Features**: Multilingual support, responsive design, SEO optimized

### 📱 Application: `fameduconnect.app`
- **File**: React frontend application
- **Purpose**: Main parent-teacher communication platform
- **Vercel Config**: `vercel.json`
- **Features**: Real-time messaging, video calls, class management

### ⚙️ Admin Dashboard: `fameduconnect.xyz`
- **File**: React admin application
- **Purpose**: Administrative control panel
- **Vercel Config**: `admin-vercel.json`
- **Features**: User management, analytics, system settings

## 🚀 Deployment Commands

### Deploy All Applications
```bash
# Deploy Landing Page to fameduconnect.com
vercel --prod --name fameduconnect-landing --local-config vercel-landing.json

# Deploy Application to fameduconnect.app
vercel --prod --name fameduconnect-app --local-config vercel.json

# Deploy Admin Dashboard to fameduconnect.xyz
vercel --prod --name fameduconnect-admin --local-config admin-vercel.json
```

### Or use the automated script:
```bash
cd Deployment
./deploy.sh
```

## 🔗 Domain Linking

### In Vercel Dashboard:
1. Go to your project settings
2. Navigate to "Domains" section
3. Add custom domains:
   - `fameduconnect.com` → fameduconnect-landing
   - `fameduconnect.app` → fameduconnect-app
   - `fameduconnect.xyz` → fameduconnect-admin

### In Namecheap:
1. Go to Domain List → Manage
2. Advanced DNS settings
3. Add CNAME records pointing to Vercel:
   - `@` → `cname.vercel-dns.com`
   - `www` → `cname.vercel-dns.com`

## 🔧 Configuration Files Updated

### Backend Environment (`.env.production`)
```env
CORS_ORIGIN=https://fameduconnect.app,https://fameduconnect.xyz,https://fameduconnect.com
FRONTEND_URL=https://fameduconnect.app
ADMIN_URL=https://fameduconnect.xyz
LANDING_URL=https://fameduconnect.com
```

### Landing Page Links
- Launch App button now points to: `https://fameduconnect.app`
- Footer web app link points to: `https://fameduconnect.app`
- Canonical URL set to: `https://fameduconnect.com`
- Open Graph URL set to: `https://fameduconnect.com`

## 📋 Post-Deployment Checklist

### ✅ Verify Domain Resolution
- [ ] `https://fameduconnect.com` loads landing page
- [ ] `https://fameduconnect.app` loads application
- [ ] `https://fameduconnect.xyz` loads admin dashboard

### ✅ Test Cross-Domain Communication
- [ ] Landing page "Launch App" button works
- [ ] API calls from app to backend work
- [ ] Admin dashboard connects to backend

### ✅ SSL Certificates
- [ ] All domains have valid SSL certificates
- [ ] HTTPS redirects work properly
- [ ] Security headers are applied

### ✅ SEO & Performance
- [ ] Landing page has correct meta tags
- [ ] All domains have proper canonical URLs
- [ ] Performance scores are optimized

## 🛡️ Security Configuration

All domains are configured with:
- HTTPS enforcement
- Security headers (HSTS, CSP, etc.)
- CORS properly configured
- Rate limiting enabled

## 📞 Support Information

- **Admin Access**: `https://fameduconnect.xyz`
- **Default Admin**: `<EMAIL>`
- **API Endpoint**: Backend will be deployed separately

---
**© 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc.**  
**Creative Director**: Na'imah Barnes