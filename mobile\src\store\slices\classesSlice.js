import { createSlice } from '@reduxjs/toolkit';

const classesSlice = createSlice({
  name: 'classes',
  initialState: {
    classes: [],
    currentClass: null,
    isLoading: false,
  },
  reducers: {
    setClasses: (state, action) => {
      state.classes = action.payload;
    },
    setCurrentClass: (state, action) => {
      state.currentClass = action.payload;
    },
  },
});

export const { setClasses, setCurrentClass } = classesSlice.actions;
export default classesSlice.reducer;