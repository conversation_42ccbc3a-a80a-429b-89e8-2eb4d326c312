import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { studentAPI } from '../../services/api';
import { toast } from 'react-hot-toast';

// Async thunks
export const fetchStudents = createAsyncThunk(
  'students/fetchStudents',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await studentAPI.getStudents(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch students');
    }
  }
);

export const fetchStudent = createAsyncThunk(
  'students/fetchStudent',
  async (studentId, { rejectWithValue }) => {
    try {
      const response = await studentAPI.getStudent(studentId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch student');
    }
  }
);

export const createStudent = createAsyncThunk(
  'students/createStudent',
  async (studentData, { rejectWithValue }) => {
    try {
      const response = await studentAPI.createStudent(studentData);
      toast.success('Student created successfully!');
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to create student';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const updateStudent = createAsyncThunk(
  'students/updateStudent',
  async ({ studentId, updates }, { rejectWithValue }) => {
    try {
      const response = await studentAPI.updateStudent(studentId, updates);
      toast.success('Student updated successfully!');
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to update student';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const fetchStudentAttendance = createAsyncThunk(
  'students/fetchAttendance',
  async ({ studentId, params }, { rejectWithValue }) => {
    try {
      const response = await studentAPI.getAttendance(studentId, params);
      return { studentId, data: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch attendance');
    }
  }
);

export const fetchStudentPerformance = createAsyncThunk(
  'students/fetchPerformance',
  async ({ studentId, params }, { rejectWithValue }) => {
    try {
      const response = await studentAPI.getPerformance(studentId, params);
      return { studentId, data: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch performance');
    }
  }
);

const initialState = {
  students: [],
  selectedStudent: null,
  attendance: {},
  performance: {},
  loading: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0
  },
  filters: {
    search: '',
    classId: null,
    grade: null
  }
};

const studentsSlice = createSlice({
  name: 'students',
  initialState,
  reducers: {
    setSelectedStudent: (state, action) => {
      state.selectedStudent = action.payload;
    },
    clearSelectedStudent: (state) => {
      state.selectedStudent = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        search: '',
        classId: null,
        grade: null
      };
    },
    updateStudentInList: (state, action) => {
      const updatedStudent = action.payload;
      const index = state.students.findIndex(s => s.id === updatedStudent.id);
      if (index !== -1) {
        state.students[index] = updatedStudent;
      }
    },
    removeStudentFromList: (state, action) => {
      const studentId = action.payload;
      state.students = state.students.filter(s => s.id !== studentId);
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Students
      .addCase(fetchStudents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStudents.fulfilled, (state, action) => {
        state.loading = false;
        state.students = action.payload.students || [];
        state.pagination = {
          currentPage: action.payload.currentPage || 1,
          totalPages: action.payload.totalPages || 1,
          totalCount: action.payload.totalCount || 0
        };
      })
      .addCase(fetchStudents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Fetch Student
      .addCase(fetchStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedStudent = action.payload;
      })
      .addCase(fetchStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create Student
      .addCase(createStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.students.unshift(action.payload);
        state.pagination.totalCount += 1;
      })
      .addCase(createStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update Student
      .addCase(updateStudent.fulfilled, (state, action) => {
        const updatedStudent = action.payload;
        const index = state.students.findIndex(s => s.id === updatedStudent.id);
        if (index !== -1) {
          state.students[index] = updatedStudent;
        }
        if (state.selectedStudent && state.selectedStudent.id === updatedStudent.id) {
          state.selectedStudent = updatedStudent;
        }
      })
      
      // Fetch Attendance
      .addCase(fetchStudentAttendance.fulfilled, (state, action) => {
        const { studentId, data } = action.payload;
        state.attendance[studentId] = data;
      })
      
      // Fetch Performance
      .addCase(fetchStudentPerformance.fulfilled, (state, action) => {
        const { studentId, data } = action.payload;
        state.performance[studentId] = data;
      });
  }
});

export const {
  setSelectedStudent,
  clearSelectedStudent,
  setFilters,
  clearFilters,
  updateStudentInList,
  removeStudentFromList,
  setError,
  clearError
} = studentsSlice.actions;

export default studentsSlice.reducer;