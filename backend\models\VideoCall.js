module.exports = (sequelize, DataTypes) => {
  const VideoCall = sequelize.define('VideoCall', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    callId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    hostId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    classId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Classes',
        key: 'id'
      }
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 200]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    scheduledStartTime: {
      type: DataTypes.DATE,
      allowNull: false
    },
    scheduledEndTime: {
      type: DataTypes.DATE,
      allowNull: false
    },
    actualStartTime: {
      type: DataTypes.DATE,
      allowNull: true
    },
    actualEndTime: {
      type: DataTypes.DATE,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('scheduled', 'active', 'ended', 'cancelled'),
      defaultValue: 'scheduled'
    },
    callType: {
      type: DataTypes.ENUM('parent-teacher', 'class-meeting', 'group-discussion', 'emergency'),
      defaultValue: 'parent-teacher'
    },
    maxParticipants: {
      type: DataTypes.INTEGER,
      defaultValue: 10,
      validate: {
        min: 2,
        max: 50
      }
    },
    currentParticipants: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    isRecorded: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    recordingUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    transcriptionUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    hasTranslation: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    translationLanguages: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    meetingSettings: {
      type: DataTypes.JSON,
      defaultValue: {
        allowScreenShare: true,
        allowChat: true,
        allowRecording: true,
        muteOnJoin: false,
        waitingRoom: false,
        autoTranscription: true,
        liveTranslation: false
      }
    },
    participants: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    chatHistory: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    meetingNotes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    actionItems: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    followUpRequired: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    followUpDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    quality: {
      type: DataTypes.JSON,
      defaultValue: {
        averageRating: 0,
        connectionIssues: 0,
        audioQuality: 0,
        videoQuality: 0
      }
    }
  }, {
    indexes: [
      { fields: ['callId'] },
      { fields: ['hostId'] },
      { fields: ['classId'] },
      { fields: ['status'] },
      { fields: ['callType'] },
      { fields: ['scheduledStartTime'] },
      { fields: ['actualStartTime'] }
    ]
  });

  VideoCall.associate = function(models) {
    VideoCall.belongsTo(models.User, {
      foreignKey: 'hostId',
      as: 'host'
    });
    VideoCall.belongsTo(models.Class, {
      foreignKey: 'classId',
      as: 'class'
    });
    VideoCall.belongsToMany(models.User, {
      through: 'VideoCallParticipants',
      foreignKey: 'videoCallId',
      as: 'participantUsers'
    });
  };

  return VideoCall;
};