const axios = require('axios');

// Quick stress test configuration
const CONFIG = {
  BACKEND_URL: 'http://localhost:3002',
  FRONTEND_URL: 'http://localhost:3000',
  TEST_DURATION: 60000, // 1 minute
  CONCURRENT_USERS: 10,
  REQUESTS_PER_SECOND: 20
};

const results = {
  backend: { success: 0, failed: 0 },
  frontend: { success: 0, failed: 0 },
  startTime: Date.now()
};

// Quick backend test
async function quickBackendTest() {
  console.log('🔧 Quick Backend Stress Test...');
  
  const endpoints = ['/api/health', '/api/test', '/api/auth/login'];
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    setInterval(async () => {
      try {
        const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
        const response = await axios.get(`${CONFIG.BACKEND_URL}${endpoint}`, {
          timeout: 5000
        });
        
        if (response.status >= 200 && response.status < 500) {
          results.backend.success++;
        } else {
          results.backend.failed++;
        }
      } catch (error) {
        results.backend.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Quick frontend test
async function quickFrontendTest() {
  console.log('📱 Quick Frontend Stress Test...');
  
  const endpoints = ['/', '/login', '/dashboard'];
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    setInterval(async () => {
      try {
        const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
        const response = await axios.get(`${CONFIG.FRONTEND_URL}${endpoint}`, {
          timeout: 5000
        });
        
        if (response.status === 200) {
          results.frontend.success++;
        } else {
          results.frontend.failed++;
        }
      } catch (error) {
        results.frontend.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Main quick test
async function runQuickTest() {
  console.log('🚀 Starting Quick Stress Test (1 minute)...');
  console.log(`👥 Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`📡 Requests/Second: ${CONFIG.REQUESTS_PER_SECOND}`);
  console.log('');
  
  quickBackendTest();
  quickFrontendTest();
  
  setTimeout(() => {
    const totalTime = Date.now() - results.startTime;
    const totalRequests = results.backend.success + results.backend.failed + 
                         results.frontend.success + results.frontend.failed;
    
    console.log('\n' + '='.repeat(50));
    console.log('📈 QUICK STRESS TEST RESULTS');
    console.log('='.repeat(50));
    console.log(`⏱️  Duration: ${Math.round(totalTime / 1000)}s`);
    console.log(`📡 Total Requests: ${totalRequests}`);
    console.log(`⚡ Requests/Second: ${Math.round(totalRequests / (totalTime / 1000))}`);
    console.log('');
    
    console.log('🔧 Backend:');
    console.log(`   ✅ Success: ${results.backend.success}`);
    console.log(`   ❌ Failed: ${results.backend.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.backend.success / (results.backend.success + results.backend.failed)) * 100)}%`);
    console.log('');
    
    console.log('📱 Frontend:');
    console.log(`   ✅ Success: ${results.frontend.success}`);
    console.log(`   ❌ Failed: ${results.frontend.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.frontend.success / (results.frontend.success + results.frontend.failed)) * 100)}%`);
    console.log('');
    
    const overallSuccessRate = Math.round(((results.backend.success + results.frontend.success) / totalRequests) * 100);
    console.log(`🎯 Overall Success Rate: ${overallSuccessRate}%`);
    
    if (overallSuccessRate >= 95) {
      console.log('✅ System is performing excellently under stress!');
    } else if (overallSuccessRate >= 80) {
      console.log('⚠️  System is performing well but could be optimized.');
    } else {
      console.log('❌ System needs optimization - high failure rate.');
    }
    
    console.log('\n' + '='.repeat(50));
    
    process.exit(0);
  }, CONFIG.TEST_DURATION);
}

runQuickTest().catch(console.error); 