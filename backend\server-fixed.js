const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const morgan = require('morgan');

// Load environment variables with fallbacks
require('dotenv').config();

// Environment variables with fallbacks
const PORT = process.env.PORT || 5555;
const NODE_ENV = process.env.NODE_ENV || 'development';
const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret-key-change-in-production';
const DATABASE_URL = process.env.DATABASE_URL || 'sqlite:./dev-database.sqlite';

console.log('🚀 Starting FamEduConnect Server...');
console.log(`📊 Environment: ${NODE_ENV}`);
console.log(`🔌 Port: ${PORT}`);
console.log(`🔑 JWT Secret: ${JWT_SECRET ? 'Set' : 'Missing'}`);
console.log(`🗄️ Database URL: ${DATABASE_URL}`);

const app = express();
const server = http.createServer(app);

// Production-ready CORS configuration
const corsOptions = {
  origin: NODE_ENV === 'production' ? 
    ['https://fameduconnect.xyz', 'https://app.fameduconnect.xyz', 'https://admin.fameduconnect.xyz'] : 
    ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8080', 'http://127.0.0.1:3000'],
  credentials: false,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Accept-Language']
};

const io = socketIo(server, {
  cors: corsOptions
});

// Security Middleware with fallbacks
app.use(helmet({
  contentSecurityPolicy: NODE_ENV === 'production' ? {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "wss:", "https:"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  } : false,
  hsts: NODE_ENV === 'production' ? {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  } : false
}));

// Rate Limiting with fallbacks
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000) / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', limiter);

// Compression and logging
app.use(compression());
app.use(morgan(NODE_ENV === 'production' ? 'combined' : 'dev'));

// CORS and body parsing
app.use(cors(corsOptions));
app.use(express.json({ 
  limit: process.env.MAX_FILE_SIZE || '10mb',
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Database connection with proper error handling
let sequelize = null;
let dbConnected = false;

// Initialize database connection
const initializeDatabase = async () => {
  try {
    console.log('🗄️ Attempting database connection...');
    
    // Simple SQLite setup for development
    if (NODE_ENV === 'development') {
      const { Sequelize } = require('sequelize');
      sequelize = new Sequelize({
        dialect: 'sqlite',
        storage: './dev-database.sqlite',
        logging: false, // Disable logging to avoid console spam
        pool: {
          max: 5,
          min: 0,
          acquire: 30000,
          idle: 10000
        }
      });
      
      // Test the connection
      await sequelize.authenticate();
      console.log('✅ Database connection established (SQLite)');
      dbConnected = true;
    } else {
      // Try to load the full models setup
      const { sequelize: db } = require('./models');
      sequelize = db;
      console.log('✅ Database connection established (Full models)');
      dbConnected = true;
    }
  } catch (error) {
    console.warn('⚠️ Database connection failed, running in test mode:', error.message);
    console.log('📝 Server will run without database functionality');
    sequelize = null;
    dbConnected = false;
  }
};

// Basic routes that don't require database
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: NODE_ENV,
    database: dbConnected ? 'Connected' : 'Not connected'
  });
});

app.get('/api/test', (req, res) => {
  res.json({
    message: 'Backend is working!',
    timestamp: new Date().toISOString(),
    database: dbConnected ? 'Connected' : 'Not connected',
    environment: NODE_ENV
  });
});

// Mock auth routes for testing
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Simple mock authentication
  if (email === '<EMAIL>' && password === 'password123') {
    res.json({
      message: 'Login successful',
      token: 'mock-jwt-token-' + Date.now(),
      user: {
        id: 1,
        email: email,
        firstName: 'Test',
        lastName: 'User',
        role: 'parent',
        preferredLanguage: 'en'
      }
    });
  } else {
    res.status(401).json({ message: 'Invalid credentials' });
  }
});

app.post('/api/auth/register', (req, res) => {
  const { email, password, firstName, lastName, role } = req.body;
  
  // Simple mock registration
  res.status(201).json({
    message: 'User created successfully',
    token: 'mock-jwt-token-' + Date.now(),
    user: {
      id: 2,
      email: email,
      firstName: firstName,
      lastName: lastName,
      role: role,
      preferredLanguage: 'en'
    }
  });
});

app.get('/api/users/profile', (req, res) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  
  if (token) {
    res.json({
      id: 1,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'parent',
      preferredLanguage: 'en'
    });
  } else {
    res.status(401).json({ message: 'No token provided' });
  }
});

// Load full routes only if database is connected
const loadFullRoutes = () => {
  if (dbConnected) {
    try {
      console.log('🔄 Loading full application routes...');
      app.use('/api/auth', require('./routes/auth'));
      app.use('/api/messages', require('./routes/messages'));
      app.use('/api/users', require('./routes/users'));
      app.use('/api/students', require('./routes/students'));
      app.use('/api/classes', require('./routes/classes'));
      app.use('/api/webrtc', require('./routes/webrtc'));
      app.use('/api/admin', require('./routes/admin'));
      console.log('✅ Full application routes loaded');
    } catch (error) {
      console.warn('⚠️ Failed to load some routes:', error.message);
      console.log('📝 Server running with basic routes only');
    }
  }
};

// Socket.IO connection handler
io.on('connection', (socket) => {
  console.log('🔌 Socket.IO client connected');
  
  socket.on('disconnect', () => {
    console.log('🔌 Socket.IO client disconnected');
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('❌ Server Error:', err);
  res.status(500).json({ 
    message: 'Internal server error',
    error: NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Route not found' });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    if (sequelize) {
      sequelize.close();
    }
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    if (sequelize) {
      sequelize.close();
    }
    process.exit(0);
  });
});

// Start server with error handling
const startServer = async () => {
  try {
    // Initialize database first
    await initializeDatabase();
    
    // Load full routes if database is connected
    loadFullRoutes();
    
    // Start the server
    server.listen(PORT, (error) => {
      if (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
      }
      
      console.log('🎉 FamEduConnect server running successfully!');
      console.log(`🌐 Server URL: http://localhost:${PORT}`);
      console.log(`📡 API URL: http://localhost:${PORT}/api`);
      console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/test`);
      console.log(`❤️ Health check: http://localhost:${PORT}/api/health`);
      console.log(`🗄️ Database: ${dbConnected ? 'Connected' : 'Not connected'}`);
      console.log(`🔧 Environment: ${NODE_ENV}`);
    });
  } catch (error) {
    console.error('❌ Failed to initialize server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();

module.exports = { app, server, io }; 