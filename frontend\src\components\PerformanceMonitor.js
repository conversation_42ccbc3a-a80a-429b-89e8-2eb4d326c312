import React, { useState, useEffect } from 'react';
import { apiPerformance } from '../services/optimizedApi';

const PerformanceMonitor = () => {
  const [stats, setStats] = useState(null);
  const [slowRequests, setSlowRequests] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateStats = () => {
      setStats(apiPerformance.getStats());
      setSlowRequests(apiPerformance.getSlowestRequests(5));
    };

    // Update stats every 5 seconds
    const interval = setInterval(updateStats, 5000);
    updateStats(); // Initial update

    return () => clearInterval(interval);
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-2 rounded-lg text-sm z-50"
      >
        📊 Performance
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-sm">API Performance</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      {stats && (
        <div className="space-y-2 text-xs">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <span className="text-gray-600">Total Requests:</span>
              <div className="font-semibold">{stats.total}</div>
            </div>
            <div>
              <span className="text-gray-600">Success Rate:</span>
              <div className={`font-semibold ${stats.successRate >= 95 ? 'text-green-600' : stats.successRate >= 80 ? 'text-yellow-600' : 'text-red-600'}`}>
                {stats.successRate.toFixed(1)}%
              </div>
            </div>
            <div>
              <span className="text-gray-600">Avg Duration:</span>
              <div className={`font-semibold ${stats.avgDuration < 500 ? 'text-green-600' : stats.avgDuration < 1000 ? 'text-yellow-600' : 'text-red-600'}`}>
                {stats.avgDuration}ms
              </div>
            </div>
            <div>
              <span className="text-gray-600">Failed:</span>
              <div className={`font-semibold ${stats.failed === 0 ? 'text-green-600' : 'text-red-600'}`}>
                {stats.failed}
              </div>
            </div>
          </div>

          {slowRequests.length > 0 && (
            <div className="mt-3">
              <div className="text-gray-600 mb-1">Slowest Requests:</div>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {slowRequests.map((request, index) => (
                  <div key={index} className="text-xs bg-gray-50 p-1 rounded">
                    <div className="font-mono text-xs truncate">
                      {request.method} {request.url}
                    </div>
                    <div className={`text-xs ${request.duration > 2000 ? 'text-red-600' : request.duration > 1000 ? 'text-yellow-600' : 'text-green-600'}`}>
                      {request.duration}ms
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
