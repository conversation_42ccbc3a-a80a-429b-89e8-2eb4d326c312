# Default values for famedu-backend
replicaCount: 2

image:
  repository: ghcr.io/your-org/famedu-backend
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80
  targetPort: 3000

ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
  hosts:
    - host: api.fameduconnect.xyz
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: famedu-backend-tls
      hosts:
        - api.fameduconnect.xyz

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

env:
  NODE_ENV: production
  PORT: 3000

secrets:
  DATABASE_URL: ""
  JWT_SECRET: ""
  REDIS_URL: ""
  RABBITMQ_URL: ""
  SENTRY_DSN: ""

configMap:
  LOG_LEVEL: info
  ENABLE_METRICS: "true"

# Database migration job
migration:
  enabled: true
  image:
    repository: ghcr.io/your-org/famedu-backend
    tag: "latest"
  env:
    NODE_ENV: production
  secrets:
    DATABASE_URL: ""

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
    path: /metrics
    port: http 