{"version": 2, "env": {"REACT_APP_API_URL": "https://api.fameduconnect.xyz", "REACT_APP_SOCKET_URL": "wss://api.fameduconnect.xyz", "REACT_APP_ENVIRONMENT": "production", "REACT_APP_VERSION": "1.0.0", "REACT_APP_AUTH_DOMAIN": "auth.fameduconnect.xyz", "REACT_APP_JWT_EXPIRATION": "86400", "REACT_APP_ENABLE_MFA": "true", "REACT_APP_ENABLE_VIDEO_CALLS": "true", "REACT_APP_ENABLE_FILE_SHARING": "true", "REACT_APP_ENABLE_NOTIFICATIONS": "true", "REACT_APP_ENABLE_ANALYTICS": "true", "REACT_APP_ENABLE_TRANSLATION": "true", "REACT_APP_ENABLE_OFFLINE_MODE": "true", "REACT_APP_FIREBASE_API_KEY": "@firebase_api_key", "REACT_APP_FIREBASE_AUTH_DOMAIN": "@firebase_auth_domain", "REACT_APP_FIREBASE_PROJECT_ID": "@firebase_project_id", "REACT_APP_FIREBASE_STORAGE_BUCKET": "@firebase_storage_bucket", "REACT_APP_FIREBASE_MESSAGING_SENDER_ID": "@firebase_messaging_sender_id", "REACT_APP_FIREBASE_APP_ID": "@firebase_app_id", "REACT_APP_FIREBASE_MEASUREMENT_ID": "@firebase_measurement_id", "REACT_APP_ICE_SERVERS": "@ice_servers", "REACT_APP_MEDIA_SERVER_URL": "@media_server_url", "REACT_APP_MAX_VIDEO_PARTICIPANTS": "16", "REACT_APP_VIDEO_QUALITY": "high", "REACT_APP_GOOGLE_ANALYTICS_ID": "@google_analytics_id", "REACT_APP_ENABLE_ERROR_REPORTING": "true", "REACT_APP_SENTRY_DSN": "@sentry_dsn", "REACT_APP_GUARDIAN_AI_KEY": "@guardian_ai_key", "REACT_APP_DEFAULT_LANGUAGE": "en", "REACT_APP_SUPPORTED_LANGUAGES": "en,ar,es,fr,de,zh", "REACT_APP_RTL_LANGUAGES": "ar", "REACT_APP_ENABLE_CSP": "true", "REACT_APP_SECURE_COOKIES": "true", "REACT_APP_HTTPS_ONLY": "true", "REACT_APP_ENABLE_COMPRESSION": "true", "REACT_APP_ENABLE_CACHING": "true", "REACT_APP_ENABLE_SERVICE_WORKER": "true", "REACT_APP_ENABLE_LAZY_LOADING": "true", "REACT_APP_SUPPORT_EMAIL": "<EMAIL>", "REACT_APP_HELP_CENTER_URL": "https://help.fameduconnect.xyz", "REACT_APP_TERMS_URL": "https://fameduconnect.xyz/terms", "REACT_APP_PRIVACY_URL": "https://fameduconnect.xyz/privacy", "REACT_APP_CONTACT_URL": "https://fameduconnect.xyz/contact"}, "build": {"env": {"REACT_APP_API_URL": "https://api.fameduconnect.xyz", "REACT_APP_SOCKET_URL": "wss://api.fameduconnect.xyz", "REACT_APP_ENVIRONMENT": "production"}}, "routes": [{"src": "/static/(.*)", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "dest": "/static/$1"}, {"src": "/favicon.ico", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "dest": "/favicon.ico"}, {"src": "/logo192.png", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "dest": "/logo192.png"}, {"src": "/logo512.png", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "dest": "/logo512.png"}, {"src": "/manifest.json", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "dest": "/manifest.json"}, {"src": "/robots.txt", "headers": {"cache-control": "public, max-age=31536000, immutable"}, "dest": "/robots.txt"}, {"src": "/(.*)", "headers": {"X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Content-Security-Policy": "default-src 'self'; script-src 'self' https://storage.fameduconnect.xyz https://www.google-analytics.com 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https://storage.fameduconnect.xyz; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://api.fameduconnect.xyz wss://api.fameduconnect.xyz https://sentry.io; media-src 'self' https://storage.fameduconnect.xyz; object-src 'none'; frame-ancestors 'self'; form-action 'self'; upgrade-insecure-requests;", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload", "Permissions-Policy": "camera=(), microphone=(), geolocation=()"}, "dest": "/index.html"}]}