// Drop all ENUM types in Postgres before running tests
const { sequelize } = require('../models');
const { Sequelize } = require('sequelize');

async function dropAllEnums(sequelize) {
  if (sequelize.getDialect() !== 'postgres') return;
  const queryInterface = sequelize.getQueryInterface();
  const enums = await sequelize.query(
    `SELECT t.typname AS enumtype
     FROM pg_type t
     JOIN pg_enum e ON t.oid = e.enumtypid
     JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace
     GROUP BY enumtype;`,
    { type: Sequelize.QueryTypes.SELECT }
  );
  for (const e of enums) {
    try {
      await queryInterface.sequelize.query(`DROP TYPE IF EXISTS "${e.enumtype}" CASCADE;`);
    } catch (err) {
      // ignore errors
    }
  }
}

module.exports = dropAllEnums;
