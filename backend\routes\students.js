const express = require('express');
const { Student, User, Class, Attendance, Performance } = require('../models');
const { body, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');
const { authorize } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

// Get students (role-based access)
router.get('/', authMiddleware, async (req, res) => {
  try {
    const { page = 1, limit = 20, classId, search } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = {};
    let includeClause = [
      {
        model: User,
        as: 'parent',
        attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
      },
      {
        model: Class,
        as: 'class',
        attributes: ['id', 'className', 'classCode', 'grade']
      }
    ];

    // Role-based filtering
    if (req.user.role === 'parent') {
      whereClause.parentId = req.user.userId;
    } else if (req.user.role === 'teacher') {
      // Get students from teacher's classes
      const teacherClasses = await Class.findAll({
        where: { teacherId: req.user.userId },
        attributes: ['id']
      });
      const classIds = teacherClasses.map(cls => cls.id);
      whereClause.classId = { [Op.in]: classIds };
    }

    // Additional filters
    if (classId) {
      whereClause.classId = classId;
    }

    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { studentId: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const students = await Student.findAndCountAll({
      where: whereClause,
      include: includeClause,
      order: [['lastName', 'ASC'], ['firstName', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      students: students.rows,
      totalCount: students.count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(students.count / limit)
    });
  } catch (error) {
    console.error('Get students error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get specific student
router.get('/:studentId', authMiddleware, async (req, res) => {
  try {
    const { studentId } = req.params;

    const student = await Student.findByPk(studentId, {
      include: [
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
        },
        {
          model: Class,
          as: 'class',
          include: [
            {
              model: User,
              as: 'teacher',
              attributes: ['id', 'firstName', 'lastName', 'email']
            }
          ]
        }
      ]
    });

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Check access permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      (req.user.role === 'parent' && student.parentId === req.user.userId) ||
      (req.user.role === 'teacher' && student.class.teacherId === req.user.userId);

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(student);
  } catch (error) {
    console.error('Get student error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create student (admin/teacher only)
router.post('/', authMiddleware, authorize(['admin', 'teacher']), [
  body('studentId').trim().isLength({ min: 3, max: 20 }),
  body('firstName').trim().isLength({ min: 1, max: 50 }),
  body('lastName').trim().isLength({ min: 1, max: 50 }),
  body('grade').isIn(['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']),
  body('dateOfBirth').isISO8601(),
  body('parentId').isUUID(),
  body('classId').isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      studentId,
      firstName,
      lastName,
      grade,
      dateOfBirth,
      parentId,
      classId,
      specialNeeds,
      medicalInfo,
      emergencyContact
    } = req.body;

    // Check if student ID already exists
    const existingStudent = await Student.findOne({ where: { studentId } });
    if (existingStudent) {
      return res.status(400).json({ message: 'Student ID already exists' });
    }

    // Verify parent and class exist
    const parent = await User.findByPk(parentId);
    const classRecord = await Class.findByPk(classId);

    if (!parent || parent.role !== 'parent') {
      return res.status(400).json({ message: 'Invalid parent ID' });
    }

    if (!classRecord) {
      return res.status(400).json({ message: 'Invalid class ID' });
    }

    const student = await Student.create({
      studentId,
      firstName,
      lastName,
      grade,
      dateOfBirth,
      parentId,
      classId,
      specialNeeds,
      medicalInfo: medicalInfo || {},
      emergencyContact: emergencyContact || {}
    });

    // Load full student data
    const fullStudent = await Student.findByPk(student.id, {
      include: [
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Class,
          as: 'class',
          attributes: ['id', 'className', 'classCode', 'grade']
        }
      ]
    });

    res.status(201).json(fullStudent);
  } catch (error) {
    console.error('Create student error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update student
router.patch('/:studentId', authMiddleware, async (req, res) => {
  try {
    const { studentId } = req.params;
    const updates = req.body;

    const student = await Student.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Check permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      (req.user.role === 'parent' && student.parentId === req.user.userId) ||
      (req.user.role === 'teacher');

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Restrict what parents can update
    if (req.user.role === 'parent') {
      const allowedFields = ['emergencyContact', 'medicalInfo'];
      const filteredUpdates = {};
      allowedFields.forEach(field => {
        if (updates[field] !== undefined) {
          filteredUpdates[field] = updates[field];
        }
      });
      Object.assign(updates, filteredUpdates);
    }

    await student.update(updates);

    const updatedStudent = await Student.findByPk(studentId, {
      include: [
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Class,
          as: 'class',
          attributes: ['id', 'className', 'classCode', 'grade']
        }
      ]
    });

    res.json(updatedStudent);
  } catch (error) {
    console.error('Update student error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get student attendance
router.get('/:studentId/attendance', authMiddleware, async (req, res) => {
  try {
    const { studentId } = req.params;
    const { startDate, endDate, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    const student = await Student.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Check access permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      (req.user.role === 'parent' && student.parentId === req.user.userId) ||
      (req.user.role === 'teacher');

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    let whereClause = { studentId };

    if (startDate && endDate) {
      whereClause.date = {
        [Op.between]: [startDate, endDate]
      };
    }

    const attendance = await Attendance.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Class,
          as: 'class',
          attributes: ['id', 'className', 'classCode']
        },
        {
          model: User,
          as: 'recorder',
          attributes: ['id', 'firstName', 'lastName']
        }
      ],
      order: [['date', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Calculate attendance statistics
    const stats = await Attendance.findAll({
      where: { studentId },
      attributes: [
        'status',
        [require('sequelize').fn('COUNT', require('sequelize').col('status')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    const attendanceStats = {
      total: attendance.count,
      present: stats.find(s => s.status === 'present')?.count || 0,
      absent: stats.find(s => s.status === 'absent')?.count || 0,
      late: stats.find(s => s.status === 'late')?.count || 0,
      excused: stats.find(s => s.status === 'excused')?.count || 0
    };

    attendanceStats.rate = attendance.count > 0 
      ? ((attendanceStats.present + attendanceStats.excused) / attendance.count * 100).toFixed(2)
      : 0;

    res.json({
      attendance: attendance.rows,
      stats: attendanceStats,
      totalCount: attendance.count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(attendance.count / limit)
    });
  } catch (error) {
    console.error('Get student attendance error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get student performance
router.get('/:studentId/performance', authMiddleware, async (req, res) => {
  try {
    const { studentId } = req.params;
    const { subject, gradingPeriod, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    const student = await Student.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Check access permissions
    const hasAccess = 
      req.user.role === 'admin' ||
      (req.user.role === 'parent' && student.parentId === req.user.userId) ||
      (req.user.role === 'teacher');

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    let whereClause = { studentId };

    if (subject) {
      whereClause.subject = subject;
    }

    if (gradingPeriod) {
      whereClause.gradingPeriod = gradingPeriod;
    }

    const performance = await Performance.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Class,
          as: 'class',
          attributes: ['id', 'className', 'classCode']
        },
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'firstName', 'lastName']
        }
      ],
      order: [['dueDate', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Calculate performance statistics
    const stats = await Performance.findAll({
      where: { studentId },
      attributes: [
        [require('sequelize').fn('AVG', require('sequelize').col('percentage')), 'averageGrade'],
        [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'totalAssignments'],
        [require('sequelize').fn('COUNT', require('sequelize').literal("CASE WHEN \"isLate\" = true THEN 1 END")), 'lateSubmissions']
      ],
      raw: true
    });

    const performanceStats = {
      averageGrade: parseFloat(stats[0].averageGrade || 0).toFixed(2),
      totalAssignments: parseInt(stats[0].totalAssignments || 0),
      lateSubmissions: parseInt(stats[0].lateSubmissions || 0),
      onTimeRate: stats[0].totalAssignments > 0 
        ? (((stats[0].totalAssignments - stats[0].lateSubmissions) / stats[0].totalAssignments) * 100).toFixed(2)
        : 0
    };

    res.json({
      performance: performance.rows,
      stats: performanceStats,
      totalCount: performance.count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(performance.count / limit)
    });
  } catch (error) {
    console.error('Get student performance error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Record attendance (teacher/admin only)
router.post('/:studentId/attendance', authMiddleware, authorize(['teacher', 'admin']), [
  body('date').isISO8601(),
  body('status').isIn(['present', 'absent', 'late', 'excused', 'partial']),
  body('classId').isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { studentId } = req.params;
    const { date, status, classId, arrivalTime, reason, notes } = req.body;

    // Check if attendance already recorded for this date
    const existingAttendance = await Attendance.findOne({
      where: { studentId, classId, date }
    });

    if (existingAttendance) {
      return res.status(400).json({ message: 'Attendance already recorded for this date' });
    }

    const attendance = await Attendance.create({
      studentId,
      classId,
      date,
      status,
      arrivalTime,
      reason,
      notes,
      recordedBy: req.user.userId,
      minutesLate: status === 'late' && arrivalTime ? calculateLateness(arrivalTime) : 0
    });

    // Update student's attendance rate
    await updateStudentAttendanceRate(studentId);

    res.status(201).json(attendance);
  } catch (error) {
    console.error('Record attendance error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function to calculate lateness
function calculateLateness(arrivalTime) {
  // Assuming school starts at 8:00 AM
  const schoolStartTime = new Date(`1970-01-01T08:00:00`);
  const arrival = new Date(`1970-01-01T${arrivalTime}`);
  
  if (arrival > schoolStartTime) {
    return Math.floor((arrival - schoolStartTime) / (1000 * 60)); // minutes
  }
  
  return 0;
}

// Helper function to update student attendance rate
async function updateStudentAttendanceRate(studentId) {
  const attendanceRecords = await Attendance.findAll({
    where: { studentId },
    attributes: ['status']
  });

  if (attendanceRecords.length > 0) {
    const presentCount = attendanceRecords.filter(record => 
      ['present', 'excused'].includes(record.status)
    ).length;
    
    const attendanceRate = (presentCount / attendanceRecords.length) * 100;
    
    await Student.update(
      { attendanceRate: attendanceRate.toFixed(2) },
      { where: { id: studentId } }
    );
  }
}

module.exports = router;