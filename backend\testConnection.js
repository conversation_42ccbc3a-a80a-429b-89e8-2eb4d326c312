const { Sequelize } = require('sequelize');
require('dotenv').config({ path: '.env.test' });

console.log('Loaded ENV:', {
  DB_USER: process.env.DB_USER,
  DB_PASS: process.env.DB_PASS,
  DB_NAME: process.env.DB_NAME,
  DB_HOST: process.env.DB_HOST,
  DB_PORT: process.env.DB_PORT
});

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASS,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false,
  }
);

sequelize.authenticate()
  .then(() => {
    console.log('✅ Sequelize connected successfully.');
    process.exit(0);
  })
  .catch(err => {
    console.error('❌ Sequelize connection failed:', err.message);
    process.exit(1);
  });
