import React from 'react';
import { motion } from 'framer-motion';
import { CalendarIcon, CheckCircleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/outline';

const AttendanceWidget = ({ data = null }) => {
  // Mock data if none provided
  const mockData = {
    thisWeek: [
      { date: '2024-01-15', status: 'present' },
      { date: '2024-01-16', status: 'present' },
      { date: '2024-01-17', status: 'late' },
      { date: '2024-01-18', status: 'present' },
      { date: '2024-01-19', status: 'present' }
    ],
    stats: {
      present: 85,
      absent: 5,
      late: 10,
      rate: 90
    }
  };

  const attendanceData = data || mockData;

  const getStatusIcon = (status) => {
    switch (status) {
      case 'present':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'absent':
        return <XCircleIcon className="h-4 w-4 text-red-500" />;
      case 'late':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      default:
        return <div className="h-4 w-4 bg-gray-300 rounded-full" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 border-green-300 dark:bg-green-900/20 dark:border-green-700';
      case 'absent':
        return 'bg-red-100 border-red-300 dark:bg-red-900/20 dark:border-red-700';
      case 'late':
        return 'bg-yellow-100 border-yellow-300 dark:bg-yellow-900/20 dark:border-yellow-700';
      default:
        return 'bg-gray-100 border-gray-300 dark:bg-gray-700 dark:border-gray-600';
    }
  };

  const getDayName = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { weekday: 'short' });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Attendance</h3>
        <CalendarIcon className="h-5 w-5 text-gray-400" />
      </div>

      {/* This Week */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">This Week</h4>
        <div className="flex justify-between space-x-2">
          {attendanceData.thisWeek.map((day, index) => (
            <motion.div
              key={day.date}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`flex-1 p-3 rounded-lg border-2 text-center ${getStatusColor(day.status)}`}
            >
              <div className="flex justify-center mb-1">
                {getStatusIcon(day.status)}
              </div>
              <p className="text-xs font-medium text-gray-700 dark:text-gray-300">
                {getDayName(day.date)}
              </p>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <p className="text-lg font-bold text-green-600">{attendanceData.stats.present}</p>
          <p className="text-xs text-gray-600 dark:text-gray-400">Present</p>
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-center"
        >
          <p className="text-lg font-bold text-yellow-600">{attendanceData.stats.late}</p>
          <p className="text-xs text-gray-600 dark:text-gray-400">Late</p>
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-center"
        >
          <p className="text-lg font-bold text-red-600">{attendanceData.stats.absent}</p>
          <p className="text-xs text-gray-600 dark:text-gray-400">Absent</p>
        </motion.div>
      </div>

      {/* Attendance Rate */}
      <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
          <span>Attendance Rate</span>
          <span className="font-semibold">{attendanceData.stats.rate}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${attendanceData.stats.rate}%` }}
            transition={{ duration: 1, delay: 0.5 }}
            className={`h-2 rounded-full ${
              attendanceData.stats.rate >= 95 ? 'bg-green-500' :
              attendanceData.stats.rate >= 85 ? 'bg-blue-500' :
              attendanceData.stats.rate >= 75 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
          ></motion.div>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {attendanceData.stats.rate >= 95 ? 'Excellent attendance!' :
           attendanceData.stats.rate >= 85 ? 'Good attendance' :
           attendanceData.stats.rate >= 75 ? 'Needs improvement' : 'Poor attendance'}
        </p>
      </div>
    </div>
  );
};

export default AttendanceWidget;