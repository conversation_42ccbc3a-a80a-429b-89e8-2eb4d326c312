import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ChatBubbleLeftRightIcon, UserIcon } from '@heroicons/react/24/outline';
import { format, parseISO } from 'date-fns';

const RecentMessages = ({ messages = [] }) => {
  const formatTime = (dateString) => {
    try {
      return format(parseISO(dateString), 'HH:mm');
    } catch {
      return '';
    }
  };

  if (messages.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Recent Messages</h3>
          <Link to="/messages" className="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
            View all
          </Link>
        </div>
        <div className="text-center py-8">
          <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">No recent messages</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Recent Messages</h3>
        <Link to="/messages" className="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
          View all
        </Link>
      </div>
      
      <div className="space-y-3">
        {messages.slice(0, 5).map((message, index) => (
          <motion.div
            key={message.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="flex items-start space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer"
          >
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
              {message.sender?.profilePicture ? (
                <img
                  src={message.sender.profilePicture}
                  alt=""
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <UserIcon className="h-4 w-4 text-gray-500" />
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {message.sender?.firstName} {message.sender?.lastName}
                </p>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formatTime(message.createdAt)}
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
                {message.content}
              </p>
              {!message.isRead && (
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-1"></div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default RecentMessages;