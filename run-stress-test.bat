@echo off
echo ========================================
echo 🧪 FamEduConnect - STRESS TEST SUITE
echo ========================================
echo.

echo [1/4] Checking if services are running...
curl -s http://localhost:3002/api/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Backend is not running. Please start the application first.
    echo Run: start-final.bat
    pause
    exit /b 1
)

curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Frontend is not running. Please start the application first.
    echo Run: start-final.bat
    pause
    exit /b 1
)

echo ✅ All services are running!
echo.

echo [2/4] Installing stress test dependencies...
npm install axios
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies.
    pause
    exit /b 1
)

echo [3/4] Choose stress test type:
echo.
echo 1. Quick Test (1 minute - 10 users, 20 req/sec)
echo 2. Full Test (5 minutes - 50 users, 100 req/sec)
echo 3. Extreme Test (10 minutes - 100 users, 200 req/sec)
echo.
set /p choice="Enter your choice (1-3): "

echo.
echo [4/4] Running stress test...

if "%choice%"=="1" (
    echo 🚀 Starting Quick Stress Test...
    node scripts/quick-stress-test.js
) else if "%choice%"=="2" (
    echo 🚀 Starting Full Stress Test...
    node scripts/stress-test.js
) else if "%choice%"=="3" (
    echo 🚀 Starting Extreme Stress Test...
    echo ⚠️  This will put heavy load on your system!
    echo.
    set /p confirm="Are you sure? (y/n): "
    if /i "%confirm%"=="y" (
        node scripts/stress-test.js
    ) else (
        echo Test cancelled.
        pause
        exit /b 0
    )
) else (
    echo ❌ Invalid choice. Please run again and select 1-3.
    pause
    exit /b 1
)

echo.
echo 📊 Stress test completed!
echo 📄 Check stress-test-report.json for detailed results.
echo.
pause 