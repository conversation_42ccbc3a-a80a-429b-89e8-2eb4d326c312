# FamEduConnect Complete Deployment Script (PowerShell)
# This script deploys all components of the FamEduConnect platform

# Configuration
param (
    [string]$Environment = "production",
    [string]$BackendTarget = "heroku" # Options: heroku, digitalocean, aws
)

$ProjectDir = Get-Location
$DeploymentLogDir = Join-Path -Path $ProjectDir -ChildPath "deployment-logs"
$DeploymentLogFile = Join-Path -Path $DeploymentLogDir -ChildPath "full-deployment-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"

# Create logs directory if it doesn't exist
if (-not (Test-Path -Path $DeploymentLogDir)) {
    New-Item -ItemType Directory -Path $DeploymentLogDir | Out-Null
}

# Log function
function Write-Log {
    param (
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $DeploymentLogFile -Value $logMessage
}

# Check if deployment scripts exist
$frontendScript = Join-Path -Path $ProjectDir -ChildPath "deployment-scripts\deploy-frontend.ps1"
$backendScript = Join-Path -Path $ProjectDir -ChildPath "deployment-scripts\deploy-backend.ps1"

if (-not (Test-Path -Path $frontendScript) -or -not (Test-Path -Path $backendScript)) {
    Write-Log "ERROR: Deployment scripts not found" -Level "ERROR"
    exit 1
}

# Display deployment information
Write-Log "Starting full deployment of FamEduConnect"
Write-Log "Environment: $Environment"
Write-Log "Backend Target: $BackendTarget"
Write-Log "Project Directory: $ProjectDir"

# Confirm deployment
$confirmation = Read-Host "Do you want to proceed with deployment? (y/n)"
if ($confirmation -ne "y") {
    Write-Log "Deployment cancelled by user"
    exit 0
}

# Step 1: Deploy Backend
Write-Log "Step 1: Deploying Backend..."
& $backendScript -Environment $Environment -DeploymentTarget $BackendTarget

# Check if backend deployment was successful
$backendStatusFile = Join-Path -Path $ProjectDir -ChildPath "deployment-status-backend.json"
if (-not (Test-Path -Path $backendStatusFile)) {
    Write-Log "ERROR: Backend deployment failed or status file not created" -Level "ERROR"
    exit 1
}

# Get backend URL
$backendStatus = Get-Content -Path $backendStatusFile | ConvertFrom-Json
$backendUrl = $backendStatus.backend.url
Write-Log "Backend deployed successfully to: $backendUrl"

# Step 2: Update frontend configuration with backend URL
Write-Log "Step 2: Updating frontend configuration..."
$envConfigDir = Join-Path -Path $ProjectDir -ChildPath "env-configs"
$frontendEnvFile = Join-Path -Path $envConfigDir -ChildPath "frontend.env"

if (Test-Path -Path $frontendEnvFile) {
    # Update API URL in frontend.env
    $frontendEnv = Get-Content -Path $frontendEnvFile
    $frontendEnv = $frontendEnv -replace "REACT_APP_API_URL=.*", "REACT_APP_API_URL=$backendUrl"
    $wsUrl = $backendUrl -replace "https://", "wss://"
    $frontendEnv = $frontendEnv -replace "REACT_APP_SOCKET_URL=.*", "REACT_APP_SOCKET_URL=$wsUrl"
    Set-Content -Path $frontendEnvFile -Value $frontendEnv
    Write-Log "Frontend environment variables updated with backend URL"
} else {
    Write-Log "WARNING: Frontend environment file not found at $frontendEnvFile" -Level "WARNING"
    Write-Log "Continuing without updating frontend configuration"
}

# Step 3: Deploy Frontend
Write-Log "Step 3: Deploying Frontend..."
& $frontendScript -Environment $Environment

# Check if frontend deployment was successful
$frontendStatusFile = Join-Path -Path $ProjectDir -ChildPath "deployment-status.json"
if (-not (Test-Path -Path $frontendStatusFile)) {
    Write-Log "ERROR: Frontend deployment failed or status file not created" -Level "ERROR"
    exit 1
}

# Get frontend URL
$frontendStatus = Get-Content -Path $frontendStatusFile | ConvertFrom-Json
$frontendUrl = $frontendStatus.frontend.url
Write-Log "Frontend deployed successfully to: $frontendUrl"

# Step 4: Deploy Guardian AI Monitoring
Write-Log "Step 4: Setting up Guardian AI Monitoring..."

# Check if Guardian AI files exist
$guardianAiDir = Join-Path -Path $ProjectDir -ChildPath "guardian-ai"
if (Test-Path -Path $guardianAiDir) {
    # Create Guardian AI configuration
    $guardianAiConfigDir = Join-Path -Path $guardianAiDir -ChildPath "config"
    if (-not (Test-Path -Path $guardianAiConfigDir)) {
        New-Item -ItemType Directory -Path $guardianAiConfigDir | Out-Null
    }
    
    $guardianAiKey = if ($env:GUARDIAN_AI_KEY) { $env:GUARDIAN_AI_KEY } else { "demo-key" }
    $slackWebhookUrl = if ($env:SLACK_WEBHOOK_URL) { $env:SLACK_WEBHOOK_URL } else { "https://hooks.slack.com/services/placeholder" }
    
    $monitoringConfig = @{
        apiKey = $guardianAiKey
        environment = $Environment
        endpoints = @{
            frontend = $frontendUrl
            backend = $backendUrl
        }
        alertThresholds = @{
            error = 1.0
            performance = 2000
            security = 0.5
        }
        notificationChannels = @(
            @{
                type = "email"
                address = "<EMAIL>"
            },
            @{
                type = "slack"
                webhook = $slackWebhookUrl
            }
        )
    }
    
    $monitoringConfigFile = Join-Path -Path $guardianAiConfigDir -ChildPath "monitoring.json"
    $monitoringConfig | ConvertTo-Json -Depth 5 | Set-Content -Path $monitoringConfigFile
    Write-Log "Guardian AI monitoring configuration created"
    
    # Deploy Guardian AI if deployment script exists
    $guardianAiDeployScript = Join-Path -Path $guardianAiDir -ChildPath "deploy.ps1"
    if (Test-Path -Path $guardianAiDeployScript) {
        & $guardianAiDeployScript -Environment $Environment
        Write-Log "Guardian AI monitoring deployed"
        $guardianAiStatus = "✅ Deployed"
    } else {
        Write-Log "Guardian AI deployment script not found, skipping deployment"
        $guardianAiStatus = "⚠️ Configuration Only"
    }
} else {
    Write-Log "Guardian AI directory not found, skipping monitoring setup"
    $guardianAiStatus = "❌ Not Configured"
}

# Step 5: Update DNS Configuration
Write-Log "Step 5: DNS Configuration Instructions..."

$dnsInstructions = @"

DNS CONFIGURATION INSTRUCTIONS
=============================

To complete the deployment, configure your DNS settings as follows:

1. Main Domain (fameduconnect.xyz):
   Type: A
   Value: Point to your landing page server IP

2. App Subdomain (app.fameduconnect.xyz):
   Type: CNAME
   Value: $frontendUrl

3. API Subdomain (api.fameduconnect.xyz):
   Type: CNAME
   Value: $backendUrl

4. Admin Subdomain (admin.fameduconnect.xyz):
   Type: CNAME
   Value: [Your admin dashboard URL]

For detailed DNS configuration, refer to DOMAIN_CONFIGURATION.md
"@

Add-Content -Path $DeploymentLogFile -Value $dnsInstructions
Write-Log "DNS configuration instructions added to deployment log"

# Step 6: Generate Deployment Summary
Write-Log "Step 6: Generating Deployment Summary..."

$deploymentSummaryFile = Join-Path -Path $ProjectDir -ChildPath "DEPLOYMENT_SUMMARY.md"
$deploymentId = Get-Date -Format "yyyyMMdd-HHmmss"
$currentDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

$deploymentSummary = @"
# FamEduConnect Deployment Summary

## Deployment Information

- **Environment:** $Environment
- **Deployment Date:** $currentDate
- **Deployment ID:** $deploymentId

## Deployed Components

### Backend
- **URL:** $backendUrl
- **Platform:** $BackendTarget
- **Status:** ✅ Deployed

### Frontend
- **URL:** $frontendUrl
- **Platform:** Vercel
- **Status:** ✅ Deployed

### Guardian AI Monitoring
- **Status:** $guardianAiStatus

## Next Steps

1. Configure DNS settings as specified in the deployment log
2. Verify all components are working correctly
3. Set up monitoring and alerts
4. Complete final testing

## Access Information

- **Frontend:** $frontendUrl
- **Backend API:** $backendUrl
- **Admin Dashboard:** [Configure in DNS]
- **API Documentation:** $backendUrl/docs

## Support

If you encounter any issues with this deployment, please contact:
- **Email:** <EMAIL>
- **Deployment Log:** $DeploymentLogFile

---

© 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc. – NPO: 2023-001341848  
Creative Director: Na'imah Barnes
"@

Set-Content -Path $deploymentSummaryFile -Value $deploymentSummary
Write-Log "Deployment summary generated at $deploymentSummaryFile"

# Step 7: Final Verification
Write-Log "Step 7: Running Final Verification..."

# Check frontend accessibility
try {
    $frontendResponse = Invoke-WebRequest -Uri $frontendUrl -UseBasicParsing
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Log "Frontend is accessible (HTTP 200)"
    } else {
        Write-Log "WARNING: Frontend returned HTTP status $($frontendResponse.StatusCode)" -Level "WARNING"
    }
} catch {
    Write-Log "WARNING: Could not access frontend: $_" -Level "WARNING"
}

# Check backend accessibility
try {
    $backendHealthUrl = "$backendUrl/api/health"
    $backendResponse = Invoke-WebRequest -Uri $backendHealthUrl -UseBasicParsing
    if ($backendResponse.StatusCode -eq 200) {
        Write-Log "Backend is accessible (HTTP 200)"
    } else {
        Write-Log "WARNING: Backend returned HTTP status $($backendResponse.StatusCode)" -Level "WARNING"
    }
} catch {
    Write-Log "WARNING: Could not access backend: $_" -Level "WARNING"
}

# Deployment complete
Write-Log "🎉 Deployment completed successfully!"
Write-Log "Frontend URL: $frontendUrl"
Write-Log "Backend URL: $backendUrl"
Write-Log "Deployment Summary: $deploymentSummaryFile"
Write-Log "Deployment Log: $DeploymentLogFile"

exit 0