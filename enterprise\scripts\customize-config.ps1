# FamEduConnect Enterprise Configuration Customization Script (PowerShell)
# This script helps customize the YAML configuration files for your specific environment

param(
    [switch]$Force
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "================================`n$Message`n================================" -ForegroundColor Blue
}

# Function to prompt for input with default value
function Read-InputWithDefault {
    param(
        [string]$Prompt,
        [string]$Default = ""
    )
    
    if ($Default) {
        $input = Read-Host "$Prompt [$Default]"
        if (-not $input) {
            $input = $Default
        }
    } else {
        $input = Read-Host $Prompt
    }
    
    return $input
}

# Function to generate secure password
function New-SecurePassword {
    $bytes = New-Object Byte[] 32
    (New-Object Security.Cryptography.RNGCryptoServiceProvider).GetBytes($bytes)
    return [Convert]::ToBase64String($bytes) -replace '[=+/]', '' | Select-Object -First 25
}

# Function to base64 encode
function Convert-ToBase64 {
    param([string]$String)
    $bytes = [System.Text.Encoding]::UTF8.GetBytes($String)
    return [Convert]::ToBase64String($bytes)
}

# Function to update YAML file
function Update-YamlFile {
    param(
        [string]$FilePath,
        [string]$Search,
        [string]$Replace
    )
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        $content = $content -replace [regex]::Escape($Search), $Replace
        Set-Content $FilePath $content -NoNewline
    } else {
        Write-Warning "File not found: $FilePath"
    }
}

# Function to create secrets file
function New-SecretsFile {
    param([string]$FilePath)
    
    $secretsContent = @"
# FamEduConnect Enterprise Secrets
# Generated on: $(Get-Date)
# WARNING: Keep this file secure and never commit to version control

# Database Secrets
POSTGRES_PASSWORD="$POSTGRES_PASSWORD"
POSTGRES_REPLICATION_PASSWORD="$POSTGRES_REPLICATION_PASSWORD"
POSTGRES_BACKUP_PASSWORD="$POSTGRES_BACKUP_PASSWORD"

# Base64 Encoded Secrets (for Kubernetes)
BASE64_ENCODED_POSTGRES_PASSWORD="$(Convert-ToBase64 $POSTGRES_PASSWORD)"
BASE64_ENCODED_REPLICATION_PASSWORD="$(Convert-ToBase64 $POSTGRES_REPLICATION_PASSWORD)"
BASE64_ENCODED_BACKUP_PASSWORD="$(Convert-ToBase64 $POSTGRES_BACKUP_PASSWORD)"

# JWT Secret
JWT_SECRET="$JWT_SECRET"
BASE64_ENCODED_JWT_SECRET="$(Convert-ToBase64 $JWT_SECRET)"

# Monitoring Secrets
ELASTICSEARCH_USERNAME="$ELASTICSEARCH_USERNAME"
ELASTICSEARCH_PASSWORD="$ELASTICSEARCH_PASSWORD"
BASE64_ENCODED_ELASTICSEARCH_USERNAME="$(Convert-ToBase64 $ELASTICSEARCH_USERNAME)"
BASE64_ENCODED_ELASTICSEARCH_PASSWORD="$(Convert-ToBase64 $ELASTICSEARCH_PASSWORD)"

# SSO Configuration (if using SAML/OIDC)
SAML_ENTITY_ID="$SAML_ENTITY_ID"
SAML_ACS_URL="$SAML_ACS_URL"
SAML_IDP_SSO_URL="$SAML_IDP_SSO_URL"
SAML_IDP_CERT="$SAML_IDP_CERT"

# Backup Storage
S3_BUCKET="$S3_BUCKET"
S3_ACCESS_KEY="$S3_ACCESS_KEY"
S3_SECRET_KEY="$S3_SECRET_KEY"
BASE64_ENCODED_S3_ACCESS_KEY="$(Convert-ToBase64 $S3_ACCESS_KEY)"
BASE64_ENCODED_S3_SECRET_KEY="$(Convert-ToBase64 $S3_SECRET_KEY)"
"@
    
    Set-Content $FilePath $secretsContent
}

# Main script
function Main {
    Write-Header "FamEduConnect Enterprise Configuration Customization"
    
    # Check if running from the correct directory
    if (-not (Test-Path "enterprise\ENTERPRISE_DEPLOYMENT_GUIDE.md")) {
        Write-Error "Please run this script from the FamEduConnect_Full_Codebase directory"
        exit 1
    }
    
    Write-Status "Starting configuration customization..."
    
    # Create configuration directory
    $configDir = "enterprise\config-customized"
    if (Test-Path $configDir) {
        if (-not $Force) {
            $response = Read-Host "Configuration directory already exists. Overwrite? (y/N)"
            if ($response -notmatch '^[Yy]$') {
                Write-Status "Operation cancelled."
                exit 0
            }
        }
        Remove-Item $configDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $configDir -Force | Out-Null
    
    # Step 1: Domain Configuration
    Write-Header "Step 1: Domain Configuration"
    
    $DOMAIN = Read-InputWithDefault "Enter your primary domain (e.g., yourdomain.com)" "yourdomain.com"
    $APP_SUBDOMAIN = Read-InputWithDefault "Enter your app subdomain" "app"
    $ADMIN_SUBDOMAIN = Read-InputWithDefault "Enter your admin subdomain" "admin"
    $API_SUBDOMAIN = Read-InputWithDefault "Enter your API subdomain" "api"
    
    $APP_DOMAIN = "$APP_SUBDOMAIN.$DOMAIN"
    $ADMIN_DOMAIN = "$ADMIN_SUBDOMAIN.$DOMAIN"
    $API_DOMAIN = "$API_SUBDOMAIN.$DOMAIN"
    
    Write-Status "Configured domains:"
    Write-Host "  App: $APP_DOMAIN"
    Write-Host "  Admin: $ADMIN_DOMAIN"
    Write-Host "  API: $API_DOMAIN"
    
    # Step 2: Database Configuration
    Write-Header "Step 2: Database Configuration"
    
    $DB_NAME = Read-InputWithDefault "Enter database name" "fameduconnect_prod"
    $DB_USER = Read-InputWithDefault "Enter database user" "fameduconnect"
    
    # Generate secure passwords
    $POSTGRES_PASSWORD = New-SecurePassword
    $POSTGRES_REPLICATION_PASSWORD = New-SecurePassword
    $POSTGRES_BACKUP_PASSWORD = New-SecurePassword
    
    Write-Status "Generated secure database passwords"
    
    # Step 3: Storage Configuration
    Write-Header "Step 3: Storage Configuration"
    
    $STORAGE_CLASS = Read-InputWithDefault "Enter storage class name (e.g., gp3-encrypted for AWS)" "gp3-encrypted"
    $PRIMARY_STORAGE = Read-InputWithDefault "Enter primary database storage size (e.g., 100Gi)" "100Gi"
    $REPLICA_STORAGE = Read-InputWithDefault "Enter replica database storage size (e.g., 50Gi)" "50Gi"
    
    # Step 4: Resource Configuration
    Write-Header "Step 4: Resource Configuration"
    
    $BACKEND_MIN_REPLICAS = Read-InputWithDefault "Enter backend min replicas" "3"
    $BACKEND_MAX_REPLICAS = Read-InputWithDefault "Enter backend max replicas" "50"
    $FRONTEND_MIN_REPLICAS = Read-InputWithDefault "Enter frontend min replicas" "2"
    $FRONTEND_MAX_REPLICAS = Read-InputWithDefault "Enter frontend max replicas" "20"
    $ADMIN_MIN_REPLICAS = Read-InputWithDefault "Enter admin min replicas" "2"
    $ADMIN_MAX_REPLICAS = Read-InputWithDefault "Enter admin max replicas" "10"
    
    # Step 5: Monitoring Configuration
    Write-Header "Step 5: Monitoring Configuration"
    
    $ELASTICSEARCH_USERNAME = Read-InputWithDefault "Enter Elasticsearch username" "elastic"
    $ELASTICSEARCH_PASSWORD = New-SecurePassword
    
    Write-Status "Generated Elasticsearch password"
    
    # Step 6: Backup Configuration
    Write-Header "Step 6: Backup Configuration"
    
    $S3_BUCKET = Read-InputWithDefault "Enter S3 bucket name for backups" "fameduconnect-backups"
    $S3_ACCESS_KEY = Read-InputWithDefault "Enter S3 access key" ""
    $S3_SECRET_KEY = Read-InputWithDefault "Enter S3 secret key" ""
    
    # Step 7: Security Configuration
    Write-Header "Step 7: Security Configuration"
    
    $JWT_SECRET = New-SecurePassword
    Write-Status "Generated JWT secret"
    
    # Step 8: SSO Configuration (Optional)
    Write-Header "Step 8: SSO Configuration (Optional)"
    
    $configure_sso = Read-Host "Do you want to configure SSO (SAML/OIDC)? (y/N)"
    if ($configure_sso -match '^[Yy]$') {
        $SAML_ENTITY_ID = Read-InputWithDefault "Enter SAML Entity ID" "https://$API_DOMAIN/saml/metadata"
        $SAML_ACS_URL = Read-InputWithDefault "Enter SAML ACS URL" "https://$API_DOMAIN/saml/acs"
        $SAML_IDP_SSO_URL = Read-InputWithDefault "Enter SAML IdP SSO URL" ""
        $SAML_IDP_CERT = Read-InputWithDefault "Enter SAML IdP Certificate (base64)" ""
    } else {
        $SAML_ENTITY_ID = ""
        $SAML_ACS_URL = ""
        $SAML_IDP_SSO_URL = ""
        $SAML_IDP_CERT = ""
    }
    
    # Step 9: Generate Configuration Files
    Write-Header "Step 9: Generating Configuration Files"
    
    # Create secrets file
    New-SecretsFile "$configDir\secrets.env"
    Write-Status "Created secrets file: $configDir\secrets.env"
    
    # Copy and customize YAML files
    Write-Status "Customizing YAML configuration files..."
    
    # Copy files to customized directory
    Copy-Item -Path "enterprise\k8s" -Destination "$configDir\" -Recurse
    Copy-Item -Path "enterprise\database" -Destination "$configDir\" -Recurse
    Copy-Item -Path "enterprise\istio" -Destination "$configDir\" -Recurse
    Copy-Item -Path "enterprise\monitoring" -Destination "$configDir\" -Recurse
    Copy-Item -Path "enterprise\auth" -Destination "$configDir\" -Recurse
    Copy-Item -Path "enterprise\dr" -Destination "$configDir\" -Recurse
    
    # Update domain names in Istio config
    Update-YamlFile "$configDir\istio\istio-config.yaml" "app.fameduconnect.xyz" $APP_DOMAIN
    Update-YamlFile "$configDir\istio\istio-config.yaml" "admin.fameduconnect.xyz" $ADMIN_DOMAIN
    Update-YamlFile "$configDir\istio\istio-config.yaml" "api.fameduconnect.xyz" $API_DOMAIN
    
    # Update database configuration
    Update-YamlFile "$configDir\database\postgres-ha.yaml" "fameduconnect_prod" $DB_NAME
    Update-YamlFile "$configDir\database\postgres-ha.yaml" "fameduconnect" $DB_USER
    Update-YamlFile "$configDir\database\postgres-ha.yaml" "gp3-encrypted" $STORAGE_CLASS
    Update-YamlFile "$configDir\database\postgres-ha.yaml" "100Gi" $PRIMARY_STORAGE
    Update-YamlFile "$configDir\database\postgres-ha.yaml" "50Gi" $REPLICA_STORAGE
    
    # Update HPA configuration
    Update-YamlFile "$configDir\k8s\hpa.yaml" "minReplicas: 3" "minReplicas: $BACKEND_MIN_REPLICAS"
    Update-YamlFile "$configDir\k8s\hpa.yaml" "maxReplicas: 50" "maxReplicas: $BACKEND_MAX_REPLICAS"
    Update-YamlFile "$configDir\k8s\hpa.yaml" "minReplicas: 2" "minReplicas: $FRONTEND_MIN_REPLICAS"
    Update-YamlFile "$configDir\k8s\hpa.yaml" "maxReplicas: 20" "maxReplicas: $FRONTEND_MAX_REPLICAS"
    Update-YamlFile "$configDir\k8s\hpa.yaml" "minReplicas: 2" "minReplicas: $ADMIN_MIN_REPLICAS"
    Update-YamlFile "$configDir\k8s\hpa.yaml" "maxReplicas: 10" "maxReplicas: $ADMIN_MAX_REPLICAS"
    
    # Create deployment script
    $deployScript = @"
# FamEduConnect Enterprise Deployment Script (PowerShell)
# Generated on: $(Get-Date)

# Load secrets
. .\secrets.env

# Create namespace
kubectl create namespace fameduconnect --dry-run=client -o yaml | kubectl apply -f -

# Apply database configuration
kubectl apply -f database\

# Apply Istio configuration
kubectl apply -f istio\

# Apply monitoring configuration
kubectl apply -f monitoring\

# Apply authentication configuration
kubectl apply -f auth\

# Apply disaster recovery configuration
kubectl apply -f dr\

# Apply HPA configuration
kubectl apply -f k8s\

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Access your application at:" -ForegroundColor Cyan
Write-Host "  App: https://$APP_DOMAIN" -ForegroundColor White
Write-Host "  Admin: https://$ADMIN_DOMAIN" -ForegroundColor White
Write-Host "  API: https://$API_DOMAIN" -ForegroundColor White
"@
    
    Set-Content "$configDir\deploy.ps1" $deployScript
    
    # Create environment variables file
    $envContent = @"
# FamEduConnect Environment Variables
# Generated on: $(Get-Date)

# Domain Configuration
DOMAIN=$DOMAIN
APP_DOMAIN=$APP_DOMAIN
ADMIN_DOMAIN=$ADMIN_DOMAIN
API_DOMAIN=$API_DOMAIN

# Database Configuration
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DATABASE_URL=postgresql://$DB_USER`:$POSTGRES_PASSWORD@postgres-primary:5432/$DB_NAME

# Storage Configuration
STORAGE_CLASS=$STORAGE_CLASS
PRIMARY_STORAGE=$PRIMARY_STORAGE
REPLICA_STORAGE=$REPLICA_STORAGE

# Resource Configuration
BACKEND_MIN_REPLICAS=$BACKEND_MIN_REPLICAS
BACKEND_MAX_REPLICAS=$BACKEND_MAX_REPLICAS
FRONTEND_MIN_REPLICAS=$FRONTEND_MIN_REPLICAS
FRONTEND_MAX_REPLICAS=$FRONTEND_MAX_REPLICAS
ADMIN_MIN_REPLICAS=$ADMIN_MIN_REPLICAS
ADMIN_MAX_REPLICAS=$ADMIN_MAX_REPLICAS

# Monitoring Configuration
ELASTICSEARCH_USERNAME=$ELASTICSEARCH_USERNAME
ELASTICSEARCH_PASSWORD=$ELASTICSEARCH_PASSWORD

# Backup Configuration
S3_BUCKET=$S3_BUCKET
S3_ACCESS_KEY=$S3_ACCESS_KEY
S3_SECRET_KEY=$S3_SECRET_KEY

# Security Configuration
JWT_SECRET=$JWT_SECRET

# SSO Configuration
SAML_ENTITY_ID=$SAML_ENTITY_ID
SAML_ACS_URL=$SAML_ACS_URL
SAML_IDP_SSO_URL=$SAML_IDP_SSO_URL
SAML_IDP_CERT=$SAML_IDP_CERT
"@
    
    Set-Content "$configDir\environment.env" $envContent
    
    # Step 10: Summary
    Write-Header "Configuration Customization Complete!"
    
    Write-Status "Generated files:"
    Write-Host "  - $configDir\secrets.env (SECURE - contains passwords)" -ForegroundColor Yellow
    Write-Host "  - $configDir\environment.env (environment variables)" -ForegroundColor White
    Write-Host "  - $configDir\deploy.ps1 (deployment script)" -ForegroundColor White
    Write-Host "  - $configDir\ (customized YAML files)" -ForegroundColor White
    
    Write-Warning "IMPORTANT:"
    Write-Host "  1. Review all generated files before deployment" -ForegroundColor White
    Write-Host "  2. Keep secrets.env secure and never commit to version control" -ForegroundColor White
    Write-Host "  3. Update DNS records for your domains" -ForegroundColor White
    Write-Host "  4. Obtain SSL certificates for your domains" -ForegroundColor White
    Write-Host "  5. Configure your cloud provider storage classes" -ForegroundColor White
    
    Write-Status "Next steps:"
    Write-Host "  1. Review the CONFIGURATION_REVIEW_GUIDE.md" -ForegroundColor White
    Write-Host "  2. Set up your Kubernetes cluster" -ForegroundColor White
    Write-Host "  3. Install Istio" -ForegroundColor White
    Write-Host "  4. Run: cd $configDir && .\deploy.ps1" -ForegroundColor White
    
    Write-Status "Configuration customization completed successfully!"
}

# Run main function
Main 