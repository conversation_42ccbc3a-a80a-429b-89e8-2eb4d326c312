{"name": "fameduconnect-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~49.0.0", "react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@reduxjs/toolkit": "^1.9.5", "react-redux": "^8.1.2", "axios": "^1.5.0", "react-native-vector-icons": "^10.0.0", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "expo-av": "~13.4.1", "expo-camera": "~13.4.2", "expo-notifications": "~0.20.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}