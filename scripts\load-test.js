import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const successRate = new Rate('success');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200 users
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 },   // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
    http_req_failed: ['rate<0.1'],     // Error rate must be below 10%
    errors: ['rate<0.1'],
    success: ['rate>0.9'],
  },
};

// Test data
const BASE_URL = __ENV.BASE_URL || 'https://api.fameduconnect.com';
const TEST_USERS = [
  { email: '<EMAIL>', password: 'testpass123', role: 'parent' },
  { email: '<EMAIL>', password: 'testpass123', role: 'teacher' },
  { email: '<EMAIL>', password: 'testpass123', role: 'student' },
  { email: '<EMAIL>', password: 'testpass123', role: 'admin' },
];

// Helper functions
function getRandomUser() {
  return TEST_USERS[Math.floor(Math.random() * TEST_USERS.length)];
}

function getAuthToken(user) {
  const loginRes = http.post(`${BASE_URL}/api/auth/login`, JSON.stringify({
    email: user.email,
    password: user.password,
  }), {
    headers: { 'Content-Type': 'application/json' },
  });

  if (loginRes.status === 200) {
    const body = JSON.parse(loginRes.body);
    return body.token;
  }
  return null;
}

// Main test scenarios
export default function () {
  const user = getRandomUser();
  const token = getAuthToken(user);

  if (!token) {
    errorRate.add(1);
    return;
  }

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };

  // Test 1: User Profile
  const profileRes = http.get(`${BASE_URL}/api/users/profile`, { headers });
  const profileCheck = check(profileRes, {
    'profile status is 200': (r) => r.status === 200,
    'profile response time < 500ms': (r) => r.timings.duration < 500,
  });
  
  if (profileCheck) {
    successRate.add(1);
  } else {
    errorRate.add(1);
  }

  sleep(1);

  // Test 2: Messages List
  const messagesRes = http.get(`${BASE_URL}/api/messages`, { headers });
  const messagesCheck = check(messagesRes, {
    'messages status is 200': (r) => r.status === 200,
    'messages response time < 1000ms': (r) => r.timings.duration < 1000,
  });

  if (messagesCheck) {
    successRate.add(1);
  } else {
    errorRate.add(1);
  }

  sleep(1);

  // Test 3: Send Message
  const messageData = {
    recipientId: Math.floor(Math.random() * 100) + 1,
    content: `Load test message from ${user.email} at ${new Date().toISOString()}`,
    type: 'text',
  };

  const sendMessageRes = http.post(`${BASE_URL}/api/messages`, JSON.stringify(messageData), { headers });
  const sendMessageCheck = check(sendMessageRes, {
    'send message status is 201': (r) => r.status === 201,
    'send message response time < 1000ms': (r) => r.timings.duration < 1000,
  });

  if (sendMessageCheck) {
    successRate.add(1);
  } else {
    errorRate.add(1);
  }

  sleep(1);

  // Test 4: Get Classes (for teachers and students)
  if (user.role === 'teacher' || user.role === 'student') {
    const classesRes = http.get(`${BASE_URL}/api/classes`, { headers });
    const classesCheck = check(classesRes, {
      'classes status is 200': (r) => r.status === 200,
      'classes response time < 800ms': (r) => r.timings.duration < 800,
    });

    if (classesCheck) {
      successRate.add(1);
    } else {
      errorRate.add(1);
    }

    sleep(1);
  }

  // Test 5: Get Assignments (for students)
  if (user.role === 'student') {
    const assignmentsRes = http.get(`${BASE_URL}/api/assignments`, { headers });
    const assignmentsCheck = check(assignmentsRes, {
      'assignments status is 200': (r) => r.status === 200,
      'assignments response time < 800ms': (r) => r.timings.duration < 800,
    });

    if (assignmentsCheck) {
      successRate.add(1);
    } else {
      errorRate.add(1);
    }

    sleep(1);
  }

  // Test 6: File Upload (simulated)
  const fileData = {
    filename: 'test-file.txt',
    size: 1024,
    type: 'text/plain',
  };

  const uploadRes = http.post(`${BASE_URL}/api/files/upload`, JSON.stringify(fileData), { headers });
  const uploadCheck = check(uploadRes, {
    'upload status is 200': (r) => r.status === 200,
    'upload response time < 2000ms': (r) => r.timings.duration < 2000,
  });

  if (uploadCheck) {
    successRate.add(1);
  } else {
    errorRate.add(1);
  }

  sleep(2);

  // Test 7: Search Users
  const searchRes = http.get(`${BASE_URL}/api/users/search?q=test`, { headers });
  const searchCheck = check(searchRes, {
    'search status is 200': (r) => r.status === 200,
    'search response time < 1000ms': (r) => r.timings.duration < 1000,
  });

  if (searchCheck) {
    successRate.add(1);
  } else {
    errorRate.add(1);
  }

  sleep(1);

  // Test 8: Health Check
  const healthRes = http.get(`${BASE_URL}/health`);
  const healthCheck = check(healthRes, {
    'health status is 200': (r) => r.status === 200,
    'health response time < 100ms': (r) => r.timings.duration < 100,
  });

  if (healthCheck) {
    successRate.add(1);
  } else {
    errorRate.add(1);
  }

  sleep(1);
}

// Setup function (runs once at the beginning)
export function setup() {
  console.log('Starting load test for FamEduConnect API');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Test users: ${TEST_USERS.length}`);
  
  // Verify API is accessible
  const healthRes = http.get(`${BASE_URL}/health`);
  if (healthRes.status !== 200) {
    throw new Error(`API health check failed: ${healthRes.status}`);
  }
  
  console.log('API health check passed');
}

// Teardown function (runs once at the end)
export function teardown(data) {
  console.log('Load test completed');
  console.log('Final metrics:');
  console.log(`- Total requests: ${data.metrics.http_reqs?.values?.count || 0}`);
  console.log(`- Error rate: ${data.metrics.errors?.values?.rate || 0}`);
  console.log(`- Success rate: ${data.metrics.success?.values?.rate || 0}`);
  console.log(`- Average response time: ${data.metrics.http_req_duration?.values?.avg || 0}ms`);
}

// Handle summary
export function handleSummary(data) {
  return {
    'load-test-results.json': JSON.stringify(data, null, 2),
    stdout: `
Load Test Results for FamEduConnect
==================================
Total Requests: ${data.metrics.http_reqs?.values?.count || 0}
Error Rate: ${(data.metrics.errors?.values?.rate || 0) * 100}%
Success Rate: ${(data.metrics.success?.values?.rate || 0) * 100}%
Average Response Time: ${Math.round(data.metrics.http_req_duration?.values?.avg || 0)}ms
95th Percentile: ${Math.round(data.metrics.http_req_duration?.values?.['p(95)'] || 0)}ms
Max Response Time: ${Math.round(data.metrics.http_req_duration?.values?.max || 0)}ms
Min Response Time: ${Math.round(data.metrics.http_req_duration?.values?.min || 0)}ms
    `,
  };
} 