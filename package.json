{"name": "fameduconnect", "version": "1.0.0", "description": "Family Education Connection Platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:admin\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "dev:admin": "cd admin && npm start", "dev:mobile": "cd mobile && npm start", "dev:backend-only": "cd backend && npm run dev", "dev:frontend-only": "cd frontend && npm start", "build": "npm run build:frontend && npm run build:admin", "build:frontend": "cd frontend && npm run build", "build:admin": "cd admin && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install && cd ../admin && npm install && cd ../mobile && npm install", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules admin/node_modules mobile/node_modules", "docker:dev": "docker-compose up -d", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "deploy": "./scripts/deploy.sh", "start": "npm run dev"}, "keywords": ["education", "family", "communication", "react", "nodejs", "webrtc"], "author": "FamEduConnect Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"axios": "^1.11.0", "node-cache": "^5.1.2", "redis": "^5.7.0"}}