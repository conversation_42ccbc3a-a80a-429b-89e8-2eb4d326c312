import axios from 'axios';
import { toast } from 'react-hot-toast';

// Use proxy-friendly base URL - React will proxy to backend
const API_BASE_URL = '/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Enable credentials for CORS
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add language header
    const language = localStorage.getItem('preferredLanguage') || 'en';
    config.headers['Accept-Language'] = language;
    
    // Ensure proper Content-Type for JSON requests
    if (config.data && typeof config.data === 'object') {
      config.headers['Content-Type'] = 'application/json';
    }
    
    console.log('🚀 API Request:', {
      method: config.method,
      url: config.url,
      headers: config.headers,
      data: config.data
    });
    
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log('✅ API Response:', {
      status: response.status,
      url: response.config.url,
      data: response.data
    });
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', {
      status: error.response?.status,
      url: error.config?.url,
      message: error.message,
      data: error.response?.data
    });
    
    // Handle common errors
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          localStorage.removeItem('token');
          delete api.defaults.headers.common['Authorization'];
          
          if (window.location.pathname !== '/login') {
            toast.error('Session expired. Please login again.');
            window.location.href = '/login';
          }
          break;
          
        case 403:
          toast.error('Access denied. You do not have permission to perform this action.');
          break;
          
        case 404:
          toast.error('Resource not found.');
          break;
          
        case 422:
          // Validation errors
          if (data.errors && Array.isArray(data.errors)) {
            data.errors.forEach(err => {
              toast.error(err.msg || err.message);
            });
          } else {
            toast.error(data.message || 'Validation error');
          }
          break;
          
        case 423:
          toast.error('Account temporarily locked. Please try again later.');
          break;
          
        case 429:
          toast.error('Too many requests. Please slow down.');
          break;
          
        case 500:
          toast.error('Server error. Please try again later.');
          break;
          
        default:
          toast.error(data.message || 'An unexpected error occurred');
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.');
    } else {
      // Other error
      toast.error('An unexpected error occurred');
    }
    
    return Promise.reject(error);
  }
);

// API methods
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  biometricLogin: (data) => api.post('/auth/biometric-login', data),
  logout: () => api.post('/auth/logout'),
  refreshToken: (token) => api.post('/auth/refresh', { token }),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
};

export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.patch('/users/profile', data),
  uploadProfilePicture: (formData) => api.post('/users/profile/picture', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  changePassword: (data) => api.patch('/users/password', data),
  enableBiometric: (data) => api.patch('/users/biometric', data),
  getChildren: () => api.get('/users/children'),
  getClasses: () => api.get('/users/classes'),
  searchUsers: (query, role) => api.get('/users/search', { params: { query, role } }),
  deleteAccount: (data) => api.delete('/users/account', { data }),
};

export const messageAPI = {
  getMessages: (params) => api.get('/messages', { params }),
  sendMessage: (data) => api.post('/messages', data),
  sendMessageWithFiles: (formData) => api.post('/messages', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  markAsRead: (messageId) => api.patch(`/messages/${messageId}/read`),
  getTranslations: (messageId, targetLanguage) => 
    api.get(`/messages/${messageId}/translations`, { params: { targetLanguage } }),
  deleteMessage: (messageId) => api.delete(`/messages/${messageId}`),
};

export const videoCallAPI = {
  createCall: (data) => api.post('/webrtc/calls', data),
  getCalls: (params) => api.get('/webrtc/calls', { params }),
  getCall: (callId) => api.get(`/webrtc/calls/${callId}`),
  startCall: (callId) => api.post(`/webrtc/calls/${callId}/start`),
  endCall: (callId, data) => api.post(`/webrtc/calls/${callId}/end`, data),
  joinCall: (callId) => api.post(`/webrtc/calls/${callId}/join`),
  leaveCall: (callId) => api.post(`/webrtc/calls/${callId}/leave`),
  updateCall: (callId, data) => api.patch(`/webrtc/calls/${callId}`, data),
  getRecording: (callId) => api.get(`/webrtc/calls/${callId}/recording`),
};

export const studentAPI = {
  getStudents: (params) => api.get('/students', { params }),
  getStudent: (studentId) => api.get(`/students/${studentId}`),
  createStudent: (data) => api.post('/students', data),
  updateStudent: (studentId, data) => api.patch(`/students/${studentId}`, data),
  deleteStudent: (studentId) => api.delete(`/students/${studentId}`),
  getAttendance: (studentId, params) => api.get(`/students/${studentId}/attendance`, { params }),
  getPerformance: (studentId, params) => api.get(`/students/${studentId}/performance`, { params }),
};

export const classAPI = {
  getClasses: (params) => api.get('/classes', { params }),
  getClass: (classId) => api.get(`/classes/${classId}`),
  createClass: (data) => api.post('/classes', data),
  updateClass: (classId, data) => api.patch(`/classes/${classId}`, data),
  deleteClass: (classId) => api.delete(`/classes/${classId}`),
  getStudents: (classId) => api.get(`/classes/${classId}/students`),
  addStudent: (classId, studentId) => api.post(`/classes/${classId}/students`, { studentId }),
  removeStudent: (classId, studentId) => api.delete(`/classes/${classId}/students/${studentId}`),
};

export const notificationAPI = {
  getNotifications: (params) => api.get('/notifications', { params }),
  markAsRead: (notificationId) => api.patch(`/notifications/${notificationId}/read`),
  markAllAsRead: () => api.patch('/notifications/read-all'),
  deleteNotification: (notificationId) => api.delete(`/notifications/${notificationId}`),
};

export default api;