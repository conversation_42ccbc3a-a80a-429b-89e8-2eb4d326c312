import { createSlice } from '@reduxjs/toolkit';
import { io } from 'socket.io-client';

const initialState = {
  socket: null,
  connected: false,
  onlineUsers: [],
  typingUsers: [],
  error: null
};

const socketSlice = createSlice({
  name: 'socket',
  initialState,
  reducers: {
    setSocket: (state, action) => {
      state.socket = action.payload;
    },
    setConnected: (state, action) => {
      state.connected = action.payload;
    },
    setOnlineUsers: (state, action) => {
      state.onlineUsers = action.payload;
    },
    addOnlineUser: (state, action) => {
      if (!state.onlineUsers.includes(action.payload)) {
        state.onlineUsers.push(action.payload);
      }
    },
    removeOnlineUser: (state, action) => {
      state.onlineUsers = state.onlineUsers.filter(id => id !== action.payload);
    },
    setTypingUsers: (state, action) => {
      state.typingUsers = action.payload;
    },
    addTypingUser: (state, action) => {
      if (!state.typingUsers.find(user => user.userId === action.payload.userId)) {
        state.typingUsers.push(action.payload);
      }
    },
    removeTypingUser: (state, action) => {
      state.typingUsers = state.typingUsers.filter(user => user.userId !== action.payload);
    },
    setError: (state, action) => {
      state.error = typeof action.payload === 'string' ? action.payload : 'Socket connection error';
    },
    disconnect: (state) => {
      if (state.socket) {
        state.socket.disconnect();
      }
      state.socket = null;
      state.connected = false;
      state.onlineUsers = [];
      state.typingUsers = [];
      state.error = null;
    }
  }
});

export const {
  setSocket,
  setConnected,
  setOnlineUsers,
  addOnlineUser,
  removeOnlineUser,
  setTypingUsers,
  addTypingUser,
  removeTypingUser,
  setError,
  disconnect
} = socketSlice.actions;

// Thunk to initialize socket connection
export const initializeSocket = () => (dispatch, getState) => {
  const { auth } = getState();
  
  if (!auth.token || !auth.user) {
    return;
  }

  const socket = io(process.env.REACT_APP_API_URL || 'http://localhost:5000', {
    auth: {
      token: auth.token
    },
    transports: ['websocket', 'polling']
  });

  // Connection events
  socket.on('connect', () => {
    console.log('Socket connected');
    dispatch(setConnected(true));
    dispatch(setError(null));
    
    // Join user's personal room and class rooms
    const classIds = auth.user.children?.map(child => child.classId) || 
                    auth.user.teachingClasses?.map(cls => cls.id) || [];
    
    if (classIds.length > 0) {
      socket.emit('join_classes', classIds);
    }
  });

  socket.on('disconnect', () => {
    console.log('Socket disconnected');
    dispatch(setConnected(false));
  });

  socket.on('connect_error', (error) => {
    console.error('Socket connection error:', error);
    dispatch(setError(error.message));
    dispatch(setConnected(false));
  });

  // User status events
  socket.on('user_status_update', (data) => {
    if (data.status === 'online') {
      dispatch(addOnlineUser(data.userId));
    } else if (data.status === 'offline') {
      dispatch(removeOnlineUser(data.userId));
    }
  });

  // Typing events
  socket.on('user_typing', (data) => {
    if (data.isTyping) {
      dispatch(addTypingUser(data));
    } else {
      dispatch(removeTypingUser(data.userId));
    }
  });

  // Message events
  socket.on('new_message', (message) => {
    dispatch({ type: 'messages/addMessage', payload: message });
  });

  socket.on('message_sent', (data) => {
    dispatch({ type: 'messages/updateMessageStatus', payload: data });
  });

  socket.on('message_error', (error) => {
    const errorMessage = typeof error.error === 'string' ? error.error : 'Message error occurred';
    dispatch({ type: 'messages/setError', payload: errorMessage });
  });

  // Video call events
  socket.on('call_started', (data) => {
    dispatch({ type: 'videoCall/callStarted', payload: data });
  });

  socket.on('call_ended', (data) => {
    dispatch({ type: 'videoCall/callEnded', payload: data });
  });

  socket.on('user_joined_call', (data) => {
    dispatch({ type: 'videoCall/userJoined', payload: data });
  });

  socket.on('user_left_call', (data) => {
    dispatch({ type: 'videoCall/userLeft', payload: data });
  });

  // WebRTC signaling events
  socket.on('webrtc_offer', (data) => {
    dispatch({ type: 'videoCall/receiveOffer', payload: data });
  });

  socket.on('webrtc_answer', (data) => {
    dispatch({ type: 'videoCall/receiveAnswer', payload: data });
  });

  socket.on('webrtc_ice_candidate', (data) => {
    dispatch({ type: 'videoCall/receiveIceCandidate', payload: data });
  });

  // Emergency notifications
  socket.on('emergency_notification', (data) => {
    dispatch({ type: 'notifications/addEmergencyNotification', payload: data });
  });

  // Screen sharing events
  socket.on('screen_share_started', (data) => {
    dispatch({ type: 'videoCall/screenShareStarted', payload: data });
  });

  socket.on('screen_share_stopped', (data) => {
    dispatch({ type: 'videoCall/screenShareStopped', payload: data });
  });

  // Call chat events
  socket.on('call_chat_message', (data) => {
    dispatch({ type: 'videoCall/addChatMessage', payload: data });
  });

  dispatch(setSocket(socket));
};

export default socketSlice.reducer;