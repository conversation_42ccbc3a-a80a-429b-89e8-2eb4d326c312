import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  XMarkIcon, 
  Cog6ToothIcon,
  MicrophoneIcon,
  VideoCameraIcon,
  SpeakerWaveIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';
import { useDispatch, useSelector } from 'react-redux';
import { updateCallSettings } from '../../store/slices/videoCallSlice';

const CallSettings = ({ onClose, currentCall }) => {
  const dispatch = useDispatch();
  const { callSettings } = useSelector((state) => state.videoCall);
  
  const [settings, setSettings] = useState({
    audioEnabled: callSettings.audioEnabled,
    videoEnabled: callSettings.videoEnabled,
    screenShareEnabled: callSettings.screenShareEnabled,
    chatEnabled: callSettings.chatEnabled,
    recordingEnabled: callSettings.recordingEnabled,
    audioDevice: 'default',
    videoDevice: 'default',
    speakerDevice: 'default',
    videoQuality: 'auto',
    audioQuality: 'high'
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = () => {
    dispatch(updateCallSettings(settings));
    onClose();
  };

  const SettingToggle = ({ label, description, value, onChange, icon: Icon }) => (
    <div className="flex items-center justify-between py-3">
      <div className="flex items-center space-x-3">
        <Icon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
        <div>
          <p className="text-sm font-medium text-gray-900 dark:text-white">{label}</p>
          {description && (
            <p className="text-xs text-gray-500 dark:text-gray-400">{description}</p>
          )}
        </div>
      </div>
      <button
        onClick={() => onChange(!value)}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          value ? 'bg-indigo-600' : 'bg-gray-200 dark:bg-gray-700'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            value ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );

  const SettingSelect = ({ label, value, onChange, options, icon: Icon }) => (
    <div className="py-3">
      <div className="flex items-center space-x-3 mb-2">
        <Icon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
        <label className="text-sm font-medium text-gray-900 dark:text-white">{label}</label>
      </div>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );

  return (
    <motion.div
      initial={{ x: '100%' }}
      animate={{ x: 0 }}
      exit={{ x: '100%' }}
      transition={{ type: 'spring', damping: 25, stiffness: 200 }}
      className="fixed right-0 top-0 bottom-0 w-80 bg-white dark:bg-gray-800 shadow-xl border-l border-gray-200 dark:border-gray-700 z-30 flex flex-col"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Cog6ToothIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Call Settings
          </h3>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
        >
          <XMarkIcon className="h-6 w-6" />
        </button>
      </div>

      {/* Settings Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* General Settings */}
        <div>
          <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
            General
          </h4>
          <div className="space-y-1 border-l-2 border-gray-200 dark:border-gray-700 pl-4">
            <SettingToggle
              label="Enable Audio"
              description="Allow audio in this call"
              value={settings.audioEnabled}
              onChange={(value) => handleSettingChange('audioEnabled', value)}
              icon={MicrophoneIcon}
            />
            <SettingToggle
              label="Enable Video"
              description="Allow video in this call"
              value={settings.videoEnabled}
              onChange={(value) => handleSettingChange('videoEnabled', value)}
              icon={VideoCameraIcon}
            />
            <SettingToggle
              label="Screen Sharing"
              description="Allow participants to share screens"
              value={settings.screenShareEnabled}
              onChange={(value) => handleSettingChange('screenShareEnabled', value)}
              icon={ComputerDesktopIcon}
            />
            <SettingToggle
              label="Chat"
              description="Enable chat during the call"
              value={settings.chatEnabled}
              onChange={(value) => handleSettingChange('chatEnabled', value)}
              icon={Cog6ToothIcon}
            />
          </div>
        </div>

        {/* Audio/Video Devices */}
        <div>
          <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
            Devices
          </h4>
          <div className="space-y-4">
            <SettingSelect
              label="Microphone"
              value={settings.audioDevice}
              onChange={(value) => handleSettingChange('audioDevice', value)}
              icon={MicrophoneIcon}
              options={[
                { value: 'default', label: 'Default Microphone' },
                { value: 'built-in', label: 'Built-in Microphone' },
                { value: 'external', label: 'External Microphone' }
              ]}
            />
            <SettingSelect
              label="Camera"
              value={settings.videoDevice}
              onChange={(value) => handleSettingChange('videoDevice', value)}
              icon={VideoCameraIcon}
              options={[
                { value: 'default', label: 'Default Camera' },
                { value: 'front', label: 'Front Camera' },
                { value: 'back', label: 'Back Camera' }
              ]}
            />
            <SettingSelect
              label="Speaker"
              value={settings.speakerDevice}
              onChange={(value) => handleSettingChange('speakerDevice', value)}
              icon={SpeakerWaveIcon}
              options={[
                { value: 'default', label: 'Default Speaker' },
                { value: 'built-in', label: 'Built-in Speaker' },
                { value: 'headphones', label: 'Headphones' }
              ]}
            />
          </div>
        </div>

        {/* Quality Settings */}
        <div>
          <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
            Quality
          </h4>
          <div className="space-y-4">
            <SettingSelect
              label="Video Quality"
              value={settings.videoQuality}
              onChange={(value) => handleSettingChange('videoQuality', value)}
              icon={VideoCameraIcon}
              options={[
                { value: 'auto', label: 'Auto (Recommended)' },
                { value: 'high', label: 'High (720p)' },
                { value: 'medium', label: 'Medium (480p)' },
                { value: 'low', label: 'Low (360p)' }
              ]}
            />
            <SettingSelect
              label="Audio Quality"
              value={settings.audioQuality}
              onChange={(value) => handleSettingChange('audioQuality', value)}
              icon={MicrophoneIcon}
              options={[
                { value: 'high', label: 'High Quality' },
                { value: 'medium', label: 'Medium Quality' },
                { value: 'low', label: 'Low Quality (Save Bandwidth)' }
              ]}
            />
          </div>
        </div>

        {/* Recording Settings */}
        {currentCall?.isRecorded && (
          <div>
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
              Recording
            </h4>
            <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                This call is being recorded. All participants have been notified.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md transition-colors"
          >
            Save Settings
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default CallSettings;