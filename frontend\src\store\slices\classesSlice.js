import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { classAPI } from '../../services/api';
import { toast } from 'react-hot-toast';

// Async thunks
export const fetchClasses = createAsyncThunk(
  'classes/fetchClasses',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await classAPI.getClasses(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch classes');
    }
  }
);

export const fetchClass = createAsyncThunk(
  'classes/fetchClass',
  async (classId, { rejectWithValue }) => {
    try {
      const response = await classAPI.getClass(classId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch class');
    }
  }
);

export const createClass = createAsyncThunk(
  'classes/createClass',
  async (classData, { rejectWithValue }) => {
    try {
      const response = await classAPI.createClass(classData);
      toast.success('Class created successfully!');
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to create class';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const updateClass = createAsyncThunk(
  'classes/updateClass',
  async ({ classId, updates }, { rejectWithValue }) => {
    try {
      const response = await classAPI.updateClass(classId, updates);
      toast.success('Class updated successfully!');
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to update class';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const fetchClassStudents = createAsyncThunk(
  'classes/fetchStudents',
  async ({ classId, params }, { rejectWithValue }) => {
    try {
      const response = await classAPI.getStudents(classId, params);
      return { classId, data: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch class students');
    }
  }
);

export const addStudentToClass = createAsyncThunk(
  'classes/addStudent',
  async ({ classId, studentId }, { rejectWithValue }) => {
    try {
      await classAPI.addStudent(classId, studentId);
      toast.success('Student added to class successfully!');
      return { classId, studentId };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to add student to class';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const removeStudentFromClass = createAsyncThunk(
  'classes/removeStudent',
  async ({ classId, studentId }, { rejectWithValue }) => {
    try {
      await classAPI.removeStudent(classId, studentId);
      toast.success('Student removed from class successfully!');
      return { classId, studentId };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to remove student from class';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

const initialState = {
  classes: [],
  selectedClass: null,
  classStudents: {},
  loading: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0
  },
  filters: {
    search: '',
    grade: null,
    schoolYear: null
  }
};

const classesSlice = createSlice({
  name: 'classes',
  initialState,
  reducers: {
    setSelectedClass: (state, action) => {
      state.selectedClass = action.payload;
    },
    clearSelectedClass: (state) => {
      state.selectedClass = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        search: '',
        grade: null,
        schoolYear: null
      };
    },
    updateClassInList: (state, action) => {
      const updatedClass = action.payload;
      const index = state.classes.findIndex(c => c.id === updatedClass.id);
      if (index !== -1) {
        state.classes[index] = updatedClass;
      }
    },
    removeClassFromList: (state, action) => {
      const classId = action.payload;
      state.classes = state.classes.filter(c => c.id !== classId);
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Classes
      .addCase(fetchClasses.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchClasses.fulfilled, (state, action) => {
        state.loading = false;
        state.classes = action.payload.classes || [];
        state.pagination = {
          currentPage: action.payload.currentPage || 1,
          totalPages: action.payload.totalPages || 1,
          totalCount: action.payload.totalCount || 0
        };
      })
      .addCase(fetchClasses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Fetch Class
      .addCase(fetchClass.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchClass.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedClass = action.payload;
      })
      .addCase(fetchClass.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Create Class
      .addCase(createClass.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createClass.fulfilled, (state, action) => {
        state.loading = false;
        state.classes.unshift(action.payload);
        state.pagination.totalCount += 1;
      })
      .addCase(createClass.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update Class
      .addCase(updateClass.fulfilled, (state, action) => {
        const updatedClass = action.payload;
        const index = state.classes.findIndex(c => c.id === updatedClass.id);
        if (index !== -1) {
          state.classes[index] = updatedClass;
        }
        if (state.selectedClass && state.selectedClass.id === updatedClass.id) {
          state.selectedClass = updatedClass;
        }
      })
      
      // Fetch Class Students
      .addCase(fetchClassStudents.fulfilled, (state, action) => {
        const { classId, data } = action.payload;
        state.classStudents[classId] = data;
      })
      
      // Add Student to Class
      .addCase(addStudentToClass.fulfilled, (state, action) => {
        const { classId } = action.payload;
        // Update class enrollment count
        const classIndex = state.classes.findIndex(c => c.id === classId);
        if (classIndex !== -1) {
          state.classes[classIndex].studentCount += 1;
        }
      })
      
      // Remove Student from Class
      .addCase(removeStudentFromClass.fulfilled, (state, action) => {
        const { classId, studentId } = action.payload;
        // Update class enrollment count
        const classIndex = state.classes.findIndex(c => c.id === classId);
        if (classIndex !== -1) {
          state.classes[classIndex].studentCount = Math.max(0, state.classes[classIndex].studentCount - 1);
        }
        
        // Remove from class students list
        if (state.classStudents[classId]) {
          state.classStudents[classId].students = state.classStudents[classId].students.filter(
            s => s.id !== studentId
          );
        }
      });
  }
});

export const {
  setSelectedClass,
  clearSelectedClass,
  setFilters,
  clearFilters,
  updateClassInList,
  removeClassFromList,
  setError,
  clearError
} = classesSlice.actions;

export default classesSlice.reducer;