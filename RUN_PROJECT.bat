@echo off
echo Starting FamEduConnect Project...

REM Kill any existing node processes
taskkill /f /im node.exe 2>nul

REM Start backend
echo Starting Backend...
start "Backend" cmd /k "cd backend && node server.js"

REM Wait 3 seconds
timeout /t 3 /nobreak >nul

REM Start frontend
echo Starting Frontend...
start "Frontend" cmd /k "cd frontend && npm start"

REM Wait 3 seconds
timeout /t 3 /nobreak >nul

REM Start admin
echo Starting Admin...
start "Admin" cmd /k "cd admin && npm start"

echo.
echo FamEduConnect is starting up!
echo Backend: http://localhost:8080
echo Frontend: http://localhost:3000
echo Admin: http://localhost:3001
echo.
pause