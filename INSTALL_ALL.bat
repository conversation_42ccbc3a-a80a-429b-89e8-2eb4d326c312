@echo off
echo Installing all dependencies...

echo Installing root dependencies...
npm install

echo Installing backend dependencies...
cd backend
npm install
cd ..

echo Installing frontend dependencies...
cd frontend
npm install --legacy-peer-deps
cd ..

echo Installing admin dependencies...
cd admin
npm install --legacy-peer-deps
cd ..

echo Installing mobile dependencies...
cd mobile
npm install --legacy-peer-deps
cd ..

echo.
echo All dependencies installed!
echo Run RUN_PROJECT.bat to start the application
pause