#!/bin/bash

# FamEduConnect Staging Deployment Script
set -e

echo "🚀 Deploying FamEduConnect Backend to Staging"

# Check if required environment variables are set
if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
    echo "❌ Error: AWS credentials not set"
    exit 1
fi

if [ -z "$KUBECONFIG" ]; then
    echo "❌ Error: KUBECONFIG not set"
    exit 1
fi

# Set variables
NAMESPACE="staging"
RELEASE_NAME="famedu-backend"
IMAGE_TAG=${IMAGE_TAG:-"latest"}

echo "📋 Deployment Configuration:"
echo "  Namespace: $NAMESPACE"
echo "  Release Name: $RELEASE_NAME"
echo "  Image Tag: $IMAGE_TAG"

# Create namespace if it doesn't exist
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Create secrets (you should store these securely in production)
echo "🔐 Creating Kubernetes secrets..."
kubectl create secret generic famedu-backend-secrets \
  --namespace=$NAMESPACE \
  --from-literal=DATABASE_URL="$DATABASE_URL" \
  --from-literal=JWT_SECRET="$JWT_SECRET" \
  --from-literal=REDIS_URL="$REDIS_URL" \
  --from-literal=RABBITMQ_URL="$RABBITMQ_URL" \
  --from-literal=SENTRY_DSN="$SENTRY_DSN" \
  --dry-run=client -o yaml | kubectl apply -f -

# Deploy using Helm
echo "📦 Deploying with Helm..."
helm upgrade --install $RELEASE_NAME ./helm \
  --namespace $NAMESPACE \
  --set image.tag=$IMAGE_TAG \
  --set environment=staging \
  --set secrets.DATABASE_URL="$DATABASE_URL" \
  --set secrets.JWT_SECRET="$JWT_SECRET" \
  --set secrets.REDIS_URL="$REDIS_URL" \
  --set secrets.RABBITMQ_URL="$RABBITMQ_URL" \
  --set secrets.SENTRY_DSN="$SENTRY_DSN"

# Wait for deployment to be ready
echo "⏳ Waiting for deployment to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/$RELEASE_NAME -n $NAMESPACE

# Get the service URL
SERVICE_URL=$(kubectl get svc $RELEASE_NAME -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
if [ -z "$SERVICE_URL" ]; then
    SERVICE_URL=$(kubectl get svc $RELEASE_NAME -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
fi

echo "🔗 Service URL: http://$SERVICE_URL"

# Run smoke tests
echo "🧪 Running smoke tests..."
sleep 10  # Give the service time to fully start

# Test health endpoint
if curl -f http://$SERVICE_URL/api/health; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    exit 1
fi

# Test API documentation
if curl -f http://$SERVICE_URL/docs; then
    echo "✅ API documentation accessible"
else
    echo "❌ API documentation not accessible"
fi

# Test metrics endpoint
if curl -f http://$SERVICE_URL/metrics; then
    echo "✅ Metrics endpoint accessible"
else
    echo "❌ Metrics endpoint not accessible"
fi

echo "🎉 Staging deployment completed successfully!"
echo "📚 API Documentation: http://$SERVICE_URL/docs"
echo "📊 Metrics: http://$SERVICE_URL/metrics"
echo "🔗 Health Check: http://$SERVICE_URL/api/health" 