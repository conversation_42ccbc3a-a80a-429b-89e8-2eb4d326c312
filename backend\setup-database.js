const { sequelize } = require('./models');
const { User } = require('./models');
const bcrypt = require('bcryptjs');

async function setupDatabase() {
  try {
    console.log('🔧 Setting up database...');
    
    // Force sync to ensure all columns are created
    await sequelize.sync({ force: true });
    console.log('✅ Database tables created successfully!');
    
    // Create admin user
    const adminExists = await User.findOne({
      where: { email: '<EMAIL>' }
    });
    
    if (!adminExists) {
      await User.create({
        email: '<EMAIL>',
        password: 'AdminDemo2025!',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        isVerified: true,
        hasCompletedOnboarding: false
      });
      console.log('✅ Admin user created successfully!');
    } else {
      console.log('ℹ️ Admin user already exists');
    }
    
    console.log('🎉 Database setup completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error setting up database:', error);
    process.exit(1);
  }
}

setupDatabase(); 