import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { notificationAPI } from '../../services/api';
import { toast } from 'react-hot-toast';

// Async thunks
export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await notificationAPI.getNotifications(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch notifications');
    }
  }
);

export const markAsRead = createAsyncThunk(
  'notifications/markAsRead',
  async (notificationId, { rejectWithValue }) => {
    try {
      await notificationAPI.markAsRead(notificationId);
      return notificationId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark as read');
    }
  }
);

export const markAllAsRead = createAsyncThunk(
  'notifications/markAllAsRead',
  async (_, { rejectWithValue }) => {
    try {
      await notificationAPI.markAllAsRead();
      return true;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark all as read');
    }
  }
);

export const deleteNotification = createAsyncThunk(
  'notifications/deleteNotification',
  async (notificationId, { rejectWithValue }) => {
    try {
      await notificationAPI.deleteNotification(notificationId);
      return notificationId;
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to delete notification';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

const initialState = {
  notifications: [],
  unreadCount: 0,
  unreadMessages: 0,
  unreadNotifications: 0,
  loading: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0
  },
  filters: {
    type: null,
    priority: null,
    isRead: null
  },
  emergencyNotifications: []
};

const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    addNotification: (state, action) => {
      const notification = action.payload;
      state.notifications.unshift(notification);
      
      if (!notification.isRead) {
        state.unreadCount += 1;
        state.unreadNotifications += 1;
      }
      
      // Show toast for high priority notifications
      if (notification.priority === 'high' || notification.priority === 'urgent') {
        toast(notification.message, {
          icon: notification.type === 'emergency' ? '🚨' : '📢',
          duration: notification.priority === 'urgent' ? 8000 : 5000,
          style: {
            background: notification.priority === 'urgent' ? '#dc2626' : '#f59e0b',
            color: 'white',
          },
        });
      }
    },
    addEmergencyNotification: (state, action) => {
      const notification = action.payload;
      state.emergencyNotifications.unshift(notification);
      
      // Show emergency toast
      toast.error(notification.message, {
        duration: 10000,
        icon: '🚨',
        style: {
          background: '#dc2626',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold'
        }
      });
    },
    removeEmergencyNotification: (state, action) => {
      const notificationId = action.payload;
      state.emergencyNotifications = state.emergencyNotifications.filter(
        n => n.id !== notificationId
      );
    },
    updateUnreadCounts: (state, action) => {
      const { messages, notifications } = action.payload;
      state.unreadMessages = messages || 0;
      state.unreadNotifications = notifications || 0;
      state.unreadCount = (messages || 0) + (notifications || 0);
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        type: null,
        priority: null,
        isRead: null
      };
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearAllNotifications: (state) => {
      state.notifications = [];
      state.unreadCount = 0;
      state.unreadNotifications = 0;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Notifications
      .addCase(fetchNotifications.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.loading = false;
        state.notifications = action.payload.notifications || [];
        state.unreadCount = action.payload.unreadCount || 0;
        state.pagination = {
          currentPage: action.payload.currentPage || 1,
          totalPages: action.payload.totalPages || 1,
          totalCount: action.payload.totalCount || 0
        };
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Mark as Read
      .addCase(markAsRead.fulfilled, (state, action) => {
        const notificationId = action.payload;
        const notification = state.notifications.find(n => n.id === notificationId);
        
        if (notification && !notification.isRead) {
          notification.isRead = true;
          notification.readAt = new Date().toISOString();
          state.unreadCount = Math.max(0, state.unreadCount - 1);
          state.unreadNotifications = Math.max(0, state.unreadNotifications - 1);
        }
      })
      
      // Mark All as Read
      .addCase(markAllAsRead.fulfilled, (state) => {
        state.notifications.forEach(notification => {
          if (!notification.isRead) {
            notification.isRead = true;
            notification.readAt = new Date().toISOString();
          }
        });
        state.unreadCount = 0;
        state.unreadNotifications = 0;
      })
      
      // Delete Notification
      .addCase(deleteNotification.fulfilled, (state, action) => {
        const notificationId = action.payload;
        const notificationIndex = state.notifications.findIndex(n => n.id === notificationId);
        
        if (notificationIndex !== -1) {
          const notification = state.notifications[notificationIndex];
          if (!notification.isRead) {
            state.unreadCount = Math.max(0, state.unreadCount - 1);
            state.unreadNotifications = Math.max(0, state.unreadNotifications - 1);
          }
          state.notifications.splice(notificationIndex, 1);
          state.pagination.totalCount = Math.max(0, state.pagination.totalCount - 1);
        }
      });
  }
});

export const {
  addNotification,
  addEmergencyNotification,
  removeEmergencyNotification,
  updateUnreadCounts,
  setFilters,
  clearFilters,
  setError,
  clearError,
  clearAllNotifications
} = notificationsSlice.actions;

export default notificationsSlice.reducer;