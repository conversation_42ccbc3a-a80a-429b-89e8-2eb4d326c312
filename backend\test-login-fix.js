const axios = require('axios');

console.log('🔧 Testing Login API Fix');
console.log('=' * 50);

async function testLogin() {
  try {
    console.log('\n1️⃣ Testing Login API...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'AdminDemo2025!'
    };
    
    console.log('📤 Sending login data:', JSON.stringify(loginData, null, 2));
    
    const response = await axios.post('http://localhost:5555/api/auth/login', loginData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    console.log('✅ Login Response Status:', response.status);
    console.log('✅ Login Response Data:', JSON.stringify(response.data, null, 2));
    
    console.log('\n🎉 Login API is working correctly!');
    
  } catch (error) {
    console.error('\n❌ Login test failed:', error.message);
    
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
    
    if (error.request) {
      console.error('Request Error:', error.request);
    }
  }
}

testLogin(); 