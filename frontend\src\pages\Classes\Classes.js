import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';

const Classes = () => {
  const { user } = useSelector(state => state.auth);
  const [classes, setClasses] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading classes data
    setTimeout(() => {
      setClasses([
        { 
          id: 1, 
          name: 'Mathematics 5A', 
          subject: 'Mathematics',
          students: 25,
          schedule: 'Mon, Wed, Fri - 9:00 AM'
        },
        { 
          id: 2, 
          name: 'English Literature', 
          subject: 'English',
          students: 20,
          schedule: 'Tu<PERSON>, Thu - 10:30 AM'
        },
        { 
          id: 3, 
          name: 'Science Lab', 
          subject: 'Science',
          students: 18,
          schedule: 'Wed - 2:00 PM'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-md">
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900">Classes</h1>
          <p className="text-gray-600 mt-2">Manage your classes and schedules</p>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {classes.map((classItem) => (
              <div key={classItem.id} className="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {classItem.name}
                </h3>
                <p className="text-gray-600 mb-2">
                  <span className="font-medium">Subject:</span> {classItem.subject}
                </p>
                <p className="text-gray-600 mb-2">
                  <span className="font-medium">Students:</span> {classItem.students}
                </p>
                <p className="text-gray-600 mb-4">
                  <span className="font-medium">Schedule:</span> {classItem.schedule}
                </p>
                <div className="flex space-x-2">
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                    View Details
                  </button>
                  <button className="bg-gray-600 text-white px-4 py-2 rounded-md text-sm hover:bg-gray-700">
                    Edit
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Classes;