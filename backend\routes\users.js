const express = require('express');
const { User, Student, Class } = require('../models');
const { body, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');
const multer = require('multer');
const path = require('path');

const router = express.Router();

// Configure multer for profile picture uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/profiles/');
  },
  filename: (req, file, cb) => {
    cb(null, `${req.user.userId}-${Date.now()}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Get current user profile
router.get('/profile', authMiddleware, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.userId, {
      attributes: { exclude: ['password', 'twoFactorSecret', 'encryptionKey'] },
      include: [
        {
          model: Student,
          as: 'children',
          include: [
            {
              model: Class,
              as: 'class',
              attributes: ['id', 'className', 'classCode', 'grade']
            }
          ]
        },
        {
          model: Class,
          as: 'teachingClasses',
          attributes: ['id', 'className', 'classCode', 'grade', 'currentEnrollment']
        }
      ]
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update user profile
router.patch('/profile', authMiddleware, [
  body('firstName').optional().trim().isLength({ min: 1, max: 50 }),
  body('lastName').optional().trim().isLength({ min: 1, max: 50 }),
  body('phone').optional().matches(/^[\+]?[1-9][\d]{0,15}$/),
  body('preferredLanguage').optional().isIn(['en', 'es', 'fr', 'de', 'zh', 'ar', 'hi', 'pt', 'ru', 'ja']),
  body('role').optional().isIn(['parent', 'teacher', 'student', 'admin']),
  body('hasCompletedOnboarding').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const allowedUpdates = [
      'firstName', 'lastName', 'phone', 'preferredLanguage', 
      'timezone', 'notificationSettings', 'accessibilitySettings',
      'role', 'hasCompletedOnboarding', 'preferences'
    ];
    
    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    await user.update(updates);

    const updatedUser = await User.findByPk(req.user.userId, {
      attributes: { exclude: ['password', 'twoFactorSecret', 'encryptionKey'] }
    });

    res.json(updatedUser);
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Upload profile picture
router.post('/profile/picture', authMiddleware, upload.single('profilePicture'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    await user.update({
      profilePicture: `/uploads/profiles/${req.file.filename}`
    });

    res.json({
      message: 'Profile picture updated successfully',
      profilePicture: user.profilePicture
    });
  } catch (error) {
    console.error('Upload profile picture error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Change password
router.patch('/password', authMiddleware, [
  body('currentPassword').exists(),
  body('newPassword').isLength({ min: 8, max: 128 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { currentPassword, newPassword } = req.body;

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify current password
    const isValidPassword = await user.validatePassword(currentPassword);
    if (!isValidPassword) {
      return res.status(400).json({ message: 'Current password is incorrect' });
    }

    // Update password
    await user.update({ password: newPassword });

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Enable/disable biometric authentication
router.patch('/biometric', authMiddleware, [
  body('enabled').isBoolean(),
  body('biometricData').optional().exists()
], async (req, res) => {
  try {
    const { enabled, biometricData } = req.body;

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const updates = { biometricEnabled: enabled };

    if (enabled && biometricData) {
      // Hash biometric data (simplified - use proper biometric hashing in production)
      const crypto = require('crypto');
      updates.biometricHash = crypto.createHash('sha256').update(biometricData).digest('hex');
    } else if (!enabled) {
      updates.biometricHash = null;
    }

    await user.update(updates);

    res.json({ 
      message: `Biometric authentication ${enabled ? 'enabled' : 'disabled'} successfully` 
    });
  } catch (error) {
    console.error('Biometric settings error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get user's children (for parents)
router.get('/children', authMiddleware, async (req, res) => {
  try {
    if (req.user.role !== 'parent') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const children = await Student.findAll({
      where: { parentId: req.user.userId },
      include: [
        {
          model: Class,
          as: 'class',
          include: [
            {
              model: User,
              as: 'teacher',
              attributes: ['id', 'firstName', 'lastName', 'email']
            }
          ]
        }
      ]
    });

    res.json(children);
  } catch (error) {
    console.error('Get children error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get user's classes (for teachers)
router.get('/classes', authMiddleware, async (req, res) => {
  try {
    if (req.user.role !== 'teacher') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const classes = await Class.findAll({
      where: { teacherId: req.user.userId },
      include: [
        {
          model: Student,
          as: 'students',
          include: [
            {
              model: User,
              as: 'parent',
              attributes: ['id', 'firstName', 'lastName', 'email', 'phone']
            }
          ]
        }
      ]
    });

    res.json(classes);
  } catch (error) {
    console.error('Get classes error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Search users (for messaging)
router.get('/search', authMiddleware, async (req, res) => {
  try {
    const { query, role } = req.query;

    if (!query || query.length < 2) {
      return res.status(400).json({ message: 'Query must be at least 2 characters' });
    }

    const whereClause = {
      isActive: true,
      [require('sequelize').Op.or]: [
        {
          firstName: {
            [require('sequelize').Op.iLike]: `%${query}%`
          }
        },
        {
          lastName: {
            [require('sequelize').Op.iLike]: `%${query}%`
          }
        },
        {
          email: {
            [require('sequelize').Op.iLike]: `%${query}%`
          }
        }
      ]
    };

    if (role) {
      whereClause.role = role;
    }

    const users = await User.findAll({
      where: whereClause,
      attributes: ['id', 'firstName', 'lastName', 'email', 'role', 'profilePicture'],
      limit: 10
    });

    res.json(users);
  } catch (error) {
    console.error('Search users error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete account
router.delete('/account', authMiddleware, [
  body('password').exists(),
  body('confirmDelete').equals('DELETE')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { password } = req.body;

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return res.status(400).json({ message: 'Password is incorrect' });
    }

    // Soft delete - deactivate account
    await user.update({
      isActive: false,
      email: `deleted_${Date.now()}_${user.email}`
    });

    res.json({ message: 'Account deleted successfully' });
  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;