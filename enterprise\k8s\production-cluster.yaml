apiVersion: v1
kind: Namespace
metadata:
  name: fameduconnect-prod
  labels:
    name: fameduconnect-prod
    environment: production
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fameduconnect-sa
  namespace: fameduconnect-prod
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fameduconnect-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "update"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fameduconnect-role-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fameduconnect-role
subjects:
- kind: ServiceAccount
  name: fameduconnect-sa
  namespace: fameduconnect-prod
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fameduconnect-config
  namespace: fameduconnect-prod
data:
  NODE_ENV: "production"
  PORT: "3001"
  CORS_ORIGIN: "https://fameduconnect.com,https://admin.fameduconnect.com"
  LOG_LEVEL: "info"
  DATABASE_URL: "postgresql://fameduconnect:${DB_PASSWORD}@fameduconnect-postgres.fameduconnect-database.svc.cluster.local:5432/fameduconnect_prod"
  REDIS_URL: "redis://fameduconnect-redis.fameduconnect-cache.svc.cluster.local:6379"
  JWT_SECRET: "${JWT_SECRET}"
  JWT_EXPIRES_IN: "24h"
  SESSION_SECRET: "${SESSION_SECRET}"
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_USER: "${SMTP_USER}"
  SMTP_PASS: "${SMTP_PASS}"
  AWS_ACCESS_KEY_ID: "${AWS_ACCESS_KEY_ID}"
  AWS_SECRET_ACCESS_KEY: "${AWS_SECRET_ACCESS_KEY}"
  AWS_REGION: "us-east-1"
  AWS_S3_BUCKET: "fameduconnect-files"
  STRIPE_SECRET_KEY: "${STRIPE_SECRET_KEY}"
  STRIPE_WEBHOOK_SECRET: "${STRIPE_WEBHOOK_SECRET}"
  SENTRY_DSN: "${SENTRY_DSN}"
  NEW_RELIC_LICENSE_KEY: "${NEW_RELIC_LICENSE_KEY}"
---
apiVersion: v1
kind: Secret
metadata:
  name: fameduconnect-secrets
  namespace: fameduconnect-prod
type: Opaque
data:
  DB_PASSWORD: "${BASE64_ENCODED_DB_PASSWORD}"
  JWT_SECRET: "${BASE64_ENCODED_JWT_SECRET}"
  SESSION_SECRET: "${BASE64_ENCODED_SESSION_SECRET}"
  SMTP_USER: "${BASE64_ENCODED_SMTP_USER}"
  SMTP_PASS: "${BASE64_ENCODED_SMTP_PASS}"
  AWS_ACCESS_KEY_ID: "${BASE64_ENCODED_AWS_ACCESS_KEY_ID}"
  AWS_SECRET_ACCESS_KEY: "${BASE64_ENCODED_AWS_SECRET_ACCESS_KEY}"
  STRIPE_SECRET_KEY: "${BASE64_ENCODED_STRIPE_SECRET_KEY}"
  STRIPE_WEBHOOK_SECRET: "${BASE64_ENCODED_STRIPE_WEBHOOK_SECRET}"
  SENTRY_DSN: "${BASE64_ENCODED_SENTRY_DSN}"
  NEW_RELIC_LICENSE_KEY: "${BASE64_ENCODED_NEW_RELIC_LICENSE_KEY}"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fameduconnect-backend
  namespace: fameduconnect-prod
  labels:
    app: fameduconnect-backend
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: fameduconnect-backend
  template:
    metadata:
      labels:
        app: fameduconnect-backend
        version: v1.0.0
    spec:
      serviceAccountName: fameduconnect-sa
      containers:
      - name: backend
        image: fameduconnect/backend:v1.0.0
        ports:
        - containerPort: 3001
        envFrom:
        - configMapRef:
            name: fameduconnect-config
        - secretRef:
            name: fameduconnect-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: tmp
        emptyDir: {}
      - name: logs
        persistentVolumeClaim:
          claimName: fameduconnect-logs-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fameduconnect-frontend
  namespace: fameduconnect-prod
  labels:
    app: fameduconnect-frontend
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: fameduconnect-frontend
  template:
    metadata:
      labels:
        app: fameduconnect-frontend
        version: v1.0.0
    spec:
      containers:
      - name: frontend
        image: fameduconnect/frontend:v1.0.0
        ports:
        - containerPort: 3000
        env:
        - name: REACT_APP_API_URL
          value: "https://api.fameduconnect.com"
        - name: REACT_APP_SOCKET_URL
          value: "wss://api.fameduconnect.com"
        - name: REACT_APP_SENTRY_DSN
          valueFrom:
            secretKeyRef:
              name: fameduconnect-secrets
              key: SENTRY_DSN
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fameduconnect-admin
  namespace: fameduconnect-prod
  labels:
    app: fameduconnect-admin
    version: v1.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: fameduconnect-admin
  template:
    metadata:
      labels:
        app: fameduconnect-admin
        version: v1.0.0
    spec:
      containers:
      - name: admin
        image: fameduconnect/admin:v1.0.0
        ports:
        - containerPort: 3000
        env:
        - name: REACT_APP_API_URL
          value: "https://api.fameduconnect.com"
        - name: REACT_APP_SENTRY_DSN
          valueFrom:
            secretKeyRef:
              name: fameduconnect-secrets
              key: SENTRY_DSN
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
---
apiVersion: v1
kind: Service
metadata:
  name: fameduconnect-backend-service
  namespace: fameduconnect-prod
  labels:
    app: fameduconnect-backend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3001
    protocol: TCP
    name: http
  selector:
    app: fameduconnect-backend
---
apiVersion: v1
kind: Service
metadata:
  name: fameduconnect-frontend-service
  namespace: fameduconnect-prod
  labels:
    app: fameduconnect-frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: fameduconnect-frontend
---
apiVersion: v1
kind: Service
metadata:
  name: fameduconnect-admin-service
  namespace: fameduconnect-prod
  labels:
    app: fameduconnect-admin
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: fameduconnect-admin
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fameduconnect-ingress
  namespace: fameduconnect-prod
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - fameduconnect.com
    - www.fameduconnect.com
    - api.fameduconnect.com
    - admin.fameduconnect.com
    secretName: fameduconnect-tls
  rules:
  - host: fameduconnect.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fameduconnect-frontend-service
            port:
              number: 80
  - host: www.fameduconnect.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fameduconnect-frontend-service
            port:
              number: 80
  - host: api.fameduconnect.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fameduconnect-backend-service
            port:
              number: 80
  - host: admin.fameduconnect.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fameduconnect-admin-service
            port:
              number: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fameduconnect-backend-hpa
  namespace: fameduconnect-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fameduconnect-backend
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fameduconnect-frontend-hpa
  namespace: fameduconnect-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fameduconnect-frontend
  minReplicas: 3
  maxReplicas: 15
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: fameduconnect-logs-pvc
  namespace: fameduconnect-prod
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: fameduconnect-backend-pdb
  namespace: fameduconnect-prod
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: fameduconnect-backend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: fameduconnect-frontend-pdb
  namespace: fameduconnect-prod
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: fameduconnect-frontend 