import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon, FingerPrintIcon as FingerprintIcon, QrCodeIcon } from '@heroicons/react/24/outline';
import { login, biometricLogin, clearError } from '../../store/slices/authSlice';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import LanguageSelector from '../../components/UI/LanguageSelector';
import SafeErrorDisplay from '../../components/UI/SafeErrorDisplay';

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [loginMethod, setLoginMethod] = useState('email');
  const [biometricSupported, setBiometricSupported] = useState(false);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.auth);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm();

  useEffect(() => {
    console.log('🔍 Login component mounted');
    if (navigator.credentials && window.PublicKeyCredential) {
      setBiometricSupported(true);
    }
    dispatch(clearError());
  }, [dispatch]);

  const onSubmit = async (data) => {
    console.log('✅ FORM SUBMISSION TRIGGERED:', data);
    console.log('📧 Email:', data.email);
    console.log('🔑 Password:', data.password);
    console.log('🚀 About to dispatch login...');
    
    try {
      console.log('🔄 Dispatching login action...');
      const result = await dispatch(login(data)).unwrap();
      console.log('✅ Login successful:', result);
      console.log('🔄 Navigating to dashboard...');
      navigate('/dashboard');
    } catch (err) {
      console.error('❌ Login Error:', err);
      console.error('❌ Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });
    }
  };

  const handleBiometricLogin = async () => {
    const email = watch('email');
    if (!email) {
      alert('Please enter your email first');
      return;
    }
    const biometricData = 'mock_biometric_data_' + Date.now();
    try {
      await dispatch(biometricLogin({ email, biometricData })).unwrap();
      navigate('/dashboard');
    } catch (err) {
      console.error('❌ Biometric Login Error:', err);
    }
  };

  const handleQRLogin = () => {
    alert('QR Code login will be implemented with camera access');
  };

  // Debug form state
  const formValues = watch();
  console.log('📝 Current form values:', formValues);
  console.log('❌ Form errors:', errors);
  console.log('🔄 Loading state:', loading);
  console.log('🚨 Error state:', error);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="text-center">
          <div className="mx-auto h-16 w-16 bg-indigo-600 rounded-full flex items-center justify-center mb-4">
            <span className="text-white text-2xl font-bold">F</span>
          </div>
          <h2 className="text-3xl font-extrabold text-gray-900 dark:text-white">Welcome to FamEduConnect</h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Connecting families and education</p>
          <div className="mt-4 flex justify-center">
            <LanguageSelector />
          </div>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, delay: 0.1 }} className="flex justify-center space-x-4 mb-6">
          <button
            onClick={() => setLoginMethod('email')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              loginMethod === 'email'
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300'
            }`}
          >
            Email
          </button>
          {biometricSupported && (
            <button
              onClick={() => setLoginMethod('biometric')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2 ${
                loginMethod === 'biometric'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              <FingerprintIcon className="h-4 w-4" />
              <span>Biometric</span>
            </button>
          )}
          <button
            onClick={() => setLoginMethod('qr')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2 ${
              loginMethod === 'qr'
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300'
            }`}
          >
            <QrCodeIcon className="h-4 w-4" />
            <span>QR Code</span>
          </button>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5, delay: 0.2 }} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          {loginMethod === 'email' && (
            <form 
              className="space-y-6" 
              onSubmit={handleSubmit(onSubmit)} 
              noValidate
            >
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Email Address
                </label>
                <input
                  name="email"
                  type="email"
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[^@]+@[^@]+\.[^@]+$/,
                      message: 'Invalid email address'
                    }
                  })}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white text-lg"
                  placeholder="Enter your email"
                />
                {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Password
                </label>
                <div className="mt-1 relative">
                  <input
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    {...register('password', {
                      required: 'Password is required',
                      minLength: { value: 8, message: 'Password must be at least 8 characters' }
                    })}
                    className="block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white text-lg"
                    placeholder="Enter your password"
                  />
                  <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    {showPassword ? <EyeSlashIcon className="h-5 w-5 text-gray-400" /> : <EyeIcon className="h-5 w-5 text-gray-400" />}
                  </button>
                </div>
                {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input id="remember-me" type="checkbox" className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900 dark:text-gray-300">Remember me</label>
                </div>
                <div className="text-sm">
                  <Link to="/forgot-password" className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">Forgot your password?</Link>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={loading}
                  onClick={() => console.log('🔘 Submit button clicked')}
                  className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-lg"
                >
                  {loading ? <LoadingSpinner size="sm" /> : 'Sign In'}
                </button>
              </div>
            </form>
          )}

          {loginMethod === 'biometric' && (
            <div className="text-center space-y-6">
              <div className="mx-auto w-24 h-24 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                <FingerprintIcon className="h-12 w-12 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Biometric Authentication</h3>
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Use your fingerprint or face ID to sign in securely</p>
              </div>
              <div>
                <input
                  name="biometric_email"
                  type="email"
                  {...register('email', { required: 'Email is required for biometric login' })}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter your email"
                />
                {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>}
              </div>
              <button onClick={handleBiometricLogin} disabled={loading} className="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                {loading ? <LoadingSpinner size="sm" /> : 'Authenticate with Biometric'}
              </button>
            </div>
          )}

          {loginMethod === 'qr' && (
            <div className="text-center space-y-6">
              <div className="mx-auto w-24 h-24 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                <QrCodeIcon className="h-12 w-12 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">QR Code Login</h3>
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Scan the QR code with your mobile device to sign in</p>
              </div>
              <div className="mx-auto w-48 h-48 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <QrCodeIcon className="mx-auto h-16 w-16 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-500">QR Code will appear here</p>
                </div>
              </div>
              <button onClick={handleQRLogin} className="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                Generate QR Code
              </button>
            </div>
          )}

          {error && (
            <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }}>
              <SafeErrorDisplay error={error} className="mt-4" />
            </motion.div>
          )}

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Don't have an account?{' '}
              <Link to="/register" className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
                Sign up here
              </Link>
            </p>
          </div>
        </motion.div>

        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5, delay: 0.4 }} className="text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">Need accessibility assistance? Press Alt+A for voice navigation</p>
        </motion.div>
      </div>
    </div>
  );
};

export default Login;