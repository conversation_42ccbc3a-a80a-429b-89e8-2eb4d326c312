import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { login } from '../../store/slices/authSlice';

const TestLogin = () => {
  const dispatch = useDispatch();
  const [credentials, setCredentials] = useState({
    email: '<EMAIL>',
    password: 'AdminDemo2025!'
  });
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState('');

  const handleLogin = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('🔍 Attempting login with:', credentials);
      
      const result = await dispatch(login(credentials)).unwrap();
      console.log('✅ Login successful:', result);
      setResult('✅ Login successful! Redirecting to dashboard...');
      
      // Redirect to dashboard after successful login
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 2000);
      
    } catch (error) {
      console.error('❌ Login failed:', error);
      setResult(`❌ Login failed: ${error.message || error.toString() || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testBackendHealth = async () => {
    try {
      const response = await fetch('/api/test');
      const data = await response.json();
      setResult(`✅ Backend health check: ${JSON.stringify(data)}`);
    } catch (error) {
      setResult(`❌ Backend health check failed: ${error.message}`);
    }
  };

  const testDirectLogin = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('🔍 Testing direct login API call...');
      
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials)
      });
      
      const data = await response.json();
      console.log('📡 Direct API response:', data);
      
      if (response.ok) {
        setResult(`✅ Direct login successful! Token: ${data.token.substring(0, 20)}...`);
      } else {
        setResult(`❌ Direct login failed: ${data.message}`);
      }
      
    } catch (error) {
      console.error('❌ Direct login error:', error);
      setResult(`❌ Direct login error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">Login Test</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              value={credentials.email}
              onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              type="password"
              value={credentials.password}
              onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="space-y-2">
            <button
              onClick={handleLogin}
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Logging in...' : 'Test Redux Login'}
            </button>
            
            <button
              onClick={testDirectLogin}
              disabled={loading}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              Test Direct API Login
            </button>
            
            <button
              onClick={testBackendHealth}
              className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
            >
              Test Backend Health
            </button>
          </div>
          
          {result && (
            <div className="mt-4 p-3 bg-gray-100 rounded-md">
              <pre className="text-sm whitespace-pre-wrap">{result}</pre>
            </div>
          )}
        </div>
        
        <div className="mt-6 text-center text-sm text-gray-600">
          <p>Default credentials:</p>
          <p>Email: <EMAIL></p>
          <p>Password: AdminDemo2025!</p>
        </div>
      </div>
    </div>
  );
};

export default TestLogin; 