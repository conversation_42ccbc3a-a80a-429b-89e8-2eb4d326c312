/**
 * Guardian AI Client - Frontend Monitoring Implementation
 * FamEduConnect Production Monitoring System
 * 
 * @version 1.0.0
 * @copyright 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc.
 */

class GuardianAIClient {
  /**
   * Initialize Guardian AI client
   * @param {Object} config - Configuration options
   * @param {string} config.apiKey - Guardian AI API key
   * @param {string} config.appId - Application identifier
   * @param {string} config.environment - Environment (production, staging, development)
   * @param {string} config.userId - Current user ID (optional)
   * @param {string} config.sessionId - Current session ID (optional)
   * @param {string} config.version - Application version
   */
  constructor(config) {
    this.config = {
      apiKey: config.apiKey,
      appId: config.appId || 'fameduconnect-frontend',
      environment: config.environment || 'production',
      userId: config.userId || null,
      sessionId: config.sessionId || this._generateSessionId(),
      version: config.version || '1.0.0',
      endpoint: config.endpoint || 'https://api.guardian-ai.fameduconnect.xyz/v1',
      sampleRate: config.sampleRate || 1.0, // 100% by default
      autoTrack: config.autoTrack !== false,
      consoleCapture: config.consoleCapture !== false,
      errorCapture: config.errorCapture !== false,
      performanceCapture: config.performanceCapture !== false,
      networkCapture: config.networkCapture !== false,
      maxBatchSize: config.maxBatchSize || 10,
      maxQueueSize: config.maxQueueSize || 100,
      flushInterval: config.flushInterval || 5000, // 5 seconds
    };

    this.queue = [];
    this.flushTimer = null;
    this.initialized = false;
    this.metrics = {};
    
    // Performance metrics
    this.performanceMetrics = {
      pageLoadTime: 0,
      domContentLoaded: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0,
    };
    
    // Network request tracking
    this.pendingNetworkRequests = new Map();
    this.networkMetrics = {
      totalRequests: 0,
      failedRequests: 0,
      totalTransferSize: 0,
      averageResponseTime: 0,
    };
  }

  /**
   * Initialize the monitoring system
   */
  initialize() {
    if (this.initialized) {
      console.warn('Guardian AI: Already initialized');
      return;
    }

    // Check if we should sample this session
    if (Math.random() > this.config.sampleRate) {
      console.log('Guardian AI: Session not sampled');
      return;
    }

    this._setupErrorHandling();
    this._setupPerformanceMonitoring();
    this._setupNetworkMonitoring();
    this._setupConsoleCapture();
    this._startFlushTimer();

    // Send initialization event
    this.trackEvent('guardian_ai_initialized', {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      screenSize: `${window.screen.width}x${window.screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      language: navigator.language,
      referrer: document.referrer,
      url: window.location.href,
    });

    this.initialized = true;
    console.log(`Guardian AI: Initialized (${this.config.environment})`);
  }

  /**
   * Set user information
   * @param {string} userId - User identifier
   * @param {Object} userProperties - Additional user properties
   */
  setUser(userId, userProperties = {}) {
    this.config.userId = userId;
    
    this.trackEvent('guardian_ai_user_identified', {
      userId,
      ...userProperties,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Track custom event
   * @param {string} eventName - Event name
   * @param {Object} properties - Event properties
   */
  trackEvent(eventName, properties = {}) {
    if (!this.initialized && eventName !== 'guardian_ai_initialized') {
      console.warn(`Guardian AI: Not initialized, event '${eventName}' not tracked`);
      return;
    }

    const event = {
      type: 'event',
      name: eventName,
      appId: this.config.appId,
      environment: this.config.environment,
      version: this.config.version,
      sessionId: this.config.sessionId,
      userId: this.config.userId,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      properties: properties,
    };

    this._addToQueue(event);
  }

  /**
   * Track page view
   * @param {string} pageName - Page name
   * @param {Object} properties - Additional properties
   */
  trackPageView(pageName, properties = {}) {
    const pageViewProperties = {
      pageName: pageName || document.title,
      url: window.location.href,
      referrer: document.referrer,
      ...properties,
    };

    this.trackEvent('page_view', pageViewProperties);
    
    // Reset page-specific performance metrics
    this.performanceMetrics = {
      pageLoadTime: 0,
      domContentLoaded: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0,
    };
    
    // Capture performance metrics for this page
    this._capturePerformanceMetrics();
  }

  /**
   * Track error
   * @param {Error|string} error - Error object or message
   * @param {Object} properties - Additional properties
   */
  trackError(error, properties = {}) {
    const errorProperties = {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : null,
      ...properties,
    };

    this.trackEvent('error', errorProperties);
  }

  /**
   * Track performance metric
   * @param {string} metricName - Metric name
   * @param {number} value - Metric value
   * @param {Object} properties - Additional properties
   */
  trackPerformance(metricName, value, properties = {}) {
    const performanceProperties = {
      metricName,
      value,
      ...properties,
    };

    this.trackEvent('performance', performanceProperties);
    
    // Update stored metrics
    this.performanceMetrics[metricName] = value;
  }

  /**
   * Track network request
   * @param {string} url - Request URL
   * @param {Object} details - Request details
   */
  trackNetworkRequest(url, details = {}) {
    const networkProperties = {
      url,
      method: details.method || 'GET',
      status: details.status,
      duration: details.duration,
      size: details.size,
      type: details.type || 'xhr',
      success: details.success !== false,
      ...details,
    };

    this.trackEvent('network_request', networkProperties);
    
    // Update network metrics
    this.networkMetrics.totalRequests++;
    if (!details.success) {
      this.networkMetrics.failedRequests++;
    }
    if (details.size) {
      this.networkMetrics.totalTransferSize += details.size;
    }
    if (details.duration) {
      const totalTime = this.networkMetrics.averageResponseTime * (this.networkMetrics.totalRequests - 1);
      this.networkMetrics.averageResponseTime = (totalTime + details.duration) / this.networkMetrics.totalRequests;
    }
  }

  /**
   * Manually flush the event queue
   * @returns {Promise} Promise that resolves when flush is complete
   */
  flush() {
    if (this.queue.length === 0) {
      return Promise.resolve();
    }

    const events = [...this.queue];
    this.queue = [];

    return this._sendEvents(events)
      .then(() => {
        console.log(`Guardian AI: Flushed ${events.length} events`);
      })
      .catch(error => {
        console.error('Guardian AI: Error flushing events', error);
        // Put events back in queue for retry
        this.queue = [...events, ...this.queue].slice(0, this.config.maxQueueSize);
      });
  }

  /**
   * Generate a unique session ID
   * @private
   * @returns {string} Session ID
   */
  _generateSessionId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Add event to queue
   * @private
   * @param {Object} event - Event to add
   */
  _addToQueue(event) {
    this.queue.push(event);
    
    // Flush if queue is full
    if (this.queue.length >= this.config.maxBatchSize) {
      this.flush();
    }
  }

  /**
   * Send events to Guardian AI API
   * @private
   * @param {Array} events - Events to send
   * @returns {Promise} Promise that resolves when events are sent
   */
  _sendEvents(events) {
    return new Promise((resolve, reject) => {
      // In a real implementation, this would send data to the Guardian AI API
      // For this implementation, we'll simulate a successful API call
      
      console.log(`Guardian AI: Sending ${events.length} events to ${this.config.endpoint}`);
      
      // Simulate API call
      setTimeout(() => {
        // Simulate 95% success rate
        if (Math.random() < 0.95) {
          resolve();
        } else {
          reject(new Error('Simulated API error'));
        }
      }, 100);
    });
  }

  /**
   * Start the flush timer
   * @private
   */
  _startFlushTimer() {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }

  /**
   * Set up error handling
   * @private
   */
  _setupErrorHandling() {
    if (!this.config.errorCapture) {
      return;
    }

    // Capture unhandled errors
    window.addEventListener('error', (event) => {
      this.trackError(event.error || event.message, {
        source: event.filename,
        line: event.lineno,
        column: event.colno,
        type: 'unhandled_error',
      });
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
      this.trackError(error, {
        type: 'unhandled_promise_rejection',
      });
    });
  }

  /**
   * Set up performance monitoring
   * @private
   */
  _setupPerformanceMonitoring() {
    if (!this.config.performanceCapture || !window.PerformanceObserver) {
      return;
    }

    // Capture page load metrics
    if (window.performance && window.performance.timing) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const timing = window.performance.timing;
          const pageLoadTime = timing.loadEventEnd - timing.navigationStart;
          const domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;
          
          this.trackPerformance('pageLoadTime', pageLoadTime);
          this.trackPerformance('domContentLoaded', domContentLoaded);
        }, 0);
      });
    }

    // Capture Web Vitals
    try {
      // First Contentful Paint
      const fcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        if (entries.length > 0) {
          const fcp = entries[0];
          this.trackPerformance('firstContentfulPaint', fcp.startTime);
        }
      });
      fcpObserver.observe({ type: 'paint', buffered: true });

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        if (entries.length > 0) {
          const lcp = entries[entries.length - 1];
          this.trackPerformance('largestContentfulPaint', lcp.startTime);
        }
      });
      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });

      // First Input Delay
      const fidObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        if (entries.length > 0) {
          const fid = entries[0];
          this.trackPerformance('firstInputDelay', fid.processingStart - fid.startTime);
        }
      });
      fidObserver.observe({ type: 'first-input', buffered: true });

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((entryList) => {
        let clsValue = 0;
        for (const entry of entryList.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        this.trackPerformance('cumulativeLayoutShift', clsValue);
      });
      clsObserver.observe({ type: 'layout-shift', buffered: true });
    } catch (e) {
      console.error('Guardian AI: Error setting up performance observers', e);
    }
  }

  /**
   * Capture current performance metrics
   * @private
   */
  _capturePerformanceMetrics() {
    if (!this.config.performanceCapture || !window.performance) {
      return;
    }

    // Capture navigation timing
    if (window.performance.getEntriesByType) {
      const navigationEntries = window.performance.getEntriesByType('navigation');
      if (navigationEntries.length > 0) {
        const nav = navigationEntries[0];
        this.trackPerformance('navigationTime', nav.duration);
        this.trackPerformance('dnsTime', nav.domainLookupEnd - nav.domainLookupStart);
        this.trackPerformance('connectTime', nav.connectEnd - nav.connectStart);
        this.trackPerformance('requestTime', nav.responseStart - nav.requestStart);
        this.trackPerformance('responseTime', nav.responseEnd - nav.responseStart);
        this.trackPerformance('domProcessingTime', nav.domComplete - nav.responseEnd);
        this.trackPerformance('loadEventTime', nav.loadEventEnd - nav.loadEventStart);
      }
    }

    // Capture resource timing
    if (window.performance.getEntriesByType) {
      const resourceEntries = window.performance.getEntriesByType('resource');
      const resourceStats = {
        total: resourceEntries.length,
        totalSize: 0,
        totalDuration: 0,
        byType: {},
      };

      resourceEntries.forEach(resource => {
        const type = resource.initiatorType || 'other';
        if (!resourceStats.byType[type]) {
          resourceStats.byType[type] = {
            count: 0,
            size: 0,
            duration: 0,
          };
        }
        
        resourceStats.byType[type].count++;
        resourceStats.byType[type].duration += resource.duration;
        
        if (resource.transferSize) {
          resourceStats.totalSize += resource.transferSize;
          resourceStats.byType[type].size += resource.transferSize;
        }
        
        resourceStats.totalDuration += resource.duration;
      });

      this.trackEvent('resource_timing', resourceStats);
    }

    // Capture memory info if available
    if (window.performance.memory) {
      this.trackPerformance('jsHeapSizeLimit', window.performance.memory.jsHeapSizeLimit);
      this.trackPerformance('totalJSHeapSize', window.performance.memory.totalJSHeapSize);
      this.trackPerformance('usedJSHeapSize', window.performance.memory.usedJSHeapSize);
    }
  }

  /**
   * Set up network monitoring
   * @private
   */
  _setupNetworkMonitoring() {
    if (!this.config.networkCapture) {
      return;
    }

    // Monitor fetch requests
    if (window.fetch) {
      const originalFetch = window.fetch;
      window.fetch = async (...args) => {
        const startTime = performance.now();
        const url = typeof args[0] === 'string' ? args[0] : args[0].url;
        const method = args[1]?.method || 'GET';
        
        try {
          const response = await originalFetch.apply(window, args);
          const endTime = performance.now();
          const duration = endTime - startTime;
          const size = response.headers.get('content-length') ? parseInt(response.headers.get('content-length'), 10) : 0;
          
          this.trackNetworkRequest(url, {
            method,
            status: response.status,
            duration,
            size,
            type: 'fetch',
            success: response.ok,
          });
          
          return response;
        } catch (error) {
          const endTime = performance.now();
          const duration = endTime - startTime;
          
          this.trackNetworkRequest(url, {
            method,
            status: 0,
            duration,
            size: 0,
            type: 'fetch',
            success: false,
            error: error.message,
          });
          
          throw error;
        }
      };
    }

    // Monitor XMLHttpRequest
    if (window.XMLHttpRequest) {
      const originalOpen = XMLHttpRequest.prototype.open;
      const originalSend = XMLHttpRequest.prototype.send;
      
      XMLHttpRequest.prototype.open = function(method, url) {
        this._guardianAI = {
          method,
          url,
          startTime: 0,
        };
        return originalOpen.apply(this, arguments);
      };
      
      XMLHttpRequest.prototype.send = function() {
        if (this._guardianAI) {
          this._guardianAI.startTime = performance.now();
          
          const originalOnReadyStateChange = this.onreadystatechange;
          this.onreadystatechange = function() {
            if (this.readyState === 4) {
              const endTime = performance.now();
              const duration = endTime - this._guardianAI.startTime;
              const size = this.getResponseHeader('content-length') ? parseInt(this.getResponseHeader('content-length'), 10) : 0;
              
              window.guardianAIClient.trackNetworkRequest(this._guardianAI.url, {
                method: this._guardianAI.method,
                status: this.status,
                duration,
                size,
                type: 'xhr',
                success: this.status >= 200 && this.status < 300,
              });
            }
            
            if (originalOnReadyStateChange) {
              originalOnReadyStateChange.apply(this, arguments);
            }
          };
        }
        
        return originalSend.apply(this, arguments);
      };
    }
  }

  /**
   * Set up console capture
   * @private
   */
  _setupConsoleCapture() {
    if (!this.config.consoleCapture) {
      return;
    }

    const methods = ['error', 'warn'];
    methods.forEach(method => {
      const originalMethod = console[method];
      console[method] = (...args) => {
        // Track console errors and warnings
        this.trackEvent('console', {
          level: method,
          message: args.map(arg => {
            if (arg instanceof Error) {
              return `${arg.name}: ${arg.message}\n${arg.stack}`;
            } else if (typeof arg === 'object') {
              try {
                return JSON.stringify(arg);
              } catch (e) {
                return String(arg);
              }
            } else {
              return String(arg);
            }
          }).join(' '),
        });
        
        // Call original method
        originalMethod.apply(console, args);
      };
    });
  }
}

// Create global instance
window.guardianAIClient = new GuardianAIClient({
  apiKey: window.ENV?.GUARDIAN_AI_KEY || 'demo-key',
  appId: 'fameduconnect-frontend',
  environment: window.ENV?.NODE_ENV || 'production',
  version: window.ENV?.APP_VERSION || '1.0.0',
});

// Auto-initialize
if (document.readyState === 'complete') {
  window.guardianAIClient.initialize();
} else {
  window.addEventListener('load', () => {
    window.guardianAIClient.initialize();
  });
}

export default window.guardianAIClient;