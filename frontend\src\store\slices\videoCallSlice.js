import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { videoCallAPI } from '../../services/api';
import { toast } from 'react-hot-toast';

// WebRTC configuration
const rtcConfiguration = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
  ]
};

// Async thunks
export const createCall = createAsyncThunk(
  'videoCall/createCall',
  async (callData, { rejectWithValue }) => {
    try {
      const response = await videoCallAPI.createCall(callData);
      toast.success('Video call created successfully!');
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to create call';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const joinCall = createAsyncThunk(
  'videoCall/joinCall',
  async (callId, { rejectWithValue, getState, dispatch }) => {
    try {
      // Get call details
      const callResponse = await videoCallAPI.getCall(callId);
      const call = callResponse.data;

      // Join the call
      const joinResponse = await videoCallAPI.joinCall(callId);
      
      // Initialize WebRTC
      dispatch(initializeWebRTC());
      
      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      dispatch(setLocalStream(stream));
      
      // Emit join event via socket
      const { socket } = getState().socket;
      if (socket) {
        socket.emit('join_video_call', { callId });
      }

      return { call, joinToken: joinResponse.data.joinToken };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to join call';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const leaveCall = createAsyncThunk(
  'videoCall/leaveCall',
  async (_, { getState, dispatch }) => {
    try {
      const { currentCall, localStream, peerConnections } = getState().videoCall;
      
      if (currentCall) {
        await videoCallAPI.leaveCall(currentCall.callId);
      }

      // Close peer connections
      Object.values(peerConnections).forEach(pc => {
        pc.close();
      });

      // Stop local stream
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
      }

      // Emit leave event via socket
      const { socket } = getState().socket;
      if (socket && currentCall) {
        socket.emit('leave_video_call');
      }

      dispatch(cleanup());
      
      return true;
    } catch (error) {
      console.error('Leave call error:', error);
      return true; // Always succeed to ensure cleanup
    }
  }
);

export const endCall = createAsyncThunk(
  'videoCall/endCall',
  async (callData, { getState, rejectWithValue }) => {
    try {
      const { currentCall } = getState().videoCall;
      
      if (!currentCall) {
        return rejectWithValue('No active call');
      }

      await videoCallAPI.endCall(currentCall.callId, callData);
      toast.success('Call ended successfully');
      
      return true;
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to end call';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

const initialState = {
  currentCall: null,
  participants: [],
  localStream: null,
  remoteStreams: {},
  peerConnections: {},
  isConnected: false,
  isMuted: false,
  isVideoOff: false,
  isScreenSharing: false,
  chatMessages: [],
  loading: false,
  error: null,
  connectionQuality: 'good',
  callSettings: {
    audioEnabled: true,
    videoEnabled: true,
    screenShareEnabled: true,
    chatEnabled: true,
    recordingEnabled: false
  }
};

const videoCallSlice = createSlice({
  name: 'videoCall',
  initialState,
  reducers: {
    setLocalStream: (state, action) => {
      state.localStream = action.payload;
    },
    addParticipant: (state, action) => {
      const participant = action.payload;
      const existingIndex = state.participants.findIndex(p => p.userId === participant.userId);
      
      if (existingIndex === -1) {
        state.participants.push(participant);
      } else {
        state.participants[existingIndex] = { ...state.participants[existingIndex], ...participant };
      }
    },
    removeParticipant: (state, action) => {
      const userId = action.payload;
      state.participants = state.participants.filter(p => p.userId !== userId);
      
      // Clean up remote stream and peer connection
      delete state.remoteStreams[userId];
      if (state.peerConnections[userId]) {
        state.peerConnections[userId].close();
        delete state.peerConnections[userId];
      }
    },
    setRemoteStream: (state, action) => {
      const { userId, stream } = action.payload;
      state.remoteStreams[userId] = stream;
    },
    setPeerConnection: (state, action) => {
      const { userId, peerConnection } = action.payload;
      state.peerConnections[userId] = peerConnection;
    },
    toggleMute: (state) => {
      state.isMuted = !state.isMuted;
      
      // Mute/unmute local stream
      if (state.localStream) {
        state.localStream.getAudioTracks().forEach(track => {
          track.enabled = !state.isMuted;
        });
      }
    },
    toggleVideo: (state) => {
      state.isVideoOff = !state.isVideoOff;
      
      // Enable/disable video track
      if (state.localStream) {
        state.localStream.getVideoTracks().forEach(track => {
          track.enabled = !state.isVideoOff;
        });
      }
    },
    toggleScreenShare: (state) => {
      state.isScreenSharing = !state.isScreenSharing;
    },
    addChatMessage: (state, action) => {
      state.chatMessages.push(action.payload);
    },
    setConnectionQuality: (state, action) => {
      state.connectionQuality = action.payload;
    },
    updateCallSettings: (state, action) => {
      state.callSettings = { ...state.callSettings, ...action.payload };
    },
    initializeWebRTC: (state) => {
      // WebRTC initialization logic will be handled in middleware
    },
    receiveOffer: (state, action) => {
      // Handle WebRTC offer
    },
    receiveAnswer: (state, action) => {
      // Handle WebRTC answer
    },
    receiveIceCandidate: (state, action) => {
      // Handle ICE candidate
    },
    callStarted: (state, action) => {
      state.isConnected = true;
    },
    callEnded: (state, action) => {
      state.isConnected = false;
    },
    userJoined: (state, action) => {
      const { userId, callId } = action.payload;
      // Handle user joining
    },
    userLeft: (state, action) => {
      const { userId } = action.payload;
      state.participants = state.participants.filter(p => p.userId !== userId);
    },
    screenShareStarted: (state, action) => {
      const { userId } = action.payload;
      const participant = state.participants.find(p => p.userId === userId);
      if (participant) {
        participant.isScreenSharing = true;
      }
    },
    screenShareStopped: (state, action) => {
      const { userId } = action.payload;
      const participant = state.participants.find(p => p.userId === userId);
      if (participant) {
        participant.isScreenSharing = false;
      }
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    cleanup: (state) => {
      state.currentCall = null;
      state.participants = [];
      state.localStream = null;
      state.remoteStreams = {};
      state.peerConnections = {};
      state.isConnected = false;
      state.isMuted = false;
      state.isVideoOff = false;
      state.isScreenSharing = false;
      state.chatMessages = [];
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Create Call
      .addCase(createCall.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createCall.fulfilled, (state, action) => {
        state.loading = false;
        state.currentCall = action.payload;
      })
      .addCase(createCall.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Join Call
      .addCase(joinCall.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(joinCall.fulfilled, (state, action) => {
        state.loading = false;
        state.currentCall = action.payload.call;
        state.isConnected = true;
      })
      .addCase(joinCall.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Leave Call
      .addCase(leaveCall.fulfilled, (state) => {
        state.currentCall = null;
        state.participants = [];
        state.isConnected = false;
        state.localStream = null;
        state.remoteStreams = {};
        state.peerConnections = {};
        state.chatMessages = [];
      })
      
      // End Call
      .addCase(endCall.fulfilled, (state) => {
        state.currentCall = null;
        state.participants = [];
        state.isConnected = false;
      });
  }
});

export const {
  setLocalStream,
  addParticipant,
  removeParticipant,
  setRemoteStream,
  setPeerConnection,
  toggleMute,
  toggleVideo,
  toggleScreenShare,
  addChatMessage,
  setConnectionQuality,
  updateCallSettings,
  initializeWebRTC,
  receiveOffer,
  receiveAnswer,
  receiveIceCandidate,
  callStarted,
  callEnded,
  userJoined,
  userLeft,
  screenShareStarted,
  screenShareStopped,
  setError,
  clearError,
  cleanup
} = videoCallSlice.actions;

export default videoCallSlice.reducer;