import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { checkAuth } from '../../store/slices/authSlice';

// Layout Components
import Layout from './Layout';
import Sidebar from './Sidebar';
import Header from './Header';

// Auth Pages
import Login from '../../pages/Auth/Login';
import Register from '../../pages/Auth/Register';
import ForgotPassword from '../../pages/Auth/ForgotPassword';
import ResetPassword from '../../pages/Auth/ResetPassword';

// Main Pages
import Dashboard from '../../pages/Dashboard/Dashboard';
import ParentDashboard from '../../pages/Dashboard/ParentDashboard';
import TeacherDashboard from '../../pages/Dashboard/TeacherDashboard';
import StudentDashboard from '../../pages/Dashboard/StudentDashboard';
import Messages from '../../pages/Messages/Messages';
import VideoCall from '../../pages/VideoCall/VideoCall';
import Students from '../../pages/Students/Students';
import Classes from '../../pages/Classes/Classes';
import Profile from '../../pages/Profile/Profile';
import Settings from '../../pages/Settings/Settings';
import Onboarding from '../../pages/Onboarding/Onboarding';

// Admin Pages
import AdminDashboard from '../../pages/Admin/AdminDashboard';
import AdminUsers from '../../pages/Admin/AdminUsers';
import AdminClasses from '../../pages/Admin/AdminClasses';
import AdminReports from '../../pages/Admin/AdminReports';
import AdminSettings from '../../pages/Admin/AdminSettings';

// Test Page
import TestPage from '../../pages/TestPage/TestPage';

// Protected Route Component
const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const { isAuthenticated, user, loading } = useSelector(state => state.auth);
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  if (allowedRoles.length > 0 && !allowedRoles.includes(user?.role)) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return children;
};

// Admin Route Component
const AdminRoute = ({ children }) => {
  return (
    <ProtectedRoute allowedRoles={['admin', 'super_admin']}>
      {children}
    </ProtectedRoute>
  );
};

const AppContent = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { isAuthenticated, user, hasCompletedOnboarding } = useSelector(state => state.auth);

  useEffect(() => {
    // Check authentication status on app load
    dispatch(checkAuth());
  }, [dispatch]);

  // Show onboarding if user hasn't completed it
  if (isAuthenticated && !hasCompletedOnboarding) {
    return <Onboarding />;
  }

  // Redirect to role-based dashboard if user is on generic dashboard
  if (isAuthenticated && user?.role && location?.pathname === '/dashboard') {
    const roleDashboard = user.role === 'admin' ? '/admin' : 
                         user.role === 'teacher' ? '/dashboard/teacher' :
                         user.role === 'student' ? '/dashboard/student' : '/dashboard/parent';
    return <Navigate to={roleDashboard} replace />;
  }

  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={
        isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />
      } />
      <Route path="/register" element={
        isAuthenticated ? <Navigate to="/dashboard" replace /> : <Register />
      } />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/reset-password/:token" element={<ResetPassword />} />
      
      {/* Test Route */}
      <Route path="/test" element={<TestPage />} />
      
      {/* Onboarding Route */}
      <Route path="/onboarding" element={
        <ProtectedRoute>
          <Onboarding />
        </ProtectedRoute>
      } />
      
      {/* Protected Routes */}
      <Route path="/dashboard" element={
        <ProtectedRoute>
          <Layout>
            <Dashboard />
          </Layout>
        </ProtectedRoute>
      } />
      
      {/* Role-based Dashboard Routes */}
      <Route path="/dashboard/parent" element={
        <ProtectedRoute>
          <Layout>
            <ParentDashboard />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/dashboard/teacher" element={
        <ProtectedRoute>
          <Layout>
            <TeacherDashboard />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/dashboard/student" element={
        <ProtectedRoute>
          <Layout>
            <StudentDashboard />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/messages" element={
        <ProtectedRoute>
          <Layout>
            <Messages />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/video-call/:callId?" element={
        <ProtectedRoute>
          <Layout>
            <VideoCall />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/students" element={
        <ProtectedRoute>
          <Layout>
            <Students />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/classes" element={
        <ProtectedRoute>
          <Layout>
            <Classes />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/profile" element={
        <ProtectedRoute>
          <Layout>
            <Profile />
          </Layout>
        </ProtectedRoute>
      } />
      
      <Route path="/settings" element={
        <ProtectedRoute>
          <Layout>
            <Settings />
          </Layout>
        </ProtectedRoute>
      } />
      
      {/* Admin Routes */}
      <Route path="/admin" element={
        <AdminRoute>
          <Layout>
            <AdminDashboard />
          </Layout>
        </AdminRoute>
      } />
      
      <Route path="/admin/users" element={
        <AdminRoute>
          <Layout>
            <AdminUsers />
          </Layout>
        </AdminRoute>
      } />
      
      <Route path="/admin/classes" element={
        <AdminRoute>
          <Layout>
            <AdminClasses />
          </Layout>
        </AdminRoute>
      } />
      
      <Route path="/admin/reports" element={
        <AdminRoute>
          <Layout>
            <AdminReports />
          </Layout>
        </AdminRoute>
      } />
      
      <Route path="/admin/settings" element={
        <AdminRoute>
          <Layout>
            <AdminSettings />
          </Layout>
        </AdminRoute>
      } />
      
      {/* Default Route */}
      <Route path="/" element={
        isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />
      } />
      
      {/* Catch all route */}
      <Route path="*" element={
        isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />
      } />
    </Routes>
  );
};

export default AppContent; 