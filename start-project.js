const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting FamEduConnect Project');
console.log('===============================');

// Function to run a command in a specific directory
const runCommand = (command, dir = __dirname) => {
  try {
    console.log(`Running: ${command} in ${dir}`);
    execSync(command, { 
      cwd: dir, 
      stdio: 'inherit',
      shell: true
    });
    return true;
  } catch (error) {
    console.error(`Failed to execute ${command}`);
    return false;
  }
};

// Start backend
console.log('\n📡 Starting Backend Server...');
const backendProcess = runCommand('start cmd.exe /k "cd backend && node server.js"');
if (backendProcess) {
  console.log('✅ Backend server started');
} else {
  console.error('❌ Failed to start backend server');
}

// Start frontend
console.log('\n🖥️ Starting Frontend...');
const frontendProcess = runCommand('start cmd.exe /k "cd frontend && npm start"');
if (frontendProcess) {
  console.log('✅ Frontend started');
} else {
  console.error('❌ Failed to start frontend');
}

// Start admin
console.log('\n👨‍💼 Starting Admin Dashboard...');
const adminProcess = runCommand('start cmd.exe /k "cd admin && npm start"');
if (adminProcess) {
  console.log('✅ Admin dashboard started');
} else {
  console.error('❌ Failed to start admin dashboard');
}

console.log('\n🎉 FamEduConnect is now running!');
console.log('- Backend: http://localhost:8080');
console.log('- Frontend: http://localhost:3000');
console.log('- Admin: http://localhost:3001');
console.log('\nPress Ctrl+C in each terminal window to stop the servers');