apiVersion: v1
kind: Namespace
metadata:
  name: fameduconnect-cache
  labels:
    name: fameduconnect-cache
    environment: production
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: fameduconnect-cache
data:
  redis.conf: |
    # Redis configuration for production
    bind 0.0.0.0
    port 6379
    timeout 300
    tcp-keepalive 60
    loglevel notice
    logfile ""
    databases 16
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir /data
    maxmemory 2gb
    maxmemory-policy allkeys-lru
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    aof-load-truncated yes
    aof-use-rdb-preamble yes
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    latency-monitor-threshold 100
    notify-keyspace-events ""
    hash-max-ziplist-entries 512
    hash-max-ziplist-value 64
    list-max-ziplist-size -2
    list-compress-depth 0
    set-max-intset-entries 512
    zset-max-ziplist-entries 128
    zset-max-ziplist-value 64
    hll-sparse-max-bytes 3000
    stream-node-max-bytes 4096
    stream-node-max-entries 100
    activerehashing yes
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
    hz 10
    dynamic-hz yes
    aof-rewrite-incremental-fsync yes
    rdb-save-incremental-fsync yes
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: fameduconnect-redis
  namespace: fameduconnect-cache
  labels:
    app: fameduconnect-redis
    version: v1.0.0
spec:
  serviceName: fameduconnect-redis
  replicas: 3
  selector:
    matchLabels:
      app: fameduconnect-redis
  template:
    metadata:
      labels:
        app: fameduconnect-redis
        version: v1.0.0
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - /etc/redis/redis.conf
        volumeMounts:
        - name: redis-config
          mountPath: /etc/redis
        - name: redis-data
          mountPath: /data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 999
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: fameduconnect-redis
  namespace: fameduconnect-cache
  labels:
    app: fameduconnect-redis
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: fameduconnect-redis
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: fameduconnect-redis-pdb
  namespace: fameduconnect-cache
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: fameduconnect-redis 