const http = require('http');

console.log('🧪 Testing FamEduConnect Backend Connection...');

// Test the backend connection
const testBackend = () => {
  return new Promise((resolve, reject) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3001,
      path: '/api/test',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('✅ Backend is working!');
          console.log('📊 Response:', response);
          resolve(response);
        } catch (e) {
          console.log('❌ Invalid JSON response:', data);
          reject(e);
        }
      });
    });
    
    req.on('error', (e) => {
      console.log('❌ Backend connection failed:', e.message);
      reject(e);
    });
    
    req.on('timeout', () => {
      console.log('❌ Backend connection timeout');
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.end();
  });
};

// Test login endpoint
const testLogin = () => {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const req = http.request({
      hostname: 'localhost',
      port: 3001,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 5000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('✅ Login endpoint working!');
          console.log('🔑 Token received:', response.token ? 'Yes' : 'No');
          resolve(response);
        } catch (e) {
          console.log('❌ Invalid login response:', data);
          reject(e);
        }
      });
    });
    
    req.on('error', (e) => {
      console.log('❌ Login request failed:', e.message);
      reject(e);
    });
    
    req.on('timeout', () => {
      console.log('❌ Login request timeout');
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.write(postData);
    req.end();
  });
};

// Run tests
const runTests = async () => {
  try {
    console.log('🚀 Starting connection tests...\n');
    
    // Test 1: Backend connection
    await testBackend();
    
    // Test 2: Login endpoint
    await testLogin();
    
    console.log('\n🎉 All tests passed!');
    console.log('✅ Backend is running and responding');
    console.log('✅ Frontend should be able to connect');
    console.log('\n🌐 Frontend URL: http://localhost:3000');
    console.log('📡 Backend URL: http://localhost:3001');
    console.log('\n🧪 Test credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    
  } catch (error) {
    console.log('\n❌ Tests failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the backend server is running on port 3001');
    console.log('2. Check if port 3001 is not blocked by firewall');
    console.log('3. Try running: node simple-server.js in the backend directory');
  }
};

runTests(); 