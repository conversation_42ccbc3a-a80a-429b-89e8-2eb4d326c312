import { IsEmail, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum UserRole {
  ADMIN = 'ADMIN',
  TEACHER = 'TEACHER',
  PARENT = 'PARENT',
  STUDENT = 'STUDENT',
}

export class RegisterDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    type: String
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password (minimum 6 characters)',
    example: 'password123',
    type: String,
    minLength: 6
  })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
    type: String
  })
  @IsString()
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
    type: String
  })
  @IsString()
  lastName: string;

  @ApiProperty({
    description: 'User role',
    example: UserRole.PARENT,
    enum: UserRole,
    default: UserRole.PARENT,
    required: false
  })
  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole = UserRole.PARENT;
} 