<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FamEduConnect - Connecting Families & Education Worldwide</title>
    <meta name="description" content="Secure, multilingual platform connecting families and educators with real-time communication, video calls, and educational management tools.">
    <link rel="canonical" href="https://fameduconnect.com">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Open Graph -->
    <meta property="og:title" content="FamEduConnect - Family Education Platform">
    <meta property="og:description" content="Secure platform connecting families and educators worldwide">
    <meta property="og:url" content="https://fameduconnect.com">
    <meta property="og:type" content="website">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #48bb78;
            --text-dark: #2d3748;
            --text-light: #718096;
            --bg-light: #f7fafc;
            --white: #ffffff;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: var(--text-dark);
            overflow-x: hidden;
        }
        
        /* RTL Support */
        [dir="rtl"] { text-align: right; }
        [dir="rtl"] .hero-content { align-items: flex-end; }
        
        /* Language Toggle */
        .language-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 8px 16px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .language-toggle select {
            border: none;
            background: transparent;
            font-weight: 600;
            color: var(--primary-color);
            cursor: pointer;
        }
        
        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }
        
        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero p {
            font-size: clamp(1.1rem, 2vw, 1.4rem);
            margin-bottom: 40px;
            opacity: 0.95;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 60px;
        }
        
        .cta-btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: var(--white);
            color: var(--primary-color);
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .cta-btn.secondary {
            background: transparent;
            color: var(--white);
            border: 2px solid var(--white);
        }
        
        .cta-btn.secondary:hover {
            background: var(--white);
            color: var(--primary-color);
        }
        
        /* Features Section */
        .features {
            padding: 100px 0;
            background: var(--bg-light);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .section-title {
            text-align: center;
            font-size: clamp(2rem, 4vw, 3rem);
            margin-bottom: 20px;
            color: var(--text-dark);
        }
        
        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: var(--text-light);
            margin-bottom: 60px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }
        
        .feature-card {
            background: var(--white);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--text-dark);
        }
        
        .feature-card p {
            color: var(--text-light);
            line-height: 1.6;
        }
        
        /* Security Section */
        .security {
            padding: 100px 0;
            background: var(--white);
        }
        
        .security-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        
        .security-badge {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: var(--bg-light);
            border-radius: 15px;
            border-left: 4px solid var(--accent-color);
        }
        
        .security-badge .icon {
            font-size: 2rem;
            color: var(--accent-color);
        }
        
        .security-badge .text {
            flex: 1;
        }
        
        .security-badge h4 {
            font-size: 1.1rem;
            margin-bottom: 5px;
            color: var(--text-dark);
        }
        
        .security-badge p {
            font-size: 0.9rem;
            color: var(--text-light);
        }
        
        /* Footer */
        .footer {
            background: var(--text-dark);
            color: var(--white);
            padding: 60px 0 30px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .footer-section h3 {
            margin-bottom: 20px;
            color: var(--white);
        }
        
        .footer-section p, .footer-section a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            line-height: 1.8;
        }
        
        .footer-section a:hover {
            color: var(--primary-color);
        }
        
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 30px;
            text-align: center;
            color: rgba(255,255,255,0.6);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .cta-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .language-toggle {
                top: 10px;
                right: 10px;
            }
        }
        
        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
        
        /* High contrast mode */
        @media (prefers-contrast: high) {
            .hero {
                background: #000;
                color: #fff;
            }
            
            .feature-card {
                border: 2px solid #000;
            }
        }
    </style>
</head>
<body>
    <!-- Language Toggle -->
    <div class="language-toggle">
        <select id="languageSelect" onchange="changeLanguage()" aria-label="Select Language">
            <option value="en">🇺🇸 English</option>
            <option value="ar">🇸🇦 العربية</option>
            <option value="es">🇪🇸 Español</option>
            <option value="fr">🇫🇷 Français</option>
            <option value="de">🇩🇪 Deutsch</option>
            <option value="zh">🇨🇳 中文</option>
        </select>
    </div>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1 data-translate="hero.title">🎓 FamEduConnect</h1>
            <p data-translate="hero.subtitle">Secure, multilingual platform connecting families and educators worldwide with real-time communication, video calls, and educational management tools.</p>
            
            <div class="cta-buttons">
                <a href="https://fameduconnect.app" class="cta-btn" data-translate="hero.cta.primary">
                    🚀 Launch App
                </a>
                <a href="#features" class="cta-btn secondary" data-translate="hero.cta.demo">
                    🎥 View Features
                </a>
                <a href="#security" class="cta-btn secondary" data-translate="hero.cta.pricing">
                    🛡️ Security Info
                </a>
            </div>
            
            <div style="margin-top: 40px; opacity: 0.8;">
                <p data-translate="hero.trusted">Trusted by educators and families in 50+ countries</p>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <h2 class="section-title" data-translate="features.title">Powerful Features for Modern Education</h2>
            <p class="section-subtitle" data-translate="features.subtitle">Everything you need to connect families and educators in one secure platform</p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">💬</span>
                    <h3 data-translate="features.messaging.title">Real-time Messaging</h3>
                    <p data-translate="features.messaging.desc">Instant, secure communication with automatic translation support for global accessibility.</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">📹</span>
                    <h3 data-translate="features.video.title">HD Video Calls</h3>
                    <p data-translate="features.video.desc">Crystal-clear WebRTC video calls with live captions and multi-language translation.</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">📚</span>
                    <h3 data-translate="features.management.title">Class Management</h3>
                    <p data-translate="features.management.desc">Comprehensive tools for organizing students, tracking progress, and managing educational content.</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🔒</span>
                    <h3 data-translate="features.security.title">Enterprise Security</h3>
                    <p data-translate="features.security.desc">FERPA, HIPAA, and GDPR compliant with end-to-end encryption and blockchain logging.</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🌍</span>
                    <h3 data-translate="features.multilingual.title">Multilingual Support</h3>
                    <p data-translate="features.multilingual.desc">AI-powered translation in 50+ languages with RTL support for global accessibility.</p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">📱</span>
                    <h3 data-translate="features.crossplatform.title">Cross-Platform</h3>
                    <p data-translate="features.crossplatform.desc">Available on web, mobile (iOS/Android), and as a progressive web app.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Section -->
    <section class="security">
        <div class="container">
            <h2 class="section-title" data-translate="security.title">Enterprise-Grade Security & Compliance</h2>
            <p class="section-subtitle" data-translate="security.subtitle">Built with privacy and security as our foundation</p>
            
            <div class="security-grid">
                <div class="security-badge">
                    <span class="icon">🛡️</span>
                    <div class="text">
                        <h4 data-translate="security.ferpa.title">FERPA Compliant</h4>
                        <p data-translate="security.ferpa.desc">Full compliance with educational privacy regulations</p>
                    </div>
                </div>
                
                <div class="security-badge">
                    <span class="icon">🏥</span>
                    <div class="text">
                        <h4 data-translate="security.hipaa.title">HIPAA Ready</h4>
                        <p data-translate="security.hipaa.desc">Healthcare information protection standards</p>
                    </div>
                </div>
                
                <div class="security-badge">
                    <span class="icon">🇪🇺</span>
                    <div class="text">
                        <h4 data-translate="security.gdpr.title">GDPR Compliant</h4>
                        <p data-translate="security.gdpr.desc">European data protection regulation compliance</p>
                    </div>
                </div>
                
                <div class="security-badge">
                    <span class="icon">🔐</span>
                    <div class="text">
                        <h4 data-translate="security.encryption.title">End-to-End Encryption</h4>
                        <p data-translate="security.encryption.desc">Military-grade encryption for all communications</p>
                    </div>
                </div>
                
                <div class="security-badge">
                    <span class="icon">⛓️</span>
                    <div class="text">
                        <h4 data-translate="security.blockchain.title">Blockchain Logging</h4>
                        <p data-translate="security.blockchain.desc">Immutable audit trails for all activities</p>
                    </div>
                </div>
                
                <div class="security-badge">
                    <span class="icon">🔍</span>
                    <div class="text">
                        <h4 data-translate="security.monitoring.title">24/7 Monitoring</h4>
                        <p data-translate="security.monitoring.desc">Continuous security monitoring and threat detection</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3 data-translate="footer.product.title">Product</h3>
                    <p><a href="https://fameduconnect.app" data-translate="footer.product.webapp">Web Application</a></p>
                    <p><a href="#" data-translate="footer.product.mobile">Mobile Apps</a></p>
                    <p><a href="#" data-translate="footer.product.api">API Documentation</a></p>
                </div>
                
                <div class="footer-section">
                    <h3 data-translate="footer.company.title">Company</h3>
                    <p><a href="#" data-translate="footer.company.about">About Us</a></p>
                    <p><a href="#" data-translate="footer.company.careers">Careers</a></p>
                    <p><a href="#" data-translate="footer.company.contact">Contact</a></p>
                </div>
                
                <div class="footer-section">
                    <h3 data-translate="footer.legal.title">Legal</h3>
                    <p><a href="#" data-translate="footer.legal.privacy">Privacy Policy</a></p>
                    <p><a href="#" data-translate="footer.legal.terms">Terms of Service</a></p>
                    <p><a href="#" data-translate="footer.legal.security">Security</a></p>
                </div>
                
                <div class="footer-section">
                    <h3 data-translate="footer.support.title">Support</h3>
                    <p><a href="#" data-translate="footer.support.help">Help Center</a></p>
                    <p><a href="#" data-translate="footer.support.community">Community</a></p>
                    <p><a href="#" data-translate="footer.support.status">System Status</a></p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p data-translate="footer.copyright">© 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc. – NPO: 2023-*********</p>
                <p style="margin-top: 10px; font-size: 0.9rem;" data-translate="footer.creative">Creative Director: Na'imah Barnes</p>
            </div>
        </div>
    </footer>

    <script>
        // Translation data
        const translations = {
            en: {
                'hero.title': '🎓 FamEduConnect',
                'hero.subtitle': 'Secure, multilingual platform connecting families and educators worldwide with real-time communication, video calls, and educational management tools.',
                'hero.cta.primary': '🚀 Launch App',
                'hero.cta.secondary': '📖 Learn More',
                'hero.trusted': 'Trusted by educators and families in 50+ countries',
                'features.title': 'Powerful Features for Modern Education',
                'features.subtitle': 'Everything you need to connect families and educators in one secure platform',
                'features.messaging.title': 'Real-time Messaging',
                'features.messaging.desc': 'Instant, secure communication with automatic translation support for global accessibility.',
                'features.video.title': 'HD Video Calls',
                'features.video.desc': 'Crystal-clear WebRTC video calls with live captions and multi-language translation.',
                'features.management.title': 'Class Management',
                'features.management.desc': 'Comprehensive tools for organizing students, tracking progress, and managing educational content.',
                'features.security.title': 'Enterprise Security',
                'features.security.desc': 'FERPA, HIPAA, and GDPR compliant with end-to-end encryption and blockchain logging.',
                'features.multilingual.title': 'Multilingual Support',
                'features.multilingual.desc': 'AI-powered translation in 50+ languages with RTL support for global accessibility.',
                'features.crossplatform.title': 'Cross-Platform',
                'features.crossplatform.desc': 'Available on web, mobile (iOS/Android), and as a progressive web app.',
                'security.title': 'Enterprise-Grade Security & Compliance',
                'security.subtitle': 'Built with privacy and security as our foundation',
                'security.ferpa.title': 'FERPA Compliant',
                'security.ferpa.desc': 'Full compliance with educational privacy regulations',
                'security.hipaa.title': 'HIPAA Ready',
                'security.hipaa.desc': 'Healthcare information protection standards',
                'security.gdpr.title': 'GDPR Compliant',
                'security.gdpr.desc': 'European data protection regulation compliance',
                'security.encryption.title': 'End-to-End Encryption',
                'security.encryption.desc': 'Military-grade encryption for all communications',
                'security.blockchain.title': 'Blockchain Logging',
                'security.blockchain.desc': 'Immutable audit trails for all activities',
                'security.monitoring.title': '24/7 Monitoring',
                'security.monitoring.desc': 'Continuous security monitoring and threat detection',
                'footer.product.title': 'Product',
                'footer.product.webapp': 'Web Application',
                'footer.product.mobile': 'Mobile Apps',
                'footer.product.api': 'API Documentation',
                'footer.company.title': 'Company',
                'footer.company.about': 'About Us',
                'footer.company.careers': 'Careers',
                'footer.company.contact': 'Contact',
                'footer.legal.title': 'Legal',
                'footer.legal.privacy': 'Privacy Policy',
                'footer.legal.terms': 'Terms of Service',
                'footer.legal.security': 'Security',
                'footer.support.title': 'Support',
                'footer.support.help': 'Help Center',
                'footer.support.community': 'Community',
                'footer.support.status': 'System Status',
                'footer.copyright': '© 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc. – NPO: 2023-*********',
                'footer.creative': 'Creative Director: Na\'imah Barnes'
            },
            ar: {
                'hero.title': '🎓 فام إيدو كونكت',
                'hero.subtitle': 'منصة آمنة ومتعددة اللغات تربط العائلات والمعلمين حول العالم مع التواصل الفوري ومكالمات الفيديو وأدوات إدارة التعليم.',
                'hero.cta.primary': '🚀 تشغيل التطبيق',
                'hero.cta.secondary': '📖 اعرف المزيد',
                'hero.trusted': 'موثوق من قبل المعلمين والعائلات في أكثر من 50 دولة',
                'features.title': 'ميزات قوية للتعليم الحديث',
                'features.subtitle': 'كل ما تحتاجه لربط العائلات والمعلمين في منصة آمنة واحدة',
                'features.messaging.title': 'المراسلة الفورية',
                'features.messaging.desc': 'تواصل فوري وآمن مع دعم الترجمة التلقائية للوصول العالمي.',
                'features.video.title': 'مكالمات فيديو عالية الدقة',
                'features.video.desc': 'مكالمات فيديو WebRTC واضحة مع ترجمة مباشرة ومتعددة اللغات.',
                'features.management.title': 'إدارة الفصول',
                'features.management.desc': 'أدوات شاملة لتنظيم الطلاب وتتبع التقدم وإدارة المحتوى التعليمي.',
                'features.security.title': 'أمان المؤسسات',
                'features.security.desc': 'متوافق مع FERPA و HIPAA و GDPR مع التشفير الشامل وتسجيل البلوك تشين.',
                'features.multilingual.title': 'دعم متعدد اللغات',
                'features.multilingual.desc': 'ترجمة مدعومة بالذكاء الاصطناعي بأكثر من 50 لغة مع دعم RTL للوصول العالمي.',
                'features.crossplatform.title': 'متعدد المنصات',
                'features.crossplatform.desc': 'متاح على الويب والهاتف المحمول (iOS/Android) وكتطبيق ويب تقدمي.',
                'security.title': 'أمان وامتثال على مستوى المؤسسات',
                'security.subtitle': 'مبني مع الخصوصية والأمان كأساس لنا',
                'security.ferpa.title': 'متوافق مع FERPA',
                'security.ferpa.desc': 'امتثال كامل لقوانين خصوصية التعليم',
                'security.hipaa.title': 'جاهز لـ HIPAA',
                'security.hipaa.desc': 'معايير حماية المعلومات الصحية',
                'security.gdpr.title': 'متوافق مع GDPR',
                'security.gdpr.desc': 'امتثال لقانون حماية البيانات الأوروبي',
                'security.encryption.title': 'تشفير شامل',
                'security.encryption.desc': 'تشفير عسكري لجميع الاتصالات',
                'security.blockchain.title': 'تسجيل البلوك تشين',
                'security.blockchain.desc': 'مسارات تدقيق غير قابلة للتغيير لجميع الأنشطة',
                'security.monitoring.title': 'مراقبة 24/7',
                'security.monitoring.desc': 'مراقبة أمنية مستمرة وكشف التهديدات',
                'footer.copyright': '© 2025 جود القابضة، بدايا إكس، وديفيتيا جود دوورز المحدودة – NPO: 2023-*********',
                'footer.creative': 'المدير الإبداعي: نعيمة بارنز'
            }
        };

        // Language switching functionality
        function changeLanguage() {
            const selectedLang = document.getElementById('languageSelect').value;
            const elements = document.querySelectorAll('[data-translate]');
            
            // Update text direction for RTL languages
            if (selectedLang === 'ar') {
                document.documentElement.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
            } else {
                document.documentElement.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', selectedLang);
            }
            
            // Update translations
            elements.forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[selectedLang] && translations[selectedLang][key]) {
                    element.textContent = translations[selectedLang][key];
                }
            });
            
            // Save language preference
            localStorage.setItem('preferredLanguage', selectedLang);
        }

        // Load saved language preference
        document.addEventListener('DOMContentLoaded', function() {
            const savedLang = localStorage.getItem('preferredLanguage') || 'en';
            document.getElementById('languageSelect').value = savedLang;
            if (savedLang !== 'en') {
                changeLanguage();
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>