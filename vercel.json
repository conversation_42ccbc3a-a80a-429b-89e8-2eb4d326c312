{"version": 2, "name": "fameduconnect-frontend", "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "frontend/build"}}], "routes": [{"src": "/api/(.*)", "dest": "https://api.fameduconnect.app/api/$1"}, {"src": "/(.*)", "dest": "/frontend/build/$1"}], "env": {"REACT_APP_API_URL": "https://api.fameduconnect.app", "REACT_APP_SOCKET_URL": "https://api.fameduconnect.app", "REACT_APP_ENVIRONMENT": "production"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/((?!api).*)", "destination": "/index.html"}]}