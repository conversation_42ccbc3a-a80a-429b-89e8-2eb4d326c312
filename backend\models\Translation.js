module.exports = (sequelize, DataTypes) => {
  const Translation = sequelize.define('Translation', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    messageId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Messages',
        key: 'id'
      }
    },
    originalText: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    translatedText: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    sourceLanguage: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 5]
      }
    },
    targetLanguage: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 5]
      }
    },
    translationService: {
      type: DataTypes.ENUM('google', 'azure', 'aws', 'custom'),
      defaultValue: 'google'
    },
    confidence: {
      type: DataTypes.DECIMAL(5, 4),
      allowNull: true,
      validate: {
        min: 0,
        max: 1
      }
    },
    isVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    verifiedBy: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    verifiedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    corrections: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSON,
      defaultValue: {}
    }
  }, {
    indexes: [
      { fields: ['messageId'] },
      { fields: ['sourceLanguage'] },
      { fields: ['targetLanguage'] },
      { fields: ['translationService'] },
      { fields: ['isVerified'] }
    ]
  });

  Translation.associate = function(models) {
    Translation.belongsTo(models.Message, {
      foreignKey: 'messageId',
      as: 'message'
    });
    Translation.belongsTo(models.User, {
      foreignKey: 'verifiedBy',
      as: 'verifier'
    });
  };

  return Translation;
};