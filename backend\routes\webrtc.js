const express = require('express');
const { VideoCall, User, Class } = require('../models');
const { body, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Create a video call
router.post('/calls', authMiddleware, [
  body('title').trim().isLength({ min: 1, max: 200 }),
  body('scheduledStartTime').isISO8601(),
  body('scheduledEndTime').isISO8601(),
  body('callType').isIn(['parent-teacher', 'class-meeting', 'group-discussion', 'emergency'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      title,
      description,
      scheduledStartTime,
      scheduledEndTime,
      callType,
      classId,
      maxParticipants = 10,
      meetingSettings = {}
    } = req.body;

    // Generate unique call ID
    const callId = uuidv4();

    const videoCall = await VideoCall.create({
      callId,
      hostId: req.user.userId,
      classId,
      title,
      description,
      scheduledStartTime: new Date(scheduledStartTime),
      scheduledEndTime: new Date(scheduledEndTime),
      callType,
      maxParticipants,
      meetingSettings: {
        allowScreenShare: true,
        allowChat: true,
        allowRecording: true,
        muteOnJoin: false,
        waitingRoom: false,
        autoTranscription: true,
        liveTranslation: false,
        ...meetingSettings
      }
    });

    // Load full call data
    const fullCall = await VideoCall.findByPk(videoCall.id, {
      include: [
        {
          model: User,
          as: 'host',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Class,
          as: 'class',
          attributes: ['id', 'className', 'classCode']
        }
      ]
    });

    res.status(201).json(fullCall);
  } catch (error) {
    console.error('Create video call error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get video calls for user
router.get('/calls', authMiddleware, async (req, res) => {
  try {
    const { status, upcoming, page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = {
      [require('sequelize').Op.or]: [
        { hostId: req.user.userId }
        // Add participant check when we implement the many-to-many relationship
      ]
    };

    if (status) {
      whereClause.status = status;
    }

    if (upcoming === 'true') {
      whereClause.scheduledStartTime = {
        [require('sequelize').Op.gte]: new Date()
      };
    }

    const calls = await VideoCall.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'host',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Class,
          as: 'class',
          attributes: ['id', 'className', 'classCode']
        }
      ],
      order: [['scheduledStartTime', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      calls: calls.rows,
      totalCount: calls.count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(calls.count / limit)
    });
  } catch (error) {
    console.error('Get video calls error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get specific video call
router.get('/calls/:callId', authMiddleware, async (req, res) => {
  try {
    const { callId } = req.params;

    const videoCall = await VideoCall.findOne({
      where: { callId },
      include: [
        {
          model: User,
          as: 'host',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Class,
          as: 'class',
          attributes: ['id', 'className', 'classCode']
        }
      ]
    });

    if (!videoCall) {
      return res.status(404).json({ message: 'Video call not found' });
    }

    // Check if user has access to this call
    const hasAccess = videoCall.hostId === req.user.userId || 
                     (videoCall.classId && req.user.role === 'teacher') ||
                     req.user.role === 'admin';

    if (!hasAccess) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(videoCall);
  } catch (error) {
    console.error('Get video call error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Start a video call
router.post('/calls/:callId/start', authMiddleware, async (req, res) => {
  try {
    const { callId } = req.params;

    const videoCall = await VideoCall.findOne({
      where: { callId }
    });

    if (!videoCall) {
      return res.status(404).json({ message: 'Video call not found' });
    }

    // Check if user can start this call
    if (videoCall.hostId !== req.user.userId && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Only the host can start the call' });
    }

    if (videoCall.status !== 'scheduled') {
      return res.status(400).json({ message: 'Call cannot be started' });
    }

    await videoCall.update({
      status: 'active',
      actualStartTime: new Date()
    });

    // Notify participants via Socket.IO
    const io = req.app.get('io');
    if (io) {
      if (videoCall.classId) {
        io.to(`class_${videoCall.classId}`).emit('call_started', {
          callId: videoCall.callId,
          title: videoCall.title
        });
      }
    }

    res.json({ message: 'Call started successfully', callId: videoCall.callId });
  } catch (error) {
    console.error('Start video call error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// End a video call
router.post('/calls/:callId/end', authMiddleware, async (req, res) => {
  try {
    const { callId } = req.params;
    const { meetingNotes, actionItems } = req.body;

    const videoCall = await VideoCall.findOne({
      where: { callId }
    });

    if (!videoCall) {
      return res.status(404).json({ message: 'Video call not found' });
    }

    // Check if user can end this call
    if (videoCall.hostId !== req.user.userId && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Only the host can end the call' });
    }

    if (videoCall.status !== 'active') {
      return res.status(400).json({ message: 'Call is not active' });
    }

    await videoCall.update({
      status: 'ended',
      actualEndTime: new Date(),
      meetingNotes,
      actionItems: actionItems || []
    });

    // Notify participants
    const io = req.app.get('io');
    if (io) {
      io.to(`call_${callId}`).emit('call_ended', {
        callId: videoCall.callId,
        endedBy: req.user.userId
      });
    }

    res.json({ message: 'Call ended successfully' });
  } catch (error) {
    console.error('End video call error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Join a video call
router.post('/calls/:callId/join', authMiddleware, async (req, res) => {
  try {
    const { callId } = req.params;

    const videoCall = await VideoCall.findOne({
      where: { callId }
    });

    if (!videoCall) {
      return res.status(404).json({ message: 'Video call not found' });
    }

    if (videoCall.status !== 'active' && videoCall.status !== 'scheduled') {
      return res.status(400).json({ message: 'Call is not available' });
    }

    if (videoCall.currentParticipants >= videoCall.maxParticipants) {
      return res.status(400).json({ message: 'Call is full' });
    }

    // Update participant count
    await videoCall.update({
      currentParticipants: videoCall.currentParticipants + 1
    });

    // Generate join token (in production, use proper WebRTC signaling)
    const joinToken = require('jsonwebtoken').sign(
      { 
        userId: req.user.userId, 
        callId: videoCall.callId,
        role: 'participant'
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '2h' }
    );

    res.json({
      message: 'Joined call successfully',
      callId: videoCall.callId,
      joinToken,
      meetingSettings: videoCall.meetingSettings
    });
  } catch (error) {
    console.error('Join video call error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Leave a video call
router.post('/calls/:callId/leave', authMiddleware, async (req, res) => {
  try {
    const { callId } = req.params;

    const videoCall = await VideoCall.findOne({
      where: { callId }
    });

    if (!videoCall) {
      return res.status(404).json({ message: 'Video call not found' });
    }

    // Update participant count
    await videoCall.update({
      currentParticipants: Math.max(0, videoCall.currentParticipants - 1)
    });

    res.json({ message: 'Left call successfully' });
  } catch (error) {
    console.error('Leave video call error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update video call settings
router.patch('/calls/:callId', authMiddleware, async (req, res) => {
  try {
    const { callId } = req.params;
    const updates = req.body;

    const videoCall = await VideoCall.findOne({
      where: { callId }
    });

    if (!videoCall) {
      return res.status(404).json({ message: 'Video call not found' });
    }

    // Check if user can update this call
    if (videoCall.hostId !== req.user.userId && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Only the host can update the call' });
    }

    await videoCall.update(updates);

    res.json({ message: 'Call updated successfully' });
  } catch (error) {
    console.error('Update video call error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get call recordings
router.get('/calls/:callId/recording', authMiddleware, async (req, res) => {
  try {
    const { callId } = req.params;

    const videoCall = await VideoCall.findOne({
      where: { callId }
    });

    if (!videoCall) {
      return res.status(404).json({ message: 'Video call not found' });
    }

    if (!videoCall.isRecorded || !videoCall.recordingUrl) {
      return res.status(404).json({ message: 'Recording not available' });
    }

    res.json({
      recordingUrl: videoCall.recordingUrl,
      transcriptionUrl: videoCall.transcriptionUrl,
      duration: videoCall.actualEndTime - videoCall.actualStartTime
    });
  } catch (error) {
    console.error('Get recording error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;