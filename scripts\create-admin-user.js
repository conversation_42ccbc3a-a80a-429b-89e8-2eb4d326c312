const bcrypt = require('bcryptjs');
const { User } = require('../backend/models');

async function createAdminUser() {
  try {
    console.log('Creating admin user...');
    
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ 
      where: { email: '<EMAIL>' } 
    });
    
    if (existingAdmin) {
      console.log('Admin user already exists!');
      console.log('Email: <EMAIL>');
      console.log('Password: AdminDemo2025!');
      return;
    }
    
    // Create admin user
    const hashedPassword = await bcrypt.hash('AdminDemo2025!', 10);
    
    const adminUser = await User.create({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      isActive: true,
      emailVerified: true,
      preferredLanguage: 'en',
      accessibilitySettings: {
        highContrast: false,
        largeText: false,
        screenReader: false
      },
      notificationSettings: {
        email: true,
        push: true,
        sms: false
      }
    });
    
    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: AdminDemo2025!');
    console.log('🆔 User ID:', adminUser.id);
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  }
}

// Run the script
createAdminUser(); 