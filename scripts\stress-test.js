const axios = require('axios');
const { exec } = require('child_process');
const fs = require('fs');

// Configuration
const CONFIG = {
  BACKEND_URL: 'http://localhost:3002',
  FRONTEND_URL: 'http://localhost:3000',
  ADMIN_URL: 'http://localhost:3001',
  TEST_DURATION: 300000, // 5 minutes
  CONCURRENT_USERS: 50,
  REQUESTS_PER_SECOND: 100,
  TIMEOUT: 10000
};

// Test Results
const results = {
  backend: { success: 0, failed: 0, errors: [] },
  frontend: { success: 0, failed: 0, errors: [] },
  admin: { success: 0, failed: 0, errors: [] },
  performance: { avgResponseTime: 0, maxResponseTime: 0, minResponseTime: Infinity },
  startTime: Date.now()
};

// Test endpoints
const ENDPOINTS = {
  backend: [
    '/api/health',
    '/api/test',
    '/api/auth/login',
    '/api/users/profile',
    '/api/messages',
    '/api/students',
    '/api/classes'
  ],
  frontend: [
    '/',
    '/login',
    '/dashboard',
    '/messages',
    '/profile'
  ],
  admin: [
    '/',
    '/login',
    '/dashboard',
    '/users',
    '/reports'
  ]
};

// Utility functions
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const randomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

// Test data
const TEST_CREDENTIALS = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' }
];

// Backend stress test
async function testBackend() {
  console.log('🔧 Testing Backend Endpoints...');
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    const userIndex = i % TEST_CREDENTIALS.length;
    const credentials = TEST_CREDENTIALS[userIndex];
    
    setInterval(async () => {
      try {
        // Test health endpoint
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, {
          timeout: CONFIG.TIMEOUT
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.backend.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.backend.failed++;
          results.backend.errors.push(`Health check failed: ${response.status}`);
        }
      } catch (error) {
        results.backend.failed++;
        results.backend.errors.push(`Backend error: ${error.message}`);
      }
      
      // Test random endpoint
      try {
        const endpoint = ENDPOINTS.backend[randomInt(0, ENDPOINTS.backend.length - 1)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}${endpoint}`, {
          timeout: CONFIG.TIMEOUT
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.backend.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.backend.failed++;
          results.backend.errors.push(`Endpoint ${endpoint} failed: ${response.status}`);
        }
      } catch (error) {
        results.backend.failed++;
        results.backend.errors.push(`Backend endpoint error: ${error.message}`);
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Frontend stress test
async function testFrontend() {
  console.log('📱 Testing Frontend Endpoints...');
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    setInterval(async () => {
      try {
        const endpoint = ENDPOINTS.frontend[randomInt(0, ENDPOINTS.frontend.length - 1)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.FRONTEND_URL}${endpoint}`, {
          timeout: CONFIG.TIMEOUT
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.frontend.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.frontend.failed++;
          results.frontend.errors.push(`Frontend ${endpoint} failed: ${response.status}`);
        }
      } catch (error) {
        results.frontend.failed++;
        results.frontend.errors.push(`Frontend error: ${error.message}`);
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Admin stress test
async function testAdmin() {
  console.log('👨‍💼 Testing Admin Endpoints...');
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    setInterval(async () => {
      try {
        const endpoint = ENDPOINTS.admin[randomInt(0, ENDPOINTS.admin.length - 1)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.ADMIN_URL}${endpoint}`, {
          timeout: CONFIG.TIMEOUT
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.admin.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.admin.failed++;
          results.admin.errors.push(`Admin ${endpoint} failed: ${response.status}`);
        }
      } catch (error) {
        results.admin.failed++;
        results.admin.errors.push(`Admin error: ${error.message}`);
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Performance metrics
function updatePerformanceMetrics(responseTime) {
  results.performance.avgResponseTime = 
    (results.performance.avgResponseTime + responseTime) / 2;
  results.performance.maxResponseTime = Math.max(results.performance.maxResponseTime, responseTime);
  results.performance.minResponseTime = Math.min(results.performance.minResponseTime, responseTime);
}

// System health check
async function checkSystemHealth() {
  console.log('🏥 Checking System Health...');
  
  try {
    // Check if all services are running
    const healthChecks = [
      axios.get(`${CONFIG.BACKEND_URL}/api/health`),
      axios.get(`${CONFIG.FRONTEND_URL}`),
      axios.get(`${CONFIG.ADMIN_URL}`)
    ];
    
    const responses = await Promise.allSettled(healthChecks);
    
    responses.forEach((response, index) => {
      const service = ['Backend', 'Frontend', 'Admin'][index];
      if (response.status === 'fulfilled') {
        console.log(`✅ ${service} is healthy`);
      } else {
        console.log(`❌ ${service} is down: ${response.reason.message}`);
      }
    });
  } catch (error) {
    console.log(`❌ System health check failed: ${error.message}`);
  }
}

// Memory and CPU monitoring
function monitorSystemResources() {
  console.log('📊 Monitoring System Resources...');
  
  setInterval(() => {
    exec('tasklist /FI "IMAGENAME eq node.exe" /FO CSV', (error, stdout) => {
      if (!error) {
        const lines = stdout.split('\n').filter(line => line.includes('node.exe'));
        console.log(`🖥️  Active Node processes: ${lines.length}`);
      }
    });
  }, 10000);
}

// Generate report
function generateReport() {
  const totalTime = Date.now() - results.startTime;
  const totalRequests = results.backend.success + results.backend.failed + 
                       results.frontend.success + results.frontend.failed +
                       results.admin.success + results.admin.failed;
  
  const report = {
    timestamp: new Date().toISOString(),
    duration: `${Math.round(totalTime / 1000)}s`,
    totalRequests,
    requestsPerSecond: Math.round(totalRequests / (totalTime / 1000)),
    successRate: Math.round(((results.backend.success + results.frontend.success + results.admin.success) / totalRequests) * 100),
    results,
    recommendations: []
  };
  
  // Generate recommendations
  if (results.backend.failed > results.backend.success * 0.1) {
    report.recommendations.push('Backend needs optimization - high failure rate');
  }
  
  if (results.frontend.failed > results.frontend.success * 0.1) {
    report.recommendations.push('Frontend needs optimization - high failure rate');
  }
  
  if (results.performance.avgResponseTime > 2000) {
    report.recommendations.push('Response times are too slow - consider optimization');
  }
  
  if (results.performance.maxResponseTime > 10000) {
    report.recommendations.push('Maximum response time is too high - investigate bottlenecks');
  }
  
  return report;
}

// Main stress test function
async function runStressTest() {
  console.log('🚀 Starting Comprehensive Stress Test...');
  console.log(`⏱️  Duration: ${CONFIG.TEST_DURATION / 1000}s`);
  console.log(`👥 Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`📡 Requests/Second: ${CONFIG.REQUESTS_PER_SECOND}`);
  console.log('');
  
  // Start monitoring
  monitorSystemResources();
  
  // Initial health check
  await checkSystemHealth();
  console.log('');
  
  // Start stress tests
  testBackend();
  testFrontend();
  testAdmin();
  
  // Run for specified duration
  setTimeout(() => {
    console.log('\n📊 Generating Stress Test Report...');
    const report = generateReport();
    
    console.log('\n' + '='.repeat(60));
    console.log('📈 STRESS TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`⏱️  Duration: ${report.duration}`);
    console.log(`📡 Total Requests: ${report.totalRequests}`);
    console.log(`⚡ Requests/Second: ${report.requestsPerSecond}`);
    console.log(`✅ Success Rate: ${report.successRate}%`);
    console.log('');
    
    console.log('🔧 Backend Results:');
    console.log(`   ✅ Success: ${results.backend.success}`);
    console.log(`   ❌ Failed: ${results.backend.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.backend.success / (results.backend.success + results.backend.failed)) * 100)}%`);
    console.log('');
    
    console.log('📱 Frontend Results:');
    console.log(`   ✅ Success: ${results.frontend.success}`);
    console.log(`   ❌ Failed: ${results.frontend.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.frontend.success / (results.frontend.success + results.frontend.failed)) * 100)}%`);
    console.log('');
    
    console.log('👨‍💼 Admin Results:');
    console.log(`   ✅ Success: ${results.admin.success}`);
    console.log(`   ❌ Failed: ${results.admin.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.admin.success / (results.admin.success + results.admin.failed)) * 100)}%`);
    console.log('');
    
    console.log('⚡ Performance Metrics:');
    console.log(`   📊 Avg Response Time: ${Math.round(results.performance.avgResponseTime)}ms`);
    console.log(`   🐌 Min Response Time: ${Math.round(results.performance.minResponseTime)}ms`);
    console.log(`   🚀 Max Response Time: ${Math.round(results.performance.maxResponseTime)}ms`);
    console.log('');
    
    if (report.recommendations.length > 0) {
      console.log('⚠️  Recommendations:');
      report.recommendations.forEach(rec => console.log(`   • ${rec}`));
    } else {
      console.log('✅ System is performing well under stress!');
    }
    
    console.log('\n' + '='.repeat(60));
    
    // Save report to file
    fs.writeFileSync('stress-test-report.json', JSON.stringify(report, null, 2));
    console.log('📄 Report saved to: stress-test-report.json');
    
    process.exit(0);
  }, CONFIG.TEST_DURATION);
}

// Run the stress test
runStressTest().catch(console.error); 