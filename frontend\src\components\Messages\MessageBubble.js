import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  DocumentIcon,
  PhotoIcon,
  SpeakerWaveIcon,
  VideoCameraIcon,
  LanguageIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline';

const MessageBubble = ({ message, isOwnMessage, showAvatar, timestamp }) => {
  const [showActions, setShowActions] = useState(false);
  const [showTranslation, setShowTranslation] = useState(false);

  const getMessageTypeIcon = (type) => {
    switch (type) {
      case 'image':
        return <PhotoIcon className="h-4 w-4" />;
      case 'file':
        return <DocumentIcon className="h-4 w-4" />;
      case 'voice':
        return <SpeakerWaveIcon className="h-4 w-4" />;
      case 'video':
        return <VideoCameraIcon className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const getStatusIcon = () => {
    if (message.isDeleted) {
      return <ExclamationTriangleIcon className="h-3 w-3 text-gray-400" />;
    }
    
    if (isOwnMessage) {
      if (message.isRead) {
        return <CheckCircleIcon className="h-3 w-3 text-blue-500" />;
      } else if (message.isSent) {
        return <CheckIcon className="h-3 w-3 text-gray-400" />;
      }
    }
    
    return null;
  };

  const getPriorityColor = () => {
    switch (message.priority) {
      case 'urgent':
        return 'border-l-4 border-red-500';
      case 'high':
        return 'border-l-4 border-orange-500';
      default:
        return '';
    }
  };

  const renderAttachments = () => {
    if (!message.attachments || message.attachments.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 space-y-2">
        {message.attachments.map((attachment, index) => (
          <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-700 rounded">
            {attachment.mimetype?.startsWith('image/') ? (
              <img
                src={attachment.path}
                alt={attachment.originalName}
                className="max-w-xs max-h-48 rounded cursor-pointer hover:opacity-90 transition-opacity"
                onClick={() => window.open(attachment.path, '_blank')}
              />
            ) : (
              <div className="flex items-center space-x-2">
                <DocumentIcon className="h-5 w-5 text-gray-500" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {attachment.originalName}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {(attachment.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <button
                  onClick={() => window.open(attachment.path, '_blank')}
                  className="text-indigo-600 hover:text-indigo-500 text-sm font-medium"
                >
                  Download
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderVoiceMessage = () => {
    if (message.messageType !== 'voice') return null;

    return (
      <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <button className="p-2 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors">
          <SpeakerWaveIcon className="h-4 w-4" />
        </button>
        <div className="flex-1">
          <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded-full">
            <div className="h-2 bg-indigo-600 rounded-full" style={{ width: '30%' }}></div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">0:15</p>
        </div>
      </div>
    );
  };

  return (
    <div className={`flex ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 max-w-xs sm:max-w-md lg:max-w-lg`}>
      {/* Avatar */}
      {showAvatar && !isOwnMessage && (
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
          {message.sender?.profilePicture ? (
            <img
              src={message.sender.profilePicture}
              alt={`${message.sender.firstName} ${message.sender.lastName}`}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
              {message.sender?.firstName?.[0]}{message.sender?.lastName?.[0]}
            </span>
          )}
        </div>
      )}

      {/* Message Bubble */}
      <motion.div
        whileHover={{ scale: 1.02 }}
        className={`
          relative group px-4 py-2 rounded-2xl max-w-full
          ${getPriorityColor()}
          ${isOwnMessage
            ? 'bg-indigo-600 text-white rounded-br-md'
            : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600'
          }
          ${message.isDeleted ? 'opacity-60 italic' : ''}
        `}
        onMouseEnter={() => setShowActions(true)}
        onMouseLeave={() => setShowActions(false)}
      >
        {/* Sender name (for group chats) */}
        {!isOwnMessage && message.classId && (
          <p className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
            {message.sender?.firstName} {message.sender?.lastName}
          </p>
        )}

        {/* Message content */}
        <div className="space-y-2">
          {/* Text content */}
          {message.content && (
            <p className="text-sm whitespace-pre-wrap break-words">
              {message.content}
            </p>
          )}

          {/* Voice message */}
          {renderVoiceMessage()}

          {/* Attachments */}
          {renderAttachments()}
        </div>

        {/* Message metadata */}
        <div className={`flex items-center justify-between mt-1 space-x-2 ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
          <div className="flex items-center space-x-1">
            {getMessageTypeIcon(message.messageType)}
            <span className={`text-xs ${isOwnMessage ? 'text-indigo-200' : 'text-gray-500 dark:text-gray-400'}`}>
              {timestamp}
            </span>
          </div>
          
          <div className="flex items-center space-x-1">
            {getStatusIcon()}
            {message.priority === 'urgent' && (
              <ExclamationTriangleIcon className="h-3 w-3 text-red-500" />
            )}
          </div>
        </div>

        {/* Message actions */}
        {showActions && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className={`absolute top-0 ${isOwnMessage ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'} flex items-center space-x-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 p-1`}
          >
            <button
              onClick={() => setShowTranslation(!showTranslation)}
              className="p-1 rounded text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              title="Translate"
            >
              <LanguageIcon className="h-4 w-4" />
            </button>
            
            <button
              className="p-1 rounded text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              title="More actions"
            >
              <EllipsisVerticalIcon className="h-4 w-4" />
            </button>
          </motion.div>
        )}

        {/* Translation */}
        {showTranslation && message.translations && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="mt-2 p-2 bg-gray-100 dark:bg-gray-600 rounded text-sm border-l-2 border-blue-500"
          >
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Translation:</p>
            <p>{message.translations.translatedText}</p>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};

export default MessageBubble;