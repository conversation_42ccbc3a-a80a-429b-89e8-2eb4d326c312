import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import usersSlice from './slices/usersSlice';
import classesSlice from './slices/classesSlice';
import analyticsSlice from './slices/analyticsSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    users: usersSlice,
    classes: classesSlice,
    analytics: analyticsSlice,
  },
});