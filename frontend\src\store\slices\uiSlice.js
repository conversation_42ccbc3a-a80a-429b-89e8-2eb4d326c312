import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  sidebarOpen: true,
  notificationsOpen: false,
  darkMode: localStorage.getItem('darkMode') === 'true' || false,
  highContrast: localStorage.getItem('highContrast') === 'true' || false,
  largeText: localStorage.getItem('largeText') === 'true' || false,
  voiceNavigationEnabled: false,
  screenReaderMode: false,
  language: localStorage.getItem('preferredLanguage') || 'en',
  loading: {
    global: false,
    messages: false,
    videoCall: false,
    dashboard: false
  },
  modals: {
    profile: false,
    settings: false,
    fileUpload: false,
    videoCall: false,
    translation: false
  },
  toast: {
    show: false,
    message: '',
    type: 'info', // info, success, warning, error
    duration: 4000
  },
  emergencyMode: false,
  connectionStatus: 'online' // online, offline, reconnecting
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action) => {
      state.sidebarOpen = action.payload;
    },
    toggleNotifications: (state) => {
      state.notificationsOpen = !state.notificationsOpen;
    },
    setNotificationsOpen: (state, action) => {
      state.notificationsOpen = action.payload;
    },
    toggleDarkMode: (state) => {
      state.darkMode = !state.darkMode;
      localStorage.setItem('darkMode', state.darkMode.toString());
    },
    setDarkMode: (state, action) => {
      state.darkMode = action.payload;
      localStorage.setItem('darkMode', action.payload.toString());
    },
    toggleHighContrast: (state) => {
      state.highContrast = !state.highContrast;
      localStorage.setItem('highContrast', state.highContrast.toString());
    },
    setHighContrast: (state, action) => {
      state.highContrast = action.payload;
      localStorage.setItem('highContrast', action.payload.toString());
    },
    toggleLargeText: (state) => {
      state.largeText = !state.largeText;
      localStorage.setItem('largeText', state.largeText.toString());
    },
    setLargeText: (state, action) => {
      state.largeText = action.payload;
      localStorage.setItem('largeText', action.payload.toString());
    },
    toggleVoiceNavigation: (state) => {
      state.voiceNavigationEnabled = !state.voiceNavigationEnabled;
    },
    setVoiceNavigation: (state, action) => {
      state.voiceNavigationEnabled = action.payload;
    },
    toggleScreenReaderMode: (state) => {
      state.screenReaderMode = !state.screenReaderMode;
    },
    setScreenReaderMode: (state, action) => {
      state.screenReaderMode = action.payload;
    },
    setLanguage: (state, action) => {
      state.language = action.payload;
      localStorage.setItem('preferredLanguage', action.payload);
    },
    setGlobalLoading: (state, action) => {
      state.loading.global = action.payload;
    },
    setModuleLoading: (state, action) => {
      const { module, loading } = action.payload;
      state.loading[module] = loading;
    },
    openModal: (state, action) => {
      const modalName = action.payload;
      state.modals[modalName] = true;
    },
    closeModal: (state, action) => {
      const modalName = action.payload;
      state.modals[modalName] = false;
    },
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(key => {
        state.modals[key] = false;
      });
    },
    showToast: (state, action) => {
      const { message, type = 'info', duration = 4000 } = action.payload;
      state.toast = {
        show: true,
        message,
        type,
        duration
      };
    },
    hideToast: (state) => {
      state.toast.show = false;
    },
    setEmergencyMode: (state, action) => {
      state.emergencyMode = action.payload;
    },
    setConnectionStatus: (state, action) => {
      state.connectionStatus = action.payload;
    },
    resetUI: (state) => {
      // Reset to initial state but preserve user preferences
      const preferences = {
        darkMode: state.darkMode,
        highContrast: state.highContrast,
        largeText: state.largeText,
        language: state.language
      };
      
      Object.assign(state, initialState, preferences);
    }
  }
});

export const {
  toggleSidebar,
  setSidebarOpen,
  toggleNotifications,
  setNotificationsOpen,
  toggleDarkMode,
  setDarkMode,
  toggleHighContrast,
  setHighContrast,
  toggleLargeText,
  setLargeText,
  toggleVoiceNavigation,
  setVoiceNavigation,
  toggleScreenReaderMode,
  setScreenReaderMode,
  setLanguage,
  setGlobalLoading,
  setModuleLoading,
  openModal,
  closeModal,
  closeAllModals,
  showToast,
  hideToast,
  setEmergencyMode,
  setConnectionStatus,
  resetUI
} = uiSlice.actions;

// Selectors
export const selectSidebarOpen = (state) => state.ui.sidebarOpen;
export const selectDarkMode = (state) => state.ui.darkMode;
export const selectAccessibilitySettings = (state) => ({
  highContrast: state.ui.highContrast,
  largeText: state.ui.largeText,
  voiceNavigation: state.ui.voiceNavigationEnabled,
  screenReader: state.ui.screenReaderMode
});
export const selectLanguage = (state) => state.ui.language;
export const selectLoading = (state) => state.ui.loading;
export const selectModals = (state) => state.ui.modals;
export const selectToast = (state) => state.ui.toast;
export const selectEmergencyMode = (state) => state.ui.emergencyMode;
export const selectConnectionStatus = (state) => state.ui.connectionStatus;

export default uiSlice.reducer;