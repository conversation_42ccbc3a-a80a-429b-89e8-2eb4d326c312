#!/bin/bash

echo "Deploying FamEduConnect..."

# Build all components
./scripts/build.sh

# Deploy backend
echo "Deploying backend..."
pm2 stop fameduconnect-backend || true
pm2 start backend/server.js --name fameduconnect-backend

# Deploy frontend (copy to web server)
echo "Deploying frontend..."
cp -r frontend/build/* /var/www/fameduconnect/

# Deploy admin (copy to web server)
echo "Deploying admin..."
cp -r admin/build/* /var/www/fameduconnect-admin/

echo "Deployment completed!"