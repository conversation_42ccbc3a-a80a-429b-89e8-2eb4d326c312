import React from 'react';

const SafeErrorDisplay = ({ error, className = "" }) => {
  // Safely convert any error to a string
  const getErrorMessage = (err) => {
    if (!err) return 'An error occurred';
    
    if (typeof err === 'string') {
      return err;
    }
    
    if (err instanceof Error) {
      return err.message || 'An error occurred';
    }
    
    if (typeof err === 'object') {
      // Handle error objects with message property
      if (err.message) {
        return String(err.message);
      }
      
      // Handle error objects with error property
      if (err.error) {
        return String(err.error);
      }
      
      // Try to stringify the object safely
      try {
        return JSON.stringify(err);
      } catch {
        return 'An error occurred';
      }
    }
    
    // Fallback
    try {
      return String(err);
    } catch {
      return 'An error occurred';
    }
  };

  const errorMessage = getErrorMessage(error);

  return (
    <div className={`p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md ${className}`}>
      <p className="text-sm text-red-600 dark:text-red-400">
        {errorMessage}
      </p>
    </div>
  );
};

export default SafeErrorDisplay; 