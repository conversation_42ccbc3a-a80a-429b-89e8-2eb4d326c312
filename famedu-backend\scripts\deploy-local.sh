#!/bin/bash

echo "Deploying FamEduConnect Backend for Local Development..."

# Start local services
echo "Starting local services..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Run database migrations
echo "Running database migrations..."
cd ../
npx prisma migrate dev

# Generate Prisma client
echo "Generating Prisma client..."
npx prisma generate

# Start the NestJS application
echo "Starting NestJS application..."
npm run start:dev

echo "Local deployment complete!"
echo "Services available at:"
echo "- PostgreSQL: localhost:5432"
echo "- Redis: localhost:6379"
echo "- RabbitMQ: localhost:5672 (Management: http://localhost:15672)"
echo "- NestJS Backend: http://localhost:3000"
