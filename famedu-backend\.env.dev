# Local Development Environment Variables
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=postgresql://famedu_user:famedu_password_123@localhost:5432/fameduconnect_dev

# Redis Configuration
REDIS_URL=redis://localhost:6379

# RabbitMQ Configuration
RABBITMQ_URL=amqp://famedu_user:famedu_password_123@localhost:5672

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Logging
LOG_LEVEL=debug
ENABLE_METRICS=true

# Sentry (optional for local dev)
SENTRY_DSN=

# SMTP Configuration (optional for local dev)
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=

# File Upload
MAX_FILE_SIZE=10485760
BCRYPT_ROUNDS=12
