apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-cluster-config
  namespace: fameduconnect
data:
  redis.conf: |
    # Redis Cluster Configuration
    port 6379
    cluster-enabled yes
    cluster-config-file nodes.conf
    cluster-node-timeout 5000
    appendonly yes
    appendfsync everysec
    save 900 1
    save 300 10
    save 60 10000
    maxmemory 2gb
    maxmemory-policy allkeys-lru
    timeout 0
    tcp-keepalive 300
    tcp-backlog 511
    databases 16
    always-show-logo yes
    loglevel notice
    logfile ""
    syslog-enabled no
    syslog-ident redis
    syslog-facility local0
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir /data
    replica-serve-stale-data yes
    replica-read-only yes
    repl-diskless-sync no
    repl-diskless-sync-delay 5
    repl-ping-replica-period 10
    repl-timeout 60
    repl-disable-tcp-nodelay no
    replica-priority 100
    maxclients 10000
    maxmemory-samples 5
    hash-max-ziplist-entries 512
    hash-max-ziplist-value 64
    list-max-ziplist-size -2
    list-compress-depth 0
    set-max-intset-entries 512
    zset-max-ziplist-entries 128
    zset-max-ziplist-value 64
    hll-sparse-max-bytes 3000
    stream-node-max-bytes 4096
    stream-node-max-entries 100
    activerehashing yes
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
    hz 10
    dynamic-hz yes
    aof-rewrite-incremental-fsync yes
    rdb-save-incremental-fsync yes
    lfu-log-factor 10
    lfu-decay-time 1
---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
  namespace: fameduconnect
type: Opaque
data:
  redis-password: ${BASE64_ENCODED_REDIS_PASSWORD}
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
  namespace: fameduconnect
  labels:
    app: redis-cluster
    component: cache
spec:
  serviceName: redis-cluster
  replicas: 6
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
        component: cache
    spec:
      terminationGracePeriodSeconds: 30
      containers:
      - name: redis
        image: redis:7.2-alpine
        ports:
        - containerPort: 6379
          name: redis
        - containerPort: 16379
          name: cluster
        command:
        - redis-server
        - /etc/redis/redis.conf
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: redis-password
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: redis-config
        configMap:
          name: redis-cluster-config
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "gp3-encrypted"
      resources:
        requests:
          storage: 20Gi
---
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster
  namespace: fameduconnect
  labels:
    app: redis-cluster
    component: cache
spec:
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  - port: 16379
    targetPort: 16379
    protocol: TCP
    name: cluster
  selector:
    app: redis-cluster
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster-headless
  namespace: fameduconnect
  labels:
    app: redis-cluster
    component: cache
spec:
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  - port: 16379
    targetPort: 16379
    protocol: TCP
    name: cluster
  selector:
    app: redis-cluster
  type: ClusterIP
  clusterIP: None
---
apiVersion: batch/v1
kind: Job
metadata:
  name: redis-cluster-init
  namespace: fameduconnect
spec:
  template:
    spec:
      containers:
      - name: redis-cluster-init
        image: redis:7.2-alpine
        command:
        - /bin/sh
        - -c
        - |
          # Wait for all Redis pods to be ready
          sleep 30
          
          # Get Redis pod IPs
          REDIS_PODS=$(getent hosts redis-cluster-0.redis-cluster-headless | awk '{print $1}')
          REDIS_PODS="$REDIS_PODS $(getent hosts redis-cluster-1.redis-cluster-headless | awk '{print $1}')"
          REDIS_PODS="$REDIS_PODS $(getent hosts redis-cluster-2.redis-cluster-headless | awk '{print $1}')"
          REDIS_PODS="$REDIS_PODS $(getent hosts redis-cluster-3.redis-cluster-headless | awk '{print $1}')"
          REDIS_PODS="$REDIS_PODS $(getent hosts redis-cluster-4.redis-cluster-headless | awk '{print $1}')"
          REDIS_PODS="$REDIS_PODS $(getent hosts redis-cluster-5.redis-cluster-headless | awk '{print $1}')"
          
          # Create cluster
          echo "yes" | redis-cli --cluster create $REDIS_PODS:6379 --cluster-replicas 1 -a $REDIS_PASSWORD
          
          # Verify cluster
          redis-cli --cluster check redis-cluster-0.redis-cluster-headless:6379 -a $REDIS_PASSWORD
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: redis-password
      restartPolicy: OnFailure 