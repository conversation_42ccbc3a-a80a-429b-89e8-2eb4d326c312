<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FamEduConnect - Admin Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #2d3748;
        }
        
        /* Header */
        .header {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .admin-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .admin-badge {
            background: #e53e3e;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        /* Sidebar */
        .container {
            display: flex;
            min-height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 280px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 2rem 0;
        }
        
        .nav-section {
            margin-bottom: 2rem;
        }
        
        .nav-section-title {
            padding: 0 2rem;
            font-size: 0.8rem;
            font-weight: bold;
            color: #a0aec0;
            text-transform: uppercase;
            margin-bottom: 1rem;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 2rem;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background: #667eea;
            color: white;
        }
        
        .nav-icon {
            font-size: 1.2rem;
        }
        
        /* Main Content */
        .main-content {
            flex: 1;
            padding: 2rem;
        }
        
        .dashboard-header {
            margin-bottom: 2rem;
        }
        
        .dashboard-title {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .dashboard-subtitle {
            color: #718096;
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .stat-card.warning {
            border-left-color: #ed8936;
        }
        
        .stat-card.success {
            border-left-color: #48bb78;
        }
        
        .stat-card.danger {
            border-left-color: #e53e3e;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2d3748;
        }
        
        .stat-label {
            color: #718096;
            font-size: 0.9rem;
        }
        
        .stat-change {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }
        
        .stat-change.positive {
            color: #48bb78;
        }
        
        .stat-change.negative {
            color: #e53e3e;
        }
        
        /* Tables */
        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: #f7fafc;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .table-title {
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-active {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .status-pending {
            background: #feebc8;
            color: #744210;
        }
        
        .status-blocked {
            background: #fed7d7;
            color: #742a2a;
        }
        
        /* Buttons */
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-sm {
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
        }
        
        .btn-danger {
            background: #e53e3e;
        }
        
        .btn-danger:hover {
            background: #c53030;
        }
        
        .btn-warning {
            background: #ed8936;
        }
        
        .btn-warning:hover {
            background: #dd6b20;
        }
        
        /* Charts placeholder */
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #718096;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                display: flex;
                overflow-x: auto;
                padding: 1rem 0;
            }
            
            .nav-section {
                display: flex;
                gap: 1rem;
                margin: 0;
            }
            
            .nav-item {
                white-space: nowrap;
                padding: 0.5rem 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="logo">🎓 FamEduConnect Admin</div>
        <div class="admin-info">
            <span class="admin-badge">ADMIN</span>
            <div>👤 Admin User</div>
        </div>
    </header>

    <div class="container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <div class="nav-section-title">Overview</div>
                <a href="#" class="nav-item active" onclick="showSection('dashboard')">
                    <span class="nav-icon">📊</span>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('analytics')">
                    <span class="nav-icon">📈</span>
                    <span>Analytics</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Management</div>
                <a href="#" class="nav-item" onclick="showSection('schools')">
                    <span class="nav-icon">🏫</span>
                    <span>Manage Schools</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('users')">
                    <span class="nav-icon">👥</span>
                    <span>Manage Users</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('messages')">
                    <span class="nav-icon">💬</span>
                    <span>Message Oversight</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Reports</div>
                <a href="#" class="nav-item" onclick="showSection('reports')">
                    <span class="nav-icon">📋</span>
                    <span>Usage Reports</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('video-logs')">
                    <span class="nav-icon">📹</span>
                    <span>Video Call Logs</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('audit')">
                    <span class="nav-icon">🔍</span>
                    <span>Audit Logs</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">System</div>
                <a href="#" class="nav-item" onclick="showSection('settings')">
                    <span class="nav-icon">⚙️</span>
                    <span>Settings</span>
                </a>
                <a href="#" class="nav-item" onclick="showSection('compliance')">
                    <span class="nav-icon">🛡️</span>
                    <span>Compliance</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="section">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">System Overview</h1>
                    <p class="dashboard-subtitle">Monitor FamEduConnect platform performance and usage</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">Total Users</div>
                        <div class="stat-change positive">↗ +12% this month</div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-number">156</div>
                        <div class="stat-label">Active Schools</div>
                        <div class="stat-change positive">↗ +3 new schools</div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">Messages Today</div>
                        <div class="stat-change positive">↗ +8% from yesterday</div>
                    </div>

                    <div class="stat-card danger">
                        <div class="stat-number">23</div>
                        <div class="stat-label">Flagged Content</div>
                        <div class="stat-change negative">↗ Needs review</div>
                    </div>
                </div>

                <!-- Chart -->
                <div class="chart-container">
                    <div>📊 Usage Analytics Chart<br><small>Real-time data visualization would appear here</small></div>
                </div>

                <!-- Recent Activity Table -->
                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Recent System Activity</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>User</th>
                                <th>Action</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2:34 PM</td>
                                <td>Sarah Johnson (Parent)</td>
                                <td>Started video call with Ms. Rodriguez</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td><button class="btn btn-sm">View</button></td>
                            </tr>
                            <tr>
                                <td>2:28 PM</td>
                                <td>Lincoln Elementary</td>
                                <td>New teacher registration</td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td><button class="btn btn-sm">Approve</button></td>
                            </tr>
                            <tr>
                                <td>2:15 PM</td>
                                <td>System</td>
                                <td>Automated backup completed</td>
                                <td><span class="status-badge status-active">Success</span></td>
                                <td><button class="btn btn-sm">Details</button></td>
                            </tr>
                            <tr>
                                <td>1:45 PM</td>
                                <td>Mike Thompson (Teacher)</td>
                                <td>Flagged message reported</td>
                                <td><span class="status-badge status-blocked">Review</span></td>
                                <td><button class="btn btn-sm btn-warning">Review</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Users Management Section -->
            <div id="users" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">User Management</h1>
                    <p class="dashboard-subtitle">Manage parents, teachers, and student accounts</p>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">User Accounts</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Role</th>
                                <th>School</th>
                                <th>Status</th>
                                <th>Last Active</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Sarah Johnson</td>
                                <td>Parent</td>
                                <td>Lincoln Elementary</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>2 minutes ago</td>
                                <td>
                                    <button class="btn btn-sm">Edit</button>
                                    <button class="btn btn-sm btn-danger">Block</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Ms. Rodriguez</td>
                                <td>Teacher</td>
                                <td>Lincoln Elementary</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>5 minutes ago</td>
                                <td>
                                    <button class="btn btn-sm">Edit</button>
                                    <button class="btn btn-sm btn-danger">Block</button>
                                </td>
                            </tr>
                            <tr>
                                <td>John Smith</td>
                                <td>Parent</td>
                                <td>Washington Middle</td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td>Never</td>
                                <td>
                                    <button class="btn btn-sm">Approve</button>
                                    <button class="btn btn-sm btn-danger">Reject</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- School Administration Interface -->
            <div id="schools" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">School Administration</h1>
                    <p class="dashboard-subtitle">Manage schools, districts, and educational institutions</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">Active Schools</div>
                        <div class="stat-change positive">↗ +3 this month</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">12</div>
                        <div class="stat-label">School Districts</div>
                        <div class="stat-change positive">↗ +1 new district</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">8</div>
                        <div class="stat-label">Pending Approvals</div>
                        <div class="stat-change">Needs review</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">School Directory</h3>
                        <button class="btn">+ Add New School</button>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>School Name</th>
                                <th>District</th>
                                <th>Students</th>
                                <th>Teachers</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Lincoln Elementary School</td>
                                <td>Metro District</td>
                                <td>485</td>
                                <td>24</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>
                                    <button class="btn btn-sm">Edit</button>
                                    <button class="btn btn-sm btn-warning">Settings</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Washington Middle School</td>
                                <td>Metro District</td>
                                <td>672</td>
                                <td>38</td>
                                <td><span class="status-badge status-active">Active</span></td>
                                <td>
                                    <button class="btn btn-sm">Edit</button>
                                    <button class="btn btn-sm btn-warning">Settings</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Roosevelt High School</td>
                                <td>Metro District</td>
                                <td>1,234</td>
                                <td>67</td>
                                <td><span class="status-badge status-pending">Setup</span></td>
                                <td>
                                    <button class="btn btn-sm">Complete Setup</button>
                                    <button class="btn btn-sm btn-danger">Remove</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Content Moderation Interface -->
            <div id="messages" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Content Moderation</h1>
                    <p class="dashboard-subtitle">Monitor and moderate platform communications</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card danger">
                        <div class="stat-number">23</div>
                        <div class="stat-label">Flagged Messages</div>
                        <div class="stat-change">Requires review</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">156</div>
                        <div class="stat-label">Auto-Moderated</div>
                        <div class="stat-change">Last 24 hours</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">99.2%</div>
                        <div class="stat-label">Clean Content</div>
                        <div class="stat-change positive">↗ Improving</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Flagged Content Review</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>From</th>
                                <th>To</th>
                                <th>Content Preview</th>
                                <th>Flag Reason</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>3:45 PM</td>
                                <td>Parent: John Smith</td>
                                <td>Teacher: Ms. Davis</td>
                                <td>"This is completely unacceptable behavior..."</td>
                                <td>Aggressive Language</td>
                                <td>
                                    <button class="btn btn-sm">Approve</button>
                                    <button class="btn btn-sm btn-danger">Block</button>
                                    <button class="btn btn-sm btn-warning">Warn User</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2:30 PM</td>
                                <td>Teacher: Mr. Johnson</td>
                                <td>Parent: Lisa Brown</td>
                                <td>"Meet me after school to discuss..."</td>
                                <td>Inappropriate Contact</td>
                                <td>
                                    <button class="btn btn-sm">Approve</button>
                                    <button class="btn btn-sm btn-danger">Block</button>
                                    <button class="btn btn-sm btn-warning">Warn User</button>
                                </td>
                            </tr>
                            <tr>
                                <td>1:15 PM</td>
                                <td>Parent: Maria Garcia</td>
                                <td>Teacher: Ms. Wilson</td>
                                <td>"[File attachment: personal_info.pdf]"</td>
                                <td>Sensitive Information</td>
                                <td>
                                    <button class="btn btn-sm">Approve</button>
                                    <button class="btn btn-sm btn-danger">Block</button>
                                    <button class="btn btn-sm btn-warning">Warn User</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Analytics and Reporting Interface -->
            <div id="reports" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Usage Reports & Analytics</h1>
                    <p class="dashboard-subtitle">Comprehensive platform usage and performance metrics</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">Daily Active Users</div>
                        <div class="stat-change positive">↗ +15% vs last week</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">4.2M</div>
                        <div class="stat-label">Messages Sent</div>
                        <div class="stat-change positive">↗ +8% this month</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">892</div>
                        <div class="stat-label">Video Calls</div>
                        <div class="stat-change positive">↗ +23% this week</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">67</div>
                        <div class="stat-label">Languages Used</div>
                        <div class="stat-change">Global reach</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div>📊 User Engagement Over Time<br><small>Interactive charts showing daily/weekly/monthly trends</small></div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Top Performing Schools</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>School</th>
                                <th>Active Users</th>
                                <th>Messages/Day</th>
                                <th>Video Calls/Week</th>
                                <th>Engagement Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Lincoln Elementary</td>
                                <td>485</td>
                                <td>1,234</td>
                                <td>67</td>
                                <td><span style="color: #48bb78; font-weight: bold;">95%</span></td>
                            </tr>
                            <tr>
                                <td>Washington Middle</td>
                                <td>672</td>
                                <td>1,567</td>
                                <td>89</td>
                                <td><span style="color: #48bb78; font-weight: bold;">92%</span></td>
                            </tr>
                            <tr>
                                <td>Roosevelt High</td>
                                <td>1,234</td>
                                <td>2,345</td>
                                <td>134</td>
                                <td><span style="color: #ed8936; font-weight: bold;">78%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Video Call Monitoring Interface -->
            <div id="video-logs" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Video Call Monitoring</h1>
                    <p class="dashboard-subtitle">Monitor video calls, recordings, and compliance</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">23</div>
                        <div class="stat-label">Active Calls</div>
                        <div class="stat-change">Right now</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">892</div>
                        <div class="stat-label">Calls Today</div>
                        <div class="stat-change positive">↗ +12% vs yesterday</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">45min</div>
                        <div class="stat-label">Avg Call Duration</div>
                        <div class="stat-change">Optimal range</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">99.8%</div>
                        <div class="stat-label">Call Quality</div>
                        <div class="stat-change positive">Excellent</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Live Video Calls</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Participants</th>
                                <th>School</th>
                                <th>Duration</th>
                                <th>Quality</th>
                                <th>Translation</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Sarah Johnson ↔ Ms. Rodriguez</td>
                                <td>Lincoln Elementary</td>
                                <td>12:34</td>
                                <td><span style="color: #48bb78;">HD</span></td>
                                <td>EN → ES</td>
                                <td>
                                    <button class="btn btn-sm">Monitor</button>
                                    <button class="btn btn-sm btn-danger">End Call</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Mike Thompson ↔ Lisa Brown</td>
                                <td>Washington Middle</td>
                                <td>8:45</td>
                                <td><span style="color: #48bb78;">HD</span></td>
                                <td>EN → AR</td>
                                <td>
                                    <button class="btn btn-sm">Monitor</button>
                                    <button class="btn btn-sm btn-danger">End Call</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="data-table" style="margin-top: 2rem;">
                    <div class="table-header">
                        <h3 class="table-title">Call History & Recordings</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Date/Time</th>
                                <th>Participants</th>
                                <th>Duration</th>
                                <th>Recording</th>
                                <th>Transcript</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Jan 20, 2:30 PM</td>
                                <td>John Smith ↔ Ms. Davis</td>
                                <td>23:45</td>
                                <td><span class="status-badge status-active">Available</span></td>
                                <td><span class="status-badge status-active">Generated</span></td>
                                <td>
                                    <button class="btn btn-sm">View</button>
                                    <button class="btn btn-sm">Download</button>
                                </td>
                            </tr>
                            <tr>
                                <td>Jan 20, 1:15 PM</td>
                                <td>Maria Garcia ↔ Mr. Wilson</td>
                                <td>18:30</td>
                                <td><span class="status-badge status-active">Available</span></td>
                                <td><span class="status-badge status-active">Generated</span></td>
                                <td>
                                    <button class="btn btn-sm">View</button>
                                    <button class="btn btn-sm">Download</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Blockchain Audit Trail Interface -->
            <div id="audit" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Blockchain Audit Trail</h1>
                    <p class="dashboard-subtitle">Immutable security and compliance logging</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">1,234,567</div>
                        <div class="stat-label">Total Transactions</div>
                        <div class="stat-change">Blockchain verified</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Integrity Score</div>
                        <div class="stat-change positive">All verified</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Tampered Records</div>
                        <div class="stat-change">Secure</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2.3TB</div>
                        <div class="stat-label">Data Secured</div>
                        <div class="stat-change">Encrypted</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Recent Blockchain Transactions</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Block Hash</th>
                                <th>Timestamp</th>
                                <th>Action Type</th>
                                <th>User</th>
                                <th>Verification</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>0x7a8b9c...def123</td>
                                <td>2025-01-21 15:34:22</td>
                                <td>Message Sent</td>
                                <td><EMAIL></td>
                                <td><span class="status-badge status-active">✓ Verified</span></td>
                                <td><button class="btn btn-sm">View Details</button></td>
                            </tr>
                            <tr>
                                <td>0x6f7e8d...abc456</td>
                                <td>2025-01-21 15:33:45</td>
                                <td>Video Call Started</td>
                                <td><EMAIL></td>
                                <td><span class="status-badge status-active">✓ Verified</span></td>
                                <td><button class="btn btn-sm">View Details</button></td>
                            </tr>
                            <tr>
                                <td>0x5e6d7c...789def</td>
                                <td>2025-01-21 15:32:10</td>
                                <td>Grade Updated</td>
                                <td><EMAIL></td>
                                <td><span class="status-badge status-active">✓ Verified</span></td>
                                <td><button class="btn btn-sm">View Details</button></td>
                            </tr>
                            <tr>
                                <td>0x4d5c6b...456abc</td>
                                <td>2025-01-21 15:31:33</td>
                                <td>User Login</td>
                                <td><EMAIL></td>
                                <td><span class="status-badge status-active">✓ Verified</span></td>
                                <td><button class="btn btn-sm">View Details</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="chart-container">
                    <div>⛓️ Blockchain Network Health<br><small>Real-time blockchain network status and verification metrics</small></div>
                </div>
            </div>

            <!-- Advanced Analytics Dashboard -->
            <div id="analytics" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Advanced Analytics Dashboard</h1>
                    <p class="dashboard-subtitle">Deep insights into platform performance and user behavior</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">Total Users</div>
                        <div class="stat-change positive">↗ +12% MoM</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">89.3%</div>
                        <div class="stat-label">User Retention</div>
                        <div class="stat-change positive">↗ +3.2% vs last month</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">4.7</div>
                        <div class="stat-label">Avg Session Duration</div>
                        <div class="stat-change">Hours per session</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">67</div>
                        <div class="stat-label">Countries</div>
                        <div class="stat-change positive">Global reach</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div class="chart-container">
                        <div>📈 User Growth Trends<br><small>Monthly active users over time</small></div>
                    </div>
                    <div class="chart-container">
                        <div>🌍 Geographic Distribution<br><small>Users by country and region</small></div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Feature Usage Analytics</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Feature</th>
                                <th>Daily Users</th>
                                <th>Usage Frequency</th>
                                <th>Satisfaction Score</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>💬 Messaging</td>
                                <td>2,456</td>
                                <td>8.3x per day</td>
                                <td>4.8/5.0</td>
                                <td><span style="color: #48bb78;">↗ +15%</span></td>
                            </tr>
                            <tr>
                                <td>📹 Video Calls</td>
                                <td>892</td>
                                <td>2.1x per week</td>
                                <td>4.6/5.0</td>
                                <td><span style="color: #48bb78;">↗ +23%</span></td>
                            </tr>
                            <tr>
                                <td>📚 Grade Tracking</td>
                                <td>1,678</td>
                                <td>5.2x per week</td>
                                <td>4.7/5.0</td>
                                <td><span style="color: #48bb78;">↗ +8%</span></td>
                            </tr>
                            <tr>
                                <td>🌍 Translation</td>
                                <td>1,234</td>
                                <td>12.4x per day</td>
                                <td>4.9/5.0</td>
                                <td><span style="color: #48bb78;">↗ +34%</span></td>
                            </tr>
                            <tr>
                                <td>📅 Calendar</td>
                                <td>567</td>
                                <td>3.1x per week</td>
                                <td>4.2/5.0</td>
                                <td><span style="color: #ed8936;">↘ -5%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="data-table" style="margin-top: 2rem;">
                    <div class="table-header">
                        <h3 class="table-title">Language Usage Statistics</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Language</th>
                                <th>Users</th>
                                <th>Messages</th>
                                <th>Video Calls</th>
                                <th>Growth Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>🇺🇸 English</td>
                                <td>1,456</td>
                                <td>2.1M</td>
                                <td>567</td>
                                <td><span style="color: #48bb78;">+8%</span></td>
                            </tr>
                            <tr>
                                <td>🇪🇸 Spanish</td>
                                <td>678</td>
                                <td>890K</td>
                                <td>234</td>
                                <td><span style="color: #48bb78;">+23%</span></td>
                            </tr>
                            <tr>
                                <td>🇸🇦 Arabic</td>
                                <td>345</td>
                                <td>456K</td>
                                <td>123</td>
                                <td><span style="color: #48bb78;">+45%</span></td>
                            </tr>
                            <tr>
                                <td>🇫🇷 French</td>
                                <td>234</td>
                                <td>312K</td>
                                <td>89</td>
                                <td><span style="color: #48bb78;">+12%</span></td>
                            </tr>
                            <tr>
                                <td>🇨🇳 Chinese</td>
                                <td>134</td>
                                <td>178K</td>
                                <td>45</td>
                                <td><span style="color: #48bb78;">+67%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- System Settings -->
            <div id="settings" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">System Settings</h1>
                    <p class="dashboard-subtitle">Configure platform settings and preferences</p>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div class="data-table">
                        <div class="table-header">
                            <h3 class="table-title">Security Settings</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Two-Factor Authentication</label>
                                <select class="btn" style="width: 100%;">
                                    <option>Required for all users</option>
                                    <option>Optional</option>
                                    <option>Disabled</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Session Timeout</label>
                                <select class="btn" style="width: 100%;">
                                    <option>30 minutes</option>
                                    <option>1 hour</option>
                                    <option>4 hours</option>
                                    <option>8 hours</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Password Policy</label>
                                <select class="btn" style="width: 100%;">
                                    <option>Strong (12+ chars, mixed case, numbers, symbols)</option>
                                    <option>Medium (8+ chars, mixed case, numbers)</option>
                                    <option>Basic (6+ chars)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="data-table">
                        <div class="table-header">
                            <h3 class="table-title">Platform Settings</h3>
                        </div>
                        <div style="padding: 1.5rem;">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Default Language</label>
                                <select class="btn" style="width: 100%;">
                                    <option>English</option>
                                    <option>Spanish</option>
                                    <option>Arabic</option>
                                    <option>French</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">File Upload Limit</label>
                                <select class="btn" style="width: 100%;">
                                    <option>10 MB</option>
                                    <option>25 MB</option>
                                    <option>50 MB</option>
                                    <option>100 MB</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Video Call Quality</label>
                                <select class="btn" style="width: 100%;">
                                    <option>Auto (Adaptive)</option>
                                    <option>HD (720p)</option>
                                    <option>Full HD (1080p)</option>
                                    <option>4K (2160p)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compliance Dashboard -->
            <div id="compliance" class="section" style="display: none;">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Compliance Dashboard</h1>
                    <p class="dashboard-subtitle">FERPA, HIPAA, GDPR compliance monitoring and reporting</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card success">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">FERPA Compliance</div>
                        <div class="stat-change positive">✓ Fully compliant</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">HIPAA Compliance</div>
                        <div class="stat-change positive">✓ Fully compliant</div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">GDPR Compliance</div>
                        <div class="stat-change positive">✓ Fully compliant</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Violations</div>
                        <div class="stat-change">Clean record</div>
                    </div>
                </div>

                <div class="data-table">
                    <div class="table-header">
                        <h3 class="table-title">Compliance Audit Log</h3>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Regulation</th>
                                <th>Audit Type</th>
                                <th>Result</th>
                                <th>Actions Taken</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Jan 15, 2025</td>
                                <td>FERPA</td>
                                <td>Quarterly Review</td>
                                <td>Pass</td>
                                <td>Updated privacy policies</td>
                                <td><span class="status-badge status-active">Complete</span></td>
                            </tr>
                            <tr>
                                <td>Jan 10, 2025</td>
                                <td>GDPR</td>
                                <td>Data Processing Audit</td>
                                <td>Pass</td>
                                <td>Enhanced consent mechanisms</td>
                                <td><span class="status-badge status-active">Complete</span></td>
                            </tr>
                            <tr>
                                <td>Jan 5, 2025</td>
                                <td>HIPAA</td>
                                <td>Security Assessment</td>
                                <td>Pass</td>
                                <td>Updated encryption protocols</td>
                                <td><span class="status-badge status-active">Complete</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="chart-container">
                    <div>📋 Compliance Score Trends<br><small>Historical compliance scores and improvement metrics</small></div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });
            
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).style.display = 'block';
            
            // Add active class to clicked nav item
            event.target.closest('.nav-item').classList.add('active');
        }
    </script>
</body>
</html>