const axios = require('axios');
const fs = require('fs');

// Configuration
const CONFIG = {
  BACKEND_URL: 'http://localhost:3002',
  FRONTEND_URL: 'http://localhost:3000',
  TEST_DURATION: 120000, // 2 minutes
  CONCURRENT_USERS: 20,
  REQUESTS_PER_SECOND: 30,
  TIMEOUT: 8000
};

// Test Results
const results = {
  backend: {
    health: { success: 0, failed: 0 },
    auth: { success: 0, failed: 0 },
    api: { success: 0, failed: 0 }
  },
  frontend: {
    pages: { success: 0, failed: 0 },
    static: { success: 0, failed: 0 }
  },
  performance: {
    backend: { responseTimes: [], avgResponseTime: 0, maxResponseTime: 0, minResponseTime: Infinity },
    frontend: { responseTimes: [], avgResponseTime: 0, maxResponseTime: 0, minResponseTime: Infinity }
  },
  startTime: Date.now()
};

// Test data
const TEST_CREDENTIALS = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' }
];

const BACKEND_ENDPOINTS = ['/api/health', '/api/test', '/api/auth/login', '/api/users/profile'];
const FRONTEND_PAGES = ['/', '/login', '/dashboard', '/messages', '/profile'];

// Performance tracking
function updateBackendPerformance(responseTime) {
  results.performance.backend.responseTimes.push(responseTime);
  const times = results.performance.backend.responseTimes;
  results.performance.backend.avgResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
  results.performance.backend.maxResponseTime = Math.max(results.performance.backend.maxResponseTime, responseTime);
  results.performance.backend.minResponseTime = Math.min(results.performance.backend.minResponseTime, responseTime);
}

function updateFrontendPerformance(responseTime) {
  results.performance.frontend.responseTimes.push(responseTime);
  const times = results.performance.frontend.responseTimes;
  results.performance.frontend.avgResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
  results.performance.frontend.maxResponseTime = Math.max(results.performance.frontend.maxResponseTime, responseTime);
  results.performance.frontend.minResponseTime = Math.min(results.performance.frontend.minResponseTime, responseTime);
}

// Backend stress tests
async function stressTestBackend() {
  console.log('🔧 Starting Backend Stress Tests...');
  
  // Health endpoint test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 3); i++) {
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, { timeout: CONFIG.TIMEOUT });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.backend.health.success++;
          updateBackendPerformance(responseTime);
        } else {
          results.backend.health.failed++;
        }
      } catch (error) {
        results.backend.health.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
  
  // Auth endpoint test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 3); i++) {
    const credentials = TEST_CREDENTIALS[i % TEST_CREDENTIALS.length];
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.post(`${CONFIG.BACKEND_URL}/api/auth/login`, credentials, {
          timeout: CONFIG.TIMEOUT,
          headers: { 'Content-Type': 'application/json' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.backend.auth.success++;
          updateBackendPerformance(responseTime);
        } else {
          results.backend.auth.failed++;
        }
      } catch (error) {
        results.backend.auth.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
  
  // API endpoints test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 3); i++) {
    setInterval(async () => {
      try {
        const endpoint = BACKEND_ENDPOINTS[Math.floor(Math.random() * BACKEND_ENDPOINTS.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}${endpoint}`, { timeout: CONFIG.TIMEOUT });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.backend.api.success++;
          updateBackendPerformance(responseTime);
        } else {
          results.backend.api.failed++;
        }
      } catch (error) {
        results.backend.api.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Frontend stress tests
async function stressTestFrontend() {
  console.log('📱 Starting Frontend Stress Tests...');
  
  // Pages test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 2); i++) {
    setInterval(async () => {
      try {
        const page = FRONTEND_PAGES[Math.floor(Math.random() * FRONTEND_PAGES.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.FRONTEND_URL}${page}`, {
          timeout: CONFIG.TIMEOUT,
          headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.frontend.pages.success++;
          updateFrontendPerformance(responseTime);
        } else {
          results.frontend.pages.failed++;
        }
      } catch (error) {
        results.frontend.pages.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
  
  // Static resources test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 4); i++) {
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.FRONTEND_URL}/favicon.ico`, { timeout: CONFIG.TIMEOUT });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 400) {
          results.frontend.static.success++;
          updateFrontendPerformance(responseTime);
        } else {
          results.frontend.static.failed++;
        }
      } catch (error) {
        results.frontend.static.failed++;
      }
    }, 1000 / (CONFIG.REQUESTS_PER_SECOND / 2));
  }
}

// System health checks
async function performHealthChecks() {
  console.log('🏥 Performing System Health Checks...');
  
  try {
    const backendHealth = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, { timeout: 5000 });
    console.log('✅ Backend is healthy');
    console.log(`📊 Backend: ${JSON.stringify(backendHealth.data)}`);
  } catch (error) {
    console.log('❌ Backend health check failed:', error.message);
    return false;
  }
  
  try {
    const frontendHealth = await axios.get(`${CONFIG.FRONTEND_URL}/`, { timeout: 5000 });
    console.log('✅ Frontend is healthy');
    console.log(`📊 Frontend response size: ${frontendHealth.data.length} bytes`);
  } catch (error) {
    console.log('❌ Frontend health check failed:', error.message);
    return false;
  }
  
  return true;
}

// Generate comprehensive report
function generateComprehensiveReport() {
  const totalTime = Date.now() - results.startTime;
  
  const backendTotal = results.backend.health.success + results.backend.health.failed +
                      results.backend.auth.success + results.backend.auth.failed +
                      results.backend.api.success + results.backend.api.failed;
  
  const frontendTotal = results.frontend.pages.success + results.frontend.pages.failed +
                       results.frontend.static.success + results.frontend.static.failed;
  
  const backendSuccess = results.backend.health.success + results.backend.auth.success + results.backend.api.success;
  const frontendSuccess = results.frontend.pages.success + results.frontend.static.success;
  
  const totalRequests = backendTotal + frontendTotal;
  const totalSuccess = backendSuccess + frontendSuccess;
  
  return {
    timestamp: new Date().toISOString(),
    duration: Math.round(totalTime / 1000),
    totalRequests,
    requestsPerSecond: Math.round(totalRequests / (totalTime / 1000)),
    overallSuccessRate: Math.round((totalSuccess / totalRequests) * 100),
    backend: {
      totalRequests: backendTotal,
      successRate: Math.round((backendSuccess / backendTotal) * 100),
      performance: {
        avgResponseTime: Math.round(results.performance.backend.avgResponseTime),
        minResponseTime: Math.round(results.performance.backend.minResponseTime),
        maxResponseTime: Math.round(results.performance.backend.maxResponseTime)
      },
      results: results.backend
    },
    frontend: {
      totalRequests: frontendTotal,
      successRate: Math.round((frontendSuccess / frontendTotal) * 100),
      performance: {
        avgResponseTime: Math.round(results.performance.frontend.avgResponseTime),
        minResponseTime: Math.round(results.performance.frontend.minResponseTime),
        maxResponseTime: Math.round(results.performance.frontend.maxResponseTime)
      },
      results: results.frontend
    }
  };
}

// Main comprehensive stress test
async function runComprehensiveStressTest() {
  console.log('🚀 Starting Comprehensive Stress Test...');
  console.log(`⏱️  Duration: ${CONFIG.TEST_DURATION / 1000}s`);
  console.log(`👥 Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`📡 Requests/Second: ${CONFIG.REQUESTS_PER_SECOND}`);
  console.log('');
  
  // Health checks
  const isHealthy = await performHealthChecks();
  if (!isHealthy) {
    console.log('❌ System is not healthy. Aborting stress test.');
    process.exit(1);
  }
  console.log('');
  
  // Start stress tests
  stressTestBackend();
  stressTestFrontend();
  
  // Progress reporting
  const progressInterval = setInterval(() => {
    const elapsed = Date.now() - results.startTime;
    const progress = Math.round((elapsed / CONFIG.TEST_DURATION) * 100);
    
    const backendTotal = results.backend.health.success + results.backend.health.failed +
                        results.backend.auth.success + results.backend.auth.failed +
                        results.backend.api.success + results.backend.api.failed;
    
    const frontendTotal = results.frontend.pages.success + results.frontend.pages.failed +
                         results.frontend.static.success + results.frontend.static.failed;
    
    console.log(`📊 Progress: ${progress}% | Backend: ${backendTotal} | Frontend: ${frontendTotal} | Total: ${backendTotal + frontendTotal}`);
  }, 20000);
  
  // Complete test
  setTimeout(() => {
    clearInterval(progressInterval);
    
    const report = generateComprehensiveReport();
    
    console.log('\n' + '='.repeat(80));
    console.log('📈 COMPREHENSIVE STRESS TEST RESULTS');
    console.log('='.repeat(80));
    console.log(`⏱️  Duration: ${report.duration}s`);
    console.log(`📡 Total Requests: ${report.totalRequests}`);
    console.log(`⚡ Requests/Second: ${report.requestsPerSecond}`);
    console.log(`✅ Overall Success Rate: ${report.overallSuccessRate}%`);
    console.log('');
    
    console.log('🔧 BACKEND RESULTS:');
    console.log(`   📡 Total Requests: ${report.backend.totalRequests}`);
    console.log(`   ✅ Success Rate: ${report.backend.successRate}%`);
    console.log(`   📊 Avg Response: ${report.backend.performance.avgResponseTime}ms`);
    console.log(`   🏥 Health: ${results.backend.health.success}✅ / ${results.backend.health.failed}❌`);
    console.log(`   🔐 Auth: ${results.backend.auth.success}✅ / ${results.backend.auth.failed}❌`);
    console.log(`   🔧 API: ${results.backend.api.success}✅ / ${results.backend.api.failed}❌`);
    console.log('');
    
    console.log('📱 FRONTEND RESULTS:');
    console.log(`   📡 Total Requests: ${report.frontend.totalRequests}`);
    console.log(`   ✅ Success Rate: ${report.frontend.successRate}%`);
    console.log(`   📊 Avg Response: ${report.frontend.performance.avgResponseTime}ms`);
    console.log(`   📱 Pages: ${results.frontend.pages.success}✅ / ${results.frontend.pages.failed}❌`);
    console.log(`   🎨 Static: ${results.frontend.static.success}✅ / ${results.frontend.static.failed}❌`);
    console.log('');
    
    // Recommendations
    console.log('💡 RECOMMENDATIONS:');
    if (report.backend.successRate < 90) {
      console.log('   • Backend needs optimization - consider connection pooling and caching');
    }
    if (report.frontend.successRate < 90) {
      console.log('   • Frontend needs optimization - consider CDN and static asset optimization');
    }
    if (report.backend.performance.avgResponseTime > 1000) {
      console.log('   • Backend response times are slow - investigate database queries and API performance');
    }
    if (report.frontend.performance.avgResponseTime > 2000) {
      console.log('   • Frontend response times are slow - optimize bundle size and server configuration');
    }
    if (report.overallSuccessRate >= 95) {
      console.log('   • System is performing excellently under stress! 🎉');
    }
    
    console.log('\n' + '='.repeat(80));
    
    // Save report
    fs.writeFileSync('comprehensive-stress-test-report.json', JSON.stringify(report, null, 2));
    console.log('📄 Comprehensive report saved to: comprehensive-stress-test-report.json');
    
    process.exit(0);
  }, CONFIG.TEST_DURATION);
}

// Run the comprehensive test
runComprehensiveStressTest().catch(console.error);
