# FamEduConnect Production Deployment Script
# This script deploys the complete application to production

param(
    [string]$Environment = "production",
    [switch]$SkipTests,
    [switch]$SkipMobile,
    [switch]$Verbose
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "================================`n$Message`n================================" -ForegroundColor Blue
}

# Function to run command and check result
function Invoke-CommandWithCheck {
    param(
        [string]$Command,
        [string]$Description
    )
    
    Write-Host "Running: $Description" -ForegroundColor Cyan
    if ($Verbose) {
        Write-Host "Command: $Command" -ForegroundColor Gray
    }
    
    try {
        $result = Invoke-Expression $Command 2>&1
        $success = $LASTEXITCODE -eq 0
        
        if ($success) {
            Write-Host "✓ $Description completed successfully" -ForegroundColor Green
        } else {
            Write-Host "✗ $Description failed" -ForegroundColor Red
            if ($Verbose) {
                Write-Host $result -ForegroundColor Red
            }
        }
        
        return $success
    } catch {
        Write-Host "✗ $Description failed with error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to check prerequisites
function Test-Prerequisites {
    Write-Header "Checking Prerequisites"
    
    $prerequisites = @(
        @{ Command = "node --version"; Description = "Node.js" },
        @{ Command = "npm --version"; Description = "npm" },
        @{ Command = "git --version"; Description = "Git" },
        @{ Command = "docker --version"; Description = "Docker" }
    )
    
    $allPassed = $true
    foreach ($prereq in $prerequisites) {
        if (-not (Invoke-CommandWithCheck $prereq.Command $prereq.Description)) {
            $allPassed = $false
        }
    }
    
    return $allPassed
}

# Function to install dependencies
function Install-Dependencies {
    Write-Header "Installing Dependencies"
    
    $installCommands = @(
        @{ Command = "npm run install:all"; Description = "Install all dependencies" },
        @{ Command = "cd backend && npm install"; Description = "Install backend dependencies" },
        @{ Command = "cd frontend && npm install"; Description = "Install frontend dependencies" },
        @{ Command = "cd admin && npm install"; Description = "Install admin dependencies" }
    )
    
    $allPassed = $true
    foreach ($install in $installCommands) {
        if (-not (Invoke-CommandWithCheck $install.Command $install.Description)) {
            $allPassed = $false
        }
    }
    
    return $allPassed
}

# Function to run tests
function Run-Tests {
    if ($SkipTests) {
        Write-Warning "Skipping tests as requested"
        return $true
    }
    
    Write-Header "Running Tests"
    
    $testCommands = @(
        @{ Command = "npm run test"; Description = "Run all tests" },
        @{ Command = "cd backend && npm test"; Description = "Run backend tests" },
        @{ Command = "cd frontend && npm test"; Description = "Run frontend tests" }
    )
    
    $allPassed = $true
    foreach ($test in $testCommands) {
        if (-not (Invoke-CommandWithCheck $test.Command $test.Description)) {
            $allPassed = $false
        }
    }
    
    return $allPassed
}

# Function to build applications
function Build-Applications {
    Write-Header "Building Applications"
    
    $buildCommands = @(
        @{ Command = "cd backend && npm run build"; Description = "Build backend" },
        @{ Command = "cd frontend && npm run build"; Description = "Build frontend" },
        @{ Command = "cd admin && npm run build"; Description = "Build admin dashboard" }
    )
    
    $allPassed = $true
    foreach ($build in $buildCommands) {
        if (-not (Invoke-CommandWithCheck $build.Command $build.Description)) {
            $allPassed = $false
        }
    }
    
    return $allPassed
}

# Function to deploy backend
function Deploy-Backend {
    Write-Header "Deploying Backend"
    
    # Check if using enterprise deployment
    if (Test-Path "enterprise/scripts/deploy-monitoring.ps1") {
        Write-Status "Using enterprise deployment"
        
        $enterpriseCommands = @(
            @{ Command = "cd enterprise && .\scripts\setup-monitoring.ps1 -Environment $Environment"; Description = "Setup monitoring" },
            @{ Command = "cd enterprise/monitoring/$Environment && .\deploy-monitoring.ps1"; Description = "Deploy monitoring" }
        )
        
        foreach ($cmd in $enterpriseCommands) {
            if (-not (Invoke-CommandWithCheck $cmd.Command $cmd.Description)) {
                return $false
            }
        }
    } else {
        Write-Status "Using standard deployment"
        
        $standardCommands = @(
            @{ Command = "cd backend && npm run deploy:prod"; Description = "Deploy backend to production" }
        )
        
        foreach ($cmd in $standardCommands) {
            if (-not (Invoke-CommandWithCheck $cmd.Command $cmd.Description)) {
                return $false
            }
        }
    }
    
    return $true
}

# Function to deploy frontend
function Deploy-Frontend {
    Write-Header "Deploying Frontend"
    
    $frontendCommands = @(
        @{ Command = "cd frontend && vercel --prod"; Description = "Deploy frontend to Vercel" }
    )
    
    $allPassed = $true
    foreach ($deploy in $frontendCommands) {
        if (-not (Invoke-CommandWithCheck $deploy.Command $deploy.Description)) {
            $allPassed = $false
        }
    }
    
    return $allPassed
}

# Function to deploy admin dashboard
function Deploy-Admin {
    Write-Header "Deploying Admin Dashboard"
    
    $adminCommands = @(
        @{ Command = "cd admin && vercel --prod"; Description = "Deploy admin dashboard to Vercel" }
    )
    
    $allPassed = $true
    foreach ($deploy in $adminCommands) {
        if (-not (Invoke-CommandWithCheck $deploy.Command $deploy.Description)) {
            $allPassed = $false
        }
    }
    
    return $allPassed
}

# Function to build mobile app
function Build-Mobile {
    if ($SkipMobile) {
        Write-Warning "Skipping mobile build as requested"
        return $true
    }
    
    Write-Header "Building Mobile App"
    
    $mobileCommands = @(
        @{ Command = "cd mobile && npm install"; Description = "Install mobile dependencies" },
        @{ Command = "cd mobile && npx expo build:android"; Description = "Build Android app" },
        @{ Command = "cd mobile && npx expo build:ios"; Description = "Build iOS app" }
    )
    
    $allPassed = $true
    foreach ($build in $mobileCommands) {
        if (-not (Invoke-CommandWithCheck $build.Command $build.Description)) {
            $allPassed = $false
        }
    }
    
    return $allPassed
}

# Function to run health checks
function Test-HealthChecks {
    Write-Header "Running Health Checks"
    
    # Wait for services to be ready
    Write-Status "Waiting for services to be ready..."
    Start-Sleep -Seconds 30
    
    $healthChecks = @(
        @{ Command = "curl -f http://localhost:5555/health"; Description = "Backend health check" },
        @{ Command = "curl -f http://localhost:5555/api/test"; Description = "Backend API test" }
    )
    
    $allPassed = $true
    foreach ($check in $healthChecks) {
        if (-not (Invoke-CommandWithCheck $check.Command $check.Description)) {
            $allPassed = $false
        }
    }
    
    return $allPassed
}

# Function to generate deployment report
function New-DeploymentReport {
    param(
        [hashtable]$Results
    )
    
    $reportPath = "deployment-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').md"
    
    $report = @"
# FamEduConnect Production Deployment Report
Generated on: $(Get-Date)

## Deployment Summary

| Component | Status | Details |
|-----------|--------|---------|
"@
    
    foreach ($component in $Results.Keys) {
        $status = if ($Results[$component]) { "✅ SUCCESS" } else { "❌ FAILED" }
        $report += "`n| $component | $status | |"
    }
    
    $report += @"

## Environment Information
- Environment: $Environment
- Deployment Date: $(Get-Date)
- Node.js Version: $(node --version)
- npm Version: $(npm --version)

## Deployment URLs
- Frontend: https://fameduconnect.vercel.app
- Admin Dashboard: https://fameduconnect-admin.vercel.app
- API: https://api.fameduconnect.com

## Next Steps
"@
    
    $failedComponents = $Results.Keys | Where-Object { -not $Results[$_] }
    if ($failedComponents) {
        $report += "`n**Failed Components:**`n"
        foreach ($component in $failedComponents) {
            $report += "- $component`n"
        }
        $report += "`n**Action Required:** Review and fix the failed components before proceeding.`n"
    } else {
        $report += "`n**All components deployed successfully!** The application is ready for production use.`n"
    }
    
    Set-Content $reportPath $report
    Write-Status "Deployment report generated: $reportPath"
    return $reportPath
}

# Main deployment function
function Main {
    Write-Header "FamEduConnect Production Deployment"
    
    # Check if running from the correct directory
    if (-not (Test-Path "package.json")) {
        Write-Error "Please run this script from the FamEduConnect_Full_Codebase directory"
        exit 1
    }
    
    Write-Status "Starting production deployment for environment: $Environment"
    
    # Initialize results
    $deploymentResults = @{}
    
    # Step 1: Check prerequisites
    $deploymentResults["Prerequisites"] = Test-Prerequisites
    if (-not $deploymentResults["Prerequisites"]) {
        Write-Error "Prerequisites check failed. Please install required tools."
        exit 1
    }
    
    # Step 2: Install dependencies
    $deploymentResults["Dependencies"] = Install-Dependencies
    if (-not $deploymentResults["Dependencies"]) {
        Write-Error "Dependency installation failed."
        exit 1
    }
    
    # Step 3: Run tests
    $deploymentResults["Tests"] = Run-Tests
    if (-not $deploymentResults["Tests"]) {
        Write-Warning "Some tests failed. Continuing with deployment..."
    }
    
    # Step 4: Build applications
    $deploymentResults["Build"] = Build-Applications
    if (-not $deploymentResults["Build"]) {
        Write-Error "Build failed. Cannot proceed with deployment."
        exit 1
    }
    
    # Step 5: Deploy backend
    $deploymentResults["Backend"] = Deploy-Backend
    if (-not $deploymentResults["Backend"]) {
        Write-Error "Backend deployment failed."
        exit 1
    }
    
    # Step 6: Deploy frontend
    $deploymentResults["Frontend"] = Deploy-Frontend
    if (-not $deploymentResults["Frontend"]) {
        Write-Error "Frontend deployment failed."
        exit 1
    }
    
    # Step 7: Deploy admin dashboard
    $deploymentResults["Admin"] = Deploy-Admin
    if (-not $deploymentResults["Admin"]) {
        Write-Error "Admin dashboard deployment failed."
        exit 1
    }
    
    # Step 8: Build mobile app
    $deploymentResults["Mobile"] = Build-Mobile
    if (-not $deploymentResults["Mobile"]) {
        Write-Warning "Mobile build failed. Web application is still functional."
    }
    
    # Step 9: Health checks
    $deploymentResults["Health Checks"] = Test-HealthChecks
    if (-not $deploymentResults["Health Checks"]) {
        Write-Warning "Some health checks failed. Please verify manually."
    }
    
    # Generate report
    $reportPath = New-DeploymentReport $deploymentResults
    
    # Summary
    Write-Header "Deployment Summary"
    
    $passedComponents = ($deploymentResults.Values | Where-Object { $_ }).Count
    $totalComponents = $deploymentResults.Count
    $passRate = ($passedComponents / $totalComponents) * 100
    
    Write-Status "Deployment completed: $passedComponents/$totalComponents components successful ($([math]::Round($passRate, 1))%)"
    
    if ($passRate -eq 100) {
        Write-Status "🎉 All components deployed successfully! FamEduConnect is now live!"
    } elseif ($passRate -ge 80) {
        Write-Warning "⚠️  Most components deployed successfully, but some issues need attention."
    } else {
        Write-Error "❌ Multiple deployment failures detected. Review the report and fix issues."
    }
    
    Write-Status "Deployment report available at: $reportPath"
    
    # Exit with appropriate code
    if ($passRate -eq 100) {
        exit 0
    } else {
        exit 1
    }
}

# Run main function
Main 