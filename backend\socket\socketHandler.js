const jwt = require('jsonwebtoken');
const { User, Message, VideoCall } = require('../models');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

const socketHandler = (io) => {
  // Authentication middleware for socket connections
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, JWT_SECRET);
      const user = await User.findByPk(decoded.userId);

      if (!user || !user.isActive) {
        return next(new Error('Authentication error'));
      }

      socket.userId = user.id;
      socket.userRole = user.role;
      socket.preferredLanguage = user.preferredLanguage;
      
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User ${socket.userId} connected`);

    // Join user to their personal room
    socket.join(`user_${socket.userId}`);

    // Join user to their class rooms (for teachers and students)
    socket.on('join_classes', async (classIds) => {
      try {
        // Verify user has access to these classes
        for (const classId of classIds) {
          socket.join(`class_${classId}`);
        }
        console.log(`User ${socket.userId} joined classes: ${classIds.join(', ')}`);
      } catch (error) {
        console.error('Join classes error:', error);
      }
    });

    // Handle real-time messaging
    socket.on('send_message', async (data) => {
      try {
        const { recipientId, classId, content, messageType = 'text' } = data;

        // Create message in database
        const message = await Message.create({
          senderId: socket.userId,
          recipientId,
          classId,
          content,
          messageType,
          isSent: true,
          sentAt: new Date()
        });

        // Load full message with associations
        const fullMessage = await Message.findByPk(message.id, {
          include: [
            {
              model: User,
              as: 'sender',
              attributes: ['id', 'firstName', 'lastName', 'profilePicture']
            },
            {
              model: User,
              as: 'recipient',
              attributes: ['id', 'firstName', 'lastName', 'profilePicture']
            }
          ]
        });

        // Emit to recipient or class
        if (recipientId) {
          io.to(`user_${recipientId}`).emit('new_message', fullMessage);
        }
        if (classId) {
          socket.to(`class_${classId}`).emit('new_message', fullMessage);
        }

        // Confirm to sender
        socket.emit('message_sent', { messageId: message.id, status: 'delivered' });

      } catch (error) {
        console.error('Send message error:', error);
        socket.emit('message_error', { error: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data) => {
      const { recipientId, classId } = data;
      
      if (recipientId) {
        socket.to(`user_${recipientId}`).emit('user_typing', {
          userId: socket.userId,
          isTyping: true
        });
      }
      
      if (classId) {
        socket.to(`class_${classId}`).emit('user_typing', {
          userId: socket.userId,
          isTyping: true
        });
      }
    });

    socket.on('typing_stop', (data) => {
      const { recipientId, classId } = data;
      
      if (recipientId) {
        socket.to(`user_${recipientId}`).emit('user_typing', {
          userId: socket.userId,
          isTyping: false
        });
      }
      
      if (classId) {
        socket.to(`class_${classId}`).emit('user_typing', {
          userId: socket.userId,
          isTyping: false
        });
      }
    });

    // Handle video call signaling
    socket.on('join_video_call', async (data) => {
      try {
        const { callId } = data;
        
        // Verify user has access to this call
        const videoCall = await VideoCall.findByPk(callId);
        if (!videoCall) {
          socket.emit('call_error', { error: 'Call not found' });
          return;
        }

        socket.join(`call_${callId}`);
        socket.callId = callId;

        // Notify other participants
        socket.to(`call_${callId}`).emit('user_joined_call', {
          userId: socket.userId,
          callId
        });

        console.log(`User ${socket.userId} joined video call ${callId}`);
      } catch (error) {
        console.error('Join video call error:', error);
        socket.emit('call_error', { error: 'Failed to join call' });
      }
    });

    socket.on('leave_video_call', () => {
      if (socket.callId) {
        socket.to(`call_${socket.callId}`).emit('user_left_call', {
          userId: socket.userId,
          callId: socket.callId
        });
        socket.leave(`call_${socket.callId}`);
        socket.callId = null;
      }
    });

    // WebRTC signaling
    socket.on('webrtc_offer', (data) => {
      const { targetUserId, offer, callId } = data;
      socket.to(`user_${targetUserId}`).emit('webrtc_offer', {
        fromUserId: socket.userId,
        offer,
        callId
      });
    });

    socket.on('webrtc_answer', (data) => {
      const { targetUserId, answer, callId } = data;
      socket.to(`user_${targetUserId}`).emit('webrtc_answer', {
        fromUserId: socket.userId,
        answer,
        callId
      });
    });

    socket.on('webrtc_ice_candidate', (data) => {
      const { targetUserId, candidate, callId } = data;
      socket.to(`user_${targetUserId}`).emit('webrtc_ice_candidate', {
        fromUserId: socket.userId,
        candidate,
        callId
      });
    });

    // Handle screen sharing
    socket.on('start_screen_share', (data) => {
      const { callId } = data;
      socket.to(`call_${callId}`).emit('screen_share_started', {
        userId: socket.userId
      });
    });

    socket.on('stop_screen_share', (data) => {
      const { callId } = data;
      socket.to(`call_${callId}`).emit('screen_share_stopped', {
        userId: socket.userId
      });
    });

    // Handle chat in video calls
    socket.on('call_chat_message', (data) => {
      const { callId, message } = data;
      socket.to(`call_${callId}`).emit('call_chat_message', {
        userId: socket.userId,
        message,
        timestamp: new Date()
      });
    });

    // Handle emergency notifications
    socket.on('emergency_alert', async (data) => {
      try {
        if (socket.userRole !== 'admin' && socket.userRole !== 'teacher') {
          socket.emit('error', { message: 'Unauthorized' });
          return;
        }

        const { message, classId, priority = 'urgent' } = data;

        // Broadcast to all users in class or all users if no class specified
        if (classId) {
          io.to(`class_${classId}`).emit('emergency_notification', {
            message,
            priority,
            timestamp: new Date(),
            fromUserId: socket.userId
          });
        } else {
          io.emit('emergency_notification', {
            message,
            priority,
            timestamp: new Date(),
            fromUserId: socket.userId
          });
        }

        console.log(`Emergency alert sent by user ${socket.userId}: ${message}`);
      } catch (error) {
        console.error('Emergency alert error:', error);
        socket.emit('error', { message: 'Failed to send emergency alert' });
      }
    });

    // Handle user status updates
    socket.on('update_status', (data) => {
      const { status } = data; // online, away, busy, offline
      socket.broadcast.emit('user_status_update', {
        userId: socket.userId,
        status,
        timestamp: new Date()
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`User ${socket.userId} disconnected`);
      
      // Leave video call if in one
      if (socket.callId) {
        socket.to(`call_${socket.callId}`).emit('user_left_call', {
          userId: socket.userId,
          callId: socket.callId
        });
      }

      // Broadcast user offline status
      socket.broadcast.emit('user_status_update', {
        userId: socket.userId,
        status: 'offline',
        timestamp: new Date()
      });
    });

    // Handle connection errors
    socket.on('error', (error) => {
      console.error(`Socket error for user ${socket.userId}:`, error);
    });
  });
};

module.exports = socketHandler;