#!/bin/bash
# FamEduConnect Complete Deployment Script
# This script deploys all components of the FamEduConnect platform

# Set error handling
set -e

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_DIR=$(pwd)
DEPLOYMENT_LOG="$PROJECT_DIR/deployment-logs/full-deployment-$(date +%Y%m%d-%H%M%S).log"
BACKEND_TARGET=${2:-heroku} # Options: heroku, digitalocean, aws

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/deployment-logs"

# Log function
log() {
  local message="$1"
  local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
  echo "[$timestamp] $message" | tee -a "$DEPLOYMENT_LOG"
}

# Check if deployment scripts exist
if [ ! -f "$PROJECT_DIR/deployment-scripts/deploy-frontend.sh" ] || [ ! -f "$PROJECT_DIR/deployment-scripts/deploy-backend.sh" ]; then
  log "ERROR: Deployment scripts not found"
  exit 1
fi

# Make scripts executable
chmod +x "$PROJECT_DIR/deployment-scripts/deploy-frontend.sh"
chmod +x "$PROJECT_DIR/deployment-scripts/deploy-backend.sh"

# Display deployment information
log "Starting full deployment of FamEduConnect"
log "Environment: $ENVIRONMENT"
log "Backend Target: $BACKEND_TARGET"
log "Project Directory: $PROJECT_DIR"

# Confirm deployment
read -p "Do you want to proceed with deployment? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  log "Deployment cancelled by user"
  exit 0
fi

# Step 1: Deploy Backend
log "Step 1: Deploying Backend..."
"$PROJECT_DIR/deployment-scripts/deploy-backend.sh" "$ENVIRONMENT" "$BACKEND_TARGET"

# Check if backend deployment was successful
if [ ! -f "$PROJECT_DIR/deployment-status-backend.json" ]; then
  log "ERROR: Backend deployment failed or status file not created"
  exit 1
fi

# Get backend URL
BACKEND_URL=$(cat "$PROJECT_DIR/deployment-status-backend.json" | grep -o '"url": "[^"]*' | cut -d'"' -f4)
log "Backend deployed successfully to: $BACKEND_URL"

# Step 2: Update frontend configuration with backend URL
log "Step 2: Updating frontend configuration..."
ENV_CONFIG_DIR="$PROJECT_DIR/env-configs"

if [ -f "$ENV_CONFIG_DIR/frontend.env" ]; then
  # Update API URL in frontend.env
  sed -i "s|REACT_APP_API_URL=.*|REACT_APP_API_URL=$BACKEND_URL|g" "$ENV_CONFIG_DIR/frontend.env"
  sed -i "s|REACT_APP_SOCKET_URL=.*|REACT_APP_SOCKET_URL=wss://${BACKEND_URL#https://}|g" "$ENV_CONFIG_DIR/frontend.env"
  log "Frontend environment variables updated with backend URL"
else
  log "WARNING: Frontend environment file not found at $ENV_CONFIG_DIR/frontend.env"
  log "Continuing without updating frontend configuration"
fi

# Step 3: Deploy Frontend
log "Step 3: Deploying Frontend..."
"$PROJECT_DIR/deployment-scripts/deploy-frontend.sh" "$ENVIRONMENT"

# Check if frontend deployment was successful
if [ ! -f "$PROJECT_DIR/deployment-status.json" ]; then
  log "ERROR: Frontend deployment failed or status file not created"
  exit 1
fi

# Get frontend URL
FRONTEND_URL=$(cat "$PROJECT_DIR/deployment-status.json" | grep -o '"url": "[^"]*' | cut -d'"' -f4)
log "Frontend deployed successfully to: $FRONTEND_URL"

# Step 4: Deploy Guardian AI Monitoring
log "Step 4: Setting up Guardian AI Monitoring..."

# Check if Guardian AI files exist
if [ -d "$PROJECT_DIR/guardian-ai" ]; then
  # Create Guardian AI configuration
  mkdir -p "$PROJECT_DIR/guardian-ai/config"
  
  cat > "$PROJECT_DIR/guardian-ai/config/monitoring.json" << EOF
{
  "apiKey": "${GUARDIAN_AI_KEY:-demo-key}",
  "environment": "$ENVIRONMENT",
  "endpoints": {
    "frontend": "$FRONTEND_URL",
    "backend": "$BACKEND_URL"
  },
  "alertThresholds": {
    "error": 1.0,
    "performance": 2000,
    "security": 0.5
  },
  "notificationChannels": [
    {
      "type": "email",
      "address": "<EMAIL>"
    },
    {
      "type": "slack",
      "webhook": "${SLACK_WEBHOOK_URL:-https://hooks.slack.com/services/placeholder}"
    }
  ]
}
EOF

  log "Guardian AI monitoring configuration created"
  
  # Deploy Guardian AI if deployment script exists
  if [ -f "$PROJECT_DIR/guardian-ai/deploy.sh" ]; then
    chmod +x "$PROJECT_DIR/guardian-ai/deploy.sh"
    "$PROJECT_DIR/guardian-ai/deploy.sh" "$ENVIRONMENT"
    log "Guardian AI monitoring deployed"
  else
    log "Guardian AI deployment script not found, skipping deployment"
  fi
else
  log "Guardian AI directory not found, skipping monitoring setup"
fi

# Step 5: Update DNS Configuration
log "Step 5: DNS Configuration Instructions..."

cat << EOF >> "$DEPLOYMENT_LOG"

DNS CONFIGURATION INSTRUCTIONS
=============================

To complete the deployment, configure your DNS settings as follows:

1. Main Domain (fameduconnect.xyz):
   Type: A
   Value: Point to your landing page server IP

2. App Subdomain (app.fameduconnect.xyz):
   Type: CNAME
   Value: $FRONTEND_URL

3. API Subdomain (api.fameduconnect.xyz):
   Type: CNAME
   Value: $BACKEND_URL

4. Admin Subdomain (admin.fameduconnect.xyz):
   Type: CNAME
   Value: [Your admin dashboard URL]

For detailed DNS configuration, refer to DOMAIN_CONFIGURATION.md
EOF

log "DNS configuration instructions added to deployment log"

# Step 6: Generate Deployment Summary
log "Step 6: Generating Deployment Summary..."

DEPLOYMENT_SUMMARY="$PROJECT_DIR/DEPLOYMENT_SUMMARY.md"

cat > "$DEPLOYMENT_SUMMARY" << EOF
# FamEduConnect Deployment Summary

## Deployment Information

- **Environment:** $ENVIRONMENT
- **Deployment Date:** $(date +"%Y-%m-%d %H:%M:%S")
- **Deployment ID:** $(date +%Y%m%d-%H%M%S)

## Deployed Components

### Backend
- **URL:** $BACKEND_URL
- **Platform:** $BACKEND_TARGET
- **Status:** ✅ Deployed

### Frontend
- **URL:** $FRONTEND_URL
- **Platform:** Vercel
- **Status:** ✅ Deployed

### Guardian AI Monitoring
- **Status:** ${GUARDIAN_AI_STATUS:-⚠️ Configuration Only}

## Next Steps

1. Configure DNS settings as specified in the deployment log
2. Verify all components are working correctly
3. Set up monitoring and alerts
4. Complete final testing

## Access Information

- **Frontend:** $FRONTEND_URL
- **Backend API:** $BACKEND_URL
- **Admin Dashboard:** [Configure in DNS]
- **API Documentation:** $BACKEND_URL/docs

## Support

If you encounter any issues with this deployment, please contact:
- **Email:** <EMAIL>
- **Deployment Log:** $DEPLOYMENT_LOG

---

© 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc. – NPO: 2023-001341848  
Creative Director: Na'imah Barnes
EOF

log "Deployment summary generated at $DEPLOYMENT_SUMMARY"

# Step 7: Final Verification
log "Step 7: Running Final Verification..."

# Check frontend accessibility
HTTP_STATUS_FRONTEND=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL")
if [ "$HTTP_STATUS_FRONTEND" = "200" ]; then
  log "Frontend is accessible (HTTP 200)"
else
  log "WARNING: Frontend returned HTTP status $HTTP_STATUS_FRONTEND"
fi

# Check backend accessibility
HTTP_STATUS_BACKEND=$(curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/api/health")
if [ "$HTTP_STATUS_BACKEND" = "200" ]; then
  log "Backend is accessible (HTTP 200)"
else
  log "WARNING: Backend returned HTTP status $HTTP_STATUS_BACKEND"
fi

# Deployment complete
log "🎉 Deployment completed successfully!"
log "Frontend URL: $FRONTEND_URL"
log "Backend URL: $BACKEND_URL"
log "Deployment Summary: $DEPLOYMENT_SUMMARY"
log "Deployment Log: $DEPLOYMENT_LOG"

exit 0