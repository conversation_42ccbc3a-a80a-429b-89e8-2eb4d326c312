{"version": 4, "terraform_version": "1.12.2", "serial": 5, "lineage": "240b263a-03af-1792-a501-6cd25635a161", "outputs": {"local_dev_info": {"value": {"deploy_script": "./../../scripts/deploy-local.sh", "docker_compose_file": "./../../docker-compose.dev.yml", "env_file": "./../../.env.dev", "health_check_script": "./../../scripts/health-check.sh", "services": {"nestjs_backend": "http://localhost:3000", "postgres": "localhost:5432", "rabbitmq": "localhost:5672", "rabbitmq_management": "http://localhost:15672", "redis": "localhost:6379"}}, "type": ["object", {"deploy_script": "string", "docker_compose_file": "string", "env_file": "string", "health_check_script": "string", "services": ["object", {"nestjs_backend": "string", "postgres": "string", "rabbitmq": "string", "rabbitmq_management": "string", "redis": "string"}]}]}}, "resources": [{"mode": "managed", "type": "local_file", "name": "deploy_local", "provider": "provider[\"registry.terraform.io/hashicorp/local\"]", "instances": [{"schema_version": 0, "attributes": {"content": "#!/bin/bash\r\n\r\necho \"Deploying FamEduConnect Backend for Local Development...\"\r\n\r\n# Start local services\r\necho \"Starting local services...\"\r\ndocker-compose -f docker-compose.dev.yml up -d\r\n\r\n# Wait for services to be ready\r\necho \"Waiting for services to be ready...\"\r\nsleep 10\r\n\r\n# Run database migrations\r\necho \"Running database migrations...\"\r\ncd ../\r\nnpx prisma migrate dev\r\n\r\n# Generate Prisma client\r\necho \"Generating Prisma client...\"\r\nnpx prisma generate\r\n\r\n# Start the NestJS application\r\necho \"Starting NestJS application...\"\r\nnpm run start:dev\r\n\r\necho \"Local deployment complete!\"\r\necho \"Services available at:\"\r\necho \"- PostgreSQL: localhost:5432\"\r\necho \"- Redis: localhost:6379\"\r\necho \"- RabbitMQ: localhost:5672 (Management: http://localhost:15672)\"\r\necho \"- NestJS Backend: http://localhost:3000\"\r\n", "content_base64": null, "content_base64sha256": "wVVkrKoIZzdtpWwB7l6+r3sI0+pzDUZYzX+GFOotXbE=", "content_base64sha512": "l5IoKN7XU9AtpaaXjNXFlJaSAVwn7JcEN66maRIx0VMWgTJ8nP41jebMsxR94WXbZ4lfd4P8t4nJEfqnfyEfLA==", "content_md5": "08de8aa3368683cdfc0b3b914b87e3e0", "content_sha1": "26925b0f639eb5803e268226fa3d23c219254ac9", "content_sha256": "c15564acaa0867376da56c01ee5ebeaf7b08d3ea730d4658cd7f8614ea2d5db1", "content_sha512": "97922828ded753d02da5a6978cd5c5949692015c27ec970437aea6691231d1531681327c9cfe358de6ccb3147de165db67895f7783fcb789c911faa77f211f2c", "directory_permission": "0777", "file_permission": "0777", "filename": "./../../scripts/deploy-local.sh", "id": "26925b0f639eb5803e268226fa3d23c219254ac9", "sensitive_content": null, "source": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "sensitive_content"}]], "identity_schema_version": 0}]}, {"mode": "managed", "type": "local_file", "name": "docker_compose_dev", "provider": "provider[\"registry.terraform.io/hashicorp/local\"]", "instances": [{"schema_version": 0, "attributes": {"content": "version: '3.8'\r\n\r\nservices:\r\n  postgres:\r\n    image: postgres:15\r\n    environment:\r\n      POSTGRES_DB: fameduconnect_dev\r\n      POSTGRES_USER: famedu_user\r\n      POSTGRES_PASSWORD: famedu_password_123\r\n    ports:\r\n      - \"5432:5432\"\r\n    volumes:\r\n      - postgres_data:/var/lib/postgresql/data\r\n    networks:\r\n      - famedu_network\r\n\r\n  redis:\r\n    image: redis:7-alpine\r\n    ports:\r\n      - \"6379:6379\"\r\n    volumes:\r\n      - redis_data:/data\r\n    networks:\r\n      - famedu_network\r\n\r\n  rabbitmq:\r\n    image: rabbitmq:3-management\r\n    environment:\r\n      RABBITMQ_DEFAULT_USER: famedu_user\r\n      RABBITMQ_DEFAULT_PASS: famedu_password_123\r\n    ports:\r\n      - \"5672:5672\"\r\n      - \"15672:15672\"\r\n    volumes:\r\n      - rabbitmq_data:/var/lib/rabbitmq\r\n    networks:\r\n      - famedu_network\r\n\r\nvolumes:\r\n  postgres_data:\r\n  redis_data:\r\n  rabbitmq_data:\r\n\r\nnetworks:\r\n  famedu_network:\r\n    driver: bridge\r\n", "content_base64": null, "content_base64sha256": "J01djLkNMK+Kgffek1BAJkv4zfwhN943ZqIq7A+Q4gg=", "content_base64sha512": "W3umfSnTsN1lJopIVJigjClg5PU4j9CA7rVUVrW21f6xvjCGYtwG1BDjZaU68Q7mleKE9egWUKE4dlr2jOQoBw==", "content_md5": "a2a46bb0d68840cc897179ddd16ed3cd", "content_sha1": "9bacad5e7515f82f9754b8cd0f16ced0b69f3662", "content_sha256": "274d5d8cb90d30af8a81f7de935040264bf8cdfc2137de3766a22aec0f90e208", "content_sha512": "5b7ba67d29d3b0dd65268a485498a08c2960e4f5388fd080eeb55456b5b6d5feb1be308662dc06d410e365a53af10ee695e284f5e81650a138765af68ce42807", "directory_permission": "0777", "file_permission": "0777", "filename": "./../../docker-compose.dev.yml", "id": "9bacad5e7515f82f9754b8cd0f16ced0b69f3662", "sensitive_content": null, "source": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "content"}], [{"type": "get_attr", "value": "sensitive_content"}]], "identity_schema_version": 0}]}, {"mode": "managed", "type": "local_file", "name": "env_dev", "provider": "provider[\"registry.terraform.io/hashicorp/local\"]", "instances": [{"schema_version": 0, "attributes": {"content": "# Local Development Environment Variables\r\nNODE_ENV=development\r\nPORT=3000\r\n\r\n# Database Configuration\r\nDATABASE_URL=postgresql://famedu_user:famedu_password_123@localhost:5432/fameduconnect_dev\r\n\r\n# Redis Configuration\r\nREDIS_URL=redis://localhost:6379\r\n\r\n# RabbitMQ Configuration\r\nRABBITMQ_URL=amqp://famedu_user:famedu_password_123@localhost:5672\r\n\r\n# JWT Configuration\r\nJWT_SECRET=your-super-secret-jwt-key-change-in-production\r\n\r\n# CORS Configuration\r\nCORS_ORIGIN=http://localhost:3000,http://localhost:3001\r\n\r\n# Logging\r\nLOG_LEVEL=debug\r\nENABLE_METRICS=true\r\n\r\n# Sentry (optional for local dev)\r\nSENTRY_DSN=\r\n\r\n# SMTP Configuration (optional for local dev)\r\nSMTP_HOST=\r\nSMTP_PORT=\r\nSMTP_USER=\r\nSMTP_PASS=\r\n\r\n# File Upload\r\nMAX_FILE_SIZE=10485760\r\nBCRYPT_ROUNDS=12\r\n", "content_base64": null, "content_base64sha256": "lkccSE9KS7FKKe/N8pbU3xlwrjkp/GWeEciC179MOmk=", "content_base64sha512": "7R+xVW02WXawntp/PxzgZXg1t6D6YQQG8QNvfLFzyGorFd0CcXwH1yUIxaTj9rlmKCSQCZneR7kRavYzefAUyA==", "content_md5": "ed9fb0559b69d0b0aed3fd96cfa31e3c", "content_sha1": "48c76c98e269d9f7735d676be9bc0712b76f6a81", "content_sha256": "96471c484f4a4bb14a29efcdf296d4df1970ae3929fc659e11c882d7bf4c3a69", "content_sha512": "ed1fb1556d365976b09eda7f3f1ce0657835b7a0fa610406f1036f7cb173c86a2b15dd02717c07d72508c5a4e3f6b9662824900999de47b9116af63379f014c8", "directory_permission": "0777", "file_permission": "0777", "filename": "./../../.env.dev", "id": "48c76c98e269d9f7735d676be9bc0712b76f6a81", "sensitive_content": null, "source": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "content"}], [{"type": "get_attr", "value": "sensitive_content"}]], "identity_schema_version": 0}]}, {"mode": "managed", "type": "local_file", "name": "health_check", "provider": "provider[\"registry.terraform.io/hashicorp/local\"]", "instances": [{"schema_version": 0, "attributes": {"content": "#!/bin/bash\r\n\r\necho \"Checking FamEduConnect Backend Health...\"\r\n\r\n# Check if services are running\r\necho \"Checking PostgreSQL...\"\r\npg_isready -h localhost -p 5432 -U famedu_user || echo \"PostgreSQL not ready\"\r\n\r\necho \"Checking Redis...\"\r\nredis-cli -h localhost -p 6379 ping || echo \"Redis not ready\"\r\n\r\necho \"Checking RabbitMQ...\"\r\ncurl -s http://localhost:15672/api/overview -u famedu_user:famedu_password_123 || echo \"RabbitMQ not ready\"\r\n\r\necho \"Checking NestJS Backend...\"\r\ncurl -s http://localhost:3000/health || echo \"NestJS Backend not ready\"\r\n\r\necho \"Health check complete!\"\r\n", "content_base64": null, "content_base64sha256": "fRyTtNpvYEyHmEv3h7IOhks1Pvi2t/xeg+7lJjrHDJ4=", "content_base64sha512": "u9mK/cKznWhEpWEh1ceZI7nAmBgDEE32IFZDNJovJNDsRieWl5efY7YvkZNGZuBfa1kBvDjOtttBiF+UTs3g/g==", "content_md5": "effba55a55ba35427dbb3b44e8d469d5", "content_sha1": "77d6501c216c78b3dffc00bce44345136d038541", "content_sha256": "7d1c93b4da6f604c87984bf787b20e864b353ef8b6b7fc5e83eee5263ac70c9e", "content_sha512": "bbd98afdc2b39d6844a56121d5c79923b9c0981803104df6205643349a2f24d0ec46279697979f63b62f91934666e05f6b5901bc38ceb6db41885f944ecde0fe", "directory_permission": "0777", "file_permission": "0777", "filename": "./../../scripts/health-check.sh", "id": "77d6501c216c78b3dffc00bce44345136d038541", "sensitive_content": null, "source": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "content"}], [{"type": "get_attr", "value": "sensitive_content"}]], "identity_schema_version": 0}]}], "check_results": [{"object_kind": "var", "config_addr": "var.environment", "status": "pass", "objects": [{"object_addr": "var.environment", "status": "pass"}]}]}