# Local Development Configuration
# This file provides a simplified Terraform setup for local development
# without requiring AWS credentials

terraform {
  required_version = ">= 1.0"
  required_providers {
    local = {
      source  = "hashicorp/local"
      version = "~> 2.0"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 3.0"
    }
  }
}

# Local file provider for creating local development files
provider "local" {}

# Null provider for resource creation without actual infrastructure
provider "null" {}

# Create local development configuration files
resource "local_file" "docker_compose_dev" {
  filename = "${path.module}/../../docker-compose.dev.yml"
  content  = <<-EOT
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: fameduconnect_dev
      POSTGRES_USER: ${var.database_username}
      POSTGRES_PASSWORD: ${var.database_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - famedu_network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - famedu_network

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: ${var.rabbitmq_username}
      RABBITMQ_DEFAULT_PASS: ${var.rabbitmq_password}
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - famedu_network

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:

networks:
  famedu_network:
    driver: bridge
EOT
}

# Create environment file for local development
resource "local_file" "env_dev" {
  filename = "${path.module}/../../.env.dev"
  content  = <<-EOT
# Local Development Environment Variables
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=postgresql://${var.database_username}:${var.database_password}@localhost:5432/fameduconnect_dev

# Redis Configuration
REDIS_URL=redis://localhost:6379

# RabbitMQ Configuration
RABBITMQ_URL=amqp://${var.rabbitmq_username}:${var.rabbitmq_password}@localhost:5672

# JWT Configuration
JWT_SECRET=${var.jwt_secret}

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Logging
LOG_LEVEL=debug
ENABLE_METRICS=true

# Sentry (optional for local dev)
SENTRY_DSN=

# SMTP Configuration (optional for local dev)
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=

# File Upload
MAX_FILE_SIZE=10485760
BCRYPT_ROUNDS=12
EOT
}

# Create a simple health check script
resource "local_file" "health_check" {
  filename = "${path.module}/../../scripts/health-check.sh"
  content  = <<-EOT
#!/bin/bash

echo "Checking FamEduConnect Backend Health..."

# Check if services are running
echo "Checking PostgreSQL..."
pg_isready -h localhost -p 5432 -U ${var.database_username} || echo "PostgreSQL not ready"

echo "Checking Redis..."
redis-cli -h localhost -p 6379 ping || echo "Redis not ready"

echo "Checking RabbitMQ..."
curl -s http://localhost:15672/api/overview -u ${var.rabbitmq_username}:${var.rabbitmq_password} || echo "RabbitMQ not ready"

echo "Checking NestJS Backend..."
curl -s http://localhost:3000/health || echo "NestJS Backend not ready"

echo "Health check complete!"
EOT
}

# Create deployment script for local development
resource "local_file" "deploy_local" {
  filename = "${path.module}/../../scripts/deploy-local.sh"
  content  = <<-EOT
#!/bin/bash

echo "Deploying FamEduConnect Backend for Local Development..."

# Start local services
echo "Starting local services..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Run database migrations
echo "Running database migrations..."
cd ../
npx prisma migrate dev

# Generate Prisma client
echo "Generating Prisma client..."
npx prisma generate

# Start the NestJS application
echo "Starting NestJS application..."
npm run start:dev

echo "Local deployment complete!"
echo "Services available at:"
echo "- PostgreSQL: localhost:5432"
echo "- Redis: localhost:6379"
echo "- RabbitMQ: localhost:5672 (Management: http://localhost:15672)"
echo "- NestJS Backend: http://localhost:3000"
EOT
}

# Output local development information
output "local_dev_info" {
  description = "Local development setup information"
  value = {
    docker_compose_file = local_file.docker_compose_dev.filename
    env_file = local_file.env_dev.filename
    health_check_script = local_file.health_check.filename
    deploy_script = local_file.deploy_local.filename
    services = {
      postgres = "localhost:5432"
      redis = "localhost:6379"
      rabbitmq = "localhost:5672"
      rabbitmq_management = "http://localhost:15672"
      nestjs_backend = "http://localhost:3000"
    }
  }
} 