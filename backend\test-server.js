const express = require('express');
const cors = require('cors');

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Backend is working!',
    timestamp: new Date().toISOString(),
    status: 'success'
  });
});

// Auth test route
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Simple mock authentication
  if (email === '<EMAIL>' && password === 'password123') {
    res.json({
      message: 'Login successful',
      token: 'mock-jwt-token-' + Date.now(),
      user: {
        id: 1,
        email: email,
        firstName: 'Test',
        lastName: 'User',
        role: 'parent',
        preferredLanguage: 'en'
      }
    });
  } else {
    res.status(401).json({ message: 'Invalid credentials' });
  }
});

// Register test route
app.post('/api/auth/register', (req, res) => {
  const { email, password, firstName, lastName, role } = req.body;
  
  // Simple mock registration
  res.status(201).json({
    message: 'User created successfully',
    token: 'mock-jwt-token-' + Date.now(),
    user: {
      id: 2,
      email: email,
      firstName: firstName,
      lastName: lastName,
      role: role,
      preferredLanguage: 'en'
    }
  });
});

// User profile test route
app.get('/api/users/profile', (req, res) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  
  if (token) {
    res.json({
      id: 1,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'parent',
      preferredLanguage: 'en'
    });
  } else {
    res.status(401).json({ message: 'No token provided' });
  }
});

const PORT = process.env.PORT || 5555;

app.listen(PORT, () => {
  console.log(`🚀 Test server running on port ${PORT}`);
  console.log(`📡 API available at http://localhost:${PORT}/api`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/test`);
}); 