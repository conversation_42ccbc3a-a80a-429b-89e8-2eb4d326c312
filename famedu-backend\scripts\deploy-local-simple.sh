#!/bin/bash

echo "Deploying FamEduConnect Backend for Local Development (Simplified)..."

# Check if .env.dev exists
if [ ! -f ".env.dev" ]; then
    echo "Error: .env.dev file not found. Please run Terraform first to generate it."
    exit 1
fi

# Copy .env.dev to .env for the application
echo "Setting up environment variables..."
cp .env.dev .env

# Generate Prisma client
echo "Generating Prisma client..."
npx prisma generate

# Check if PostgreSQL is available locally
echo "Checking PostgreSQL availability..."
if command -v psql &> /dev/null; then
    echo "PostgreSQL client found. You can manually create the database:"
    echo "psql -U famedu_user -d postgres -c 'CREATE DATABASE fameduconnect_dev;'"
else
    echo "PostgreSQL client not found. Please install PostgreSQL or use Docker."
fi

# Start the NestJS application
echo "Starting NestJS application..."
echo "Note: Make sure PostgreSQL, Redis, and RabbitMQ are running locally or via Docker"
echo "You can start them manually or use: docker-compose -f docker-compose.dev.yml up -d"
echo ""
npm run start:dev

echo "Local deployment complete!"
echo "Services should be available at:"
echo "- NestJS Backend: http://localhost:3000"
echo "- Health Check: http://localhost:3000/health" 