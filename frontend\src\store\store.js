import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import socketSlice from './slices/socketSlice';
import messagesSlice from './slices/messagesSlice';
import videoCallSlice from './slices/videoCallSlice';
import studentsSlice from './slices/studentsSlice';
import classesSlice from './slices/classesSlice';
import notificationsSlice from './slices/notificationsSlice';
import uiSlice from './slices/uiSlice';
import dashboardSlice from './slices/dashboardSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    socket: socketSlice,
    messages: messagesSlice,
    videoCall: videoCallSlice,
    students: studentsSlice,
    classes: classesSlice,
    notifications: notificationsSlice,
    ui: uiSlice,
    dashboard: dashboardSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['socket/setSocket'],
        ignoredPaths: ['socket.socket'],
      },
    }),
});

// Export types for TypeScript usage (if needed)
// export type RootState = ReturnType<typeof store.getState>;
// export type AppDispatch = typeof store.dispatch;