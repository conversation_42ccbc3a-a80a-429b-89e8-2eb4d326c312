import React, { useState } from 'react';

const DebugLogin = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('AdminDemo2025!');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('🔍 Attempting login with:', { email, password });

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ email, password })
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      const data = await response.json();
      console.log('📡 Response data:', data);

      if (response.ok) {
        setResult({
          success: true,
          message: 'Login successful!',
          data: data
        });
      } else {
        setError({
          status: response.status,
          message: data.message || 'Login failed',
          data: data
        });
      }
    } catch (err) {
      console.error('❌ Login error:', err);
      setError({
        message: err.message,
        type: 'network'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      maxWidth: '500px', 
      margin: '50px auto', 
      padding: '20px',
      border: '1px solid #ccc',
      borderRadius: '8px',
      backgroundColor: '#f9f9f9'
    }}>
      <h2>🔧 Debug Login Test</h2>
      <p>This component tests the login API directly without Redux.</p>
      
      <form onSubmit={handleLogin}>
        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px' }}>
            Email:
          </label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
          />
        </div>
        
        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px' }}>
            Password:
          </label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            style={{ width: '100%', padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
          />
        </div>
        
        <button
          type="submit"
          disabled={loading}
          style={{
            width: '100%',
            padding: '10px',
            backgroundColor: loading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Logging in...' : 'Test Login'}
        </button>
      </form>

      {result && (
        <div style={{ 
          marginTop: '20px', 
          padding: '15px', 
          backgroundColor: '#d4edda', 
          border: '1px solid #c3e6cb',
          borderRadius: '4px',
          color: '#155724'
        }}>
          <h3>✅ Success!</h3>
          <p>{result.message}</p>
          <pre style={{ fontSize: '12px', overflow: 'auto' }}>
            {JSON.stringify(result.data, null, 2)}
          </pre>
        </div>
      )}

      {error && (
        <div style={{ 
          marginTop: '20px', 
          padding: '15px', 
          backgroundColor: '#f8d7da', 
          border: '1px solid #f5c6cb',
          borderRadius: '4px',
          color: '#721c24'
        }}>
          <h3>❌ Error!</h3>
          <p><strong>Message:</strong> {typeof error === 'string' ? error : error.message}</p>
          {error.status && <p><strong>Status:</strong> {error.status}</p>}
          {error.data && (
            <pre style={{ fontSize: '12px', overflow: 'auto' }}>
              {JSON.stringify(error.data, null, 2)}
            </pre>
          )}
        </div>
      )}

      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <p><strong>Instructions:</strong></p>
        <ol>
          <li>Open browser console (F12) to see detailed logs</li>
          <li>Click "Test Login" to attempt authentication</li>
          <li>Check both frontend console and backend terminal for logs</li>
          <li>If successful, you should see the user data above</li>
        </ol>
      </div>
    </div>
  );
};

export default DebugLogin; 