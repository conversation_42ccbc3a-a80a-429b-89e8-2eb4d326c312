import { IsEmail, IsString, Min<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    type: String
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password (minimum 6 characters)',
    example: 'password123',
    type: String,
    minLength: 6
  })
  @IsString()
  @MinLength(6)
  password: string;
} 