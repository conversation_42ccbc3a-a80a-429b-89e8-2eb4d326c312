#!/bin/bash

# FamEduConnect Infrastructure Deployment Script
set -e

ENVIRONMENT=${1:-staging}
REGION=${2:-us-east-1}

echo "🚀 Deploying FamEduConnect infrastructure for environment: $ENVIRONMENT in region: $REGION"

# Check if required environment variables are set
if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
    echo "❌ Error: AWS credentials not set. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY"
    exit 1
fi

# Check if required secrets are set
if [ -z "$DB_PASSWORD" ] || [ -z "$RABBITMQ_PASSWORD" ]; then
    echo "❌ Error: Required secrets not set. Please set DB_PASSWORD and RABBITMQ_PASSWORD"
    exit 1
fi

# Create terraform.tfvars file
cat > terraform.tfvars << EOF
environment = "$ENVIRONMENT"
aws_region = "$REGION"
database_username = "famedu_admin"
database_password = "$DB_PASSWORD"
rabbitmq_password = "$RABBITMQ_PASSWORD"
EOF

# Initialize Terraform
echo "📦 Initializing Terraform..."
terraform init

# Plan the deployment
echo "📋 Planning deployment..."
terraform plan -var-file=terraform.tfvars -out=tfplan

# Ask for confirmation
read -p "🤔 Do you want to apply this plan? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Deployment cancelled"
    exit 1
fi

# Apply the plan
echo "🚀 Applying infrastructure changes..."
terraform apply tfplan

# Get outputs
echo "📊 Infrastructure deployed successfully!"
echo "🔗 RDS Endpoint: $(terraform output -raw rds_endpoint)"
echo "🔗 Redis Endpoint: $(terraform output -raw redis_endpoint)"
echo "🔗 RabbitMQ Endpoint: $(terraform output -raw rabbitmq_endpoint)"
echo "🔗 EKS Cluster: $(terraform output -raw eks_cluster_name)"
echo "🔗 ALB DNS: $(terraform output -raw alb_dns_name)"

# Clean up
rm -f terraform.tfvars tfplan

echo "✅ Infrastructure deployment completed!" 