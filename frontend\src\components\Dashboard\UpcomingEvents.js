import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CalendarIcon, VideoCameraIcon, AcademicCapIcon } from '@heroicons/react/24/outline';
import { format, parseISO, isToday, isTomorrow } from 'date-fns';

const UpcomingEvents = ({ events = [] }) => {
  const formatEventDate = (dateString) => {
    try {
      const date = parseISO(dateString);
      if (isToday(date)) {
        return `Today, ${format(date, 'HH:mm')}`;
      } else if (isTomorrow(date)) {
        return `Tomorrow, ${format(date, 'HH:mm')}`;
      } else {
        return format(date, 'MMM dd, HH:mm');
      }
    } catch {
      return dateString;
    }
  };

  const getEventIcon = (type) => {
    switch (type) {
      case 'video_call':
        return VideoCameraIcon;
      case 'class':
        return AcademicCapIcon;
      default:
        return CalendarIcon;
    }
  };

  const getEventColor = (type) => {
    switch (type) {
      case 'video_call':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'class':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700';
    }
  };

  if (events.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Upcoming Events</h3>
          <Link to="/calendar" className="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
            View calendar
          </Link>
        </div>
        <div className="text-center py-8">
          <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">No upcoming events</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Upcoming Events</h3>
        <Link to="/calendar" className="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
          View calendar
        </Link>
      </div>
      
      <div className="space-y-3">
        {events.slice(0, 5).map((event, index) => {
          const IconComponent = getEventIcon(event.type);
          const colorClass = getEventColor(event.type);
          
          return (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-start space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer"
            >
              <div className={`p-2 rounded-lg ${colorClass}`}>
                <IconComponent className="h-4 w-4" />
              </div>
              
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {event.title}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {formatEventDate(event.scheduledStartTime || event.date)}
                </p>
                {event.description && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {event.description}
                  </p>
                )}
              </div>
              
              {event.priority === 'high' && (
                <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
              )}
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default UpcomingEvents;