import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  CogIcon,
  UserGroupIcon,
  AcademicCapIcon,
  ChatBubbleLeftRightIcon,
  CalendarIcon,
  BellIcon,
  ChartBarIcon,
  BookOpenIcon,
  ClipboardDocumentListIcon,
  PresentationChartLineIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { fetchDashboardData } from '../../store/slices/dashboardSlice';

const TeacherDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const { 
    stats, 
    recentMessages, 
    upcomingEvents, 
    alerts,
    loading 
  } = useSelector((state) => state.dashboard);

  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    // Set greeting based on time of day
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('Good morning');
    } else if (hour < 17) {
      setGreeting('Good afternoon');
    } else {
      setGreeting('Good evening');
    }

    // Fetch dashboard data
    dispatch(fetchDashboardData());
  }, [dispatch]);

  const handleSettingsClick = () => {
    navigate('/settings');
  };

  const handleCompleteOnboarding = () => {
    navigate('/onboarding');
  };

  const getDashboardCards = () => [
    {
      title: 'Classes',
      value: stats?.classCount || 0,
      subtitle: 'Active classes',
      icon: UserGroupIcon,
      color: 'blue',
      href: '/classes'
    },
    {
      title: 'Students',
      value: stats?.studentCount || 0,
      subtitle: 'Total students',
      icon: AcademicCapIcon,
      color: 'green',
      href: '/students'
    },
    {
      title: 'Assignments',
      value: stats?.pendingAssignments || 0,
      subtitle: 'Pending reviews',
      icon: ClipboardDocumentListIcon,
      color: 'yellow',
      href: '/assignments'
    },
    {
      title: 'Messages',
      value: stats?.unreadMessages || 0,
      subtitle: 'Unread messages',
      icon: ChatBubbleLeftRightIcon,
      color: 'purple',
      href: '/messages'
    }
  ];

  const getQuickActions = () => [
    {
      title: 'Create Assignment',
      description: 'Add new homework or project',
      icon: ClipboardDocumentListIcon,
      href: '/assignments/create',
      color: 'blue'
    },
    {
      title: 'Grade Submissions',
      description: 'Review student work',
      icon: ChartBarIcon,
      href: '/assignments/grade',
      color: 'green'
    },
    {
      title: 'Class Schedule',
      description: 'View today\'s classes',
      icon: CalendarIcon,
      href: '/schedule',
      color: 'purple'
    },
    {
      title: 'Student Progress',
      description: 'Track academic performance',
      icon: PresentationChartLineIcon,
      href: '/progress',
      color: 'yellow'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {greeting}, {user?.firstName || 'Teacher'}!
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Welcome to your teacher dashboard
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleCompleteOnboarding}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <CogIcon className="w-4 h-4 mr-2" />
                Complete Onboarding
              </button>
              <button
                onClick={handleSettingsClick}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <CogIcon className="w-4 h-4 mr-2" />
                Settings
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {getDashboardCards().map((card, index) => (
            <motion.div
              key={card.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white overflow-hidden shadow rounded-lg cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate(card.href)}
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <card.icon className={`h-6 w-6 text-${card.color}-600`} />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {card.title}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {card.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-5 py-3">
                <div className="text-sm">
                  <span className="text-gray-500">{card.subtitle}</span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {getQuickActions().map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md transition-all duration-200 hover:border-blue-300"
                onClick={() => navigate(action.href)}
              >
                <div className="flex items-center">
                  <div className={`flex-shrink-0 w-10 h-10 bg-${action.color}-100 rounded-lg flex items-center justify-center`}>
                    <action.icon className={`w-5 h-5 text-${action.color}-600`} />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-gray-900">{action.title}</h3>
                    <p className="text-sm text-gray-500">{action.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Messages */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Messages</h3>
            </div>
            <div className="p-6">
              {recentMessages && recentMessages.length > 0 ? (
                <div className="space-y-4">
                  {recentMessages.slice(0, 5).map((message, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <ChatBubbleLeftRightIcon className="w-4 h-4 text-blue-600" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">{message.sender}</p>
                        <p className="text-sm text-gray-500 truncate">{message.content}</p>
                        <p className="text-xs text-gray-400">{message.timestamp}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No recent messages</p>
              )}
            </div>
          </div>

          {/* Today's Classes */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Today's Classes</h3>
            </div>
            <div className="p-6">
              {upcomingEvents && upcomingEvents.length > 0 ? (
                <div className="space-y-4">
                  {upcomingEvents.slice(0, 5).map((event, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                          <CalendarIcon className="w-4 h-4 text-purple-600" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">{event.title}</p>
                        <p className="text-sm text-gray-500">{event.date}</p>
                        <p className="text-xs text-gray-400">{event.location}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No classes scheduled today</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeacherDashboard; 