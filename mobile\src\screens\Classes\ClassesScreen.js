import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';

const ClassesScreen = () => {
  const classes = [
    { id: 1, name: 'Mathematics', teacher: 'Mr<PERSON> <PERSON>', students: 25, status: 'active' },
    { id: 2, name: '<PERSON>', teacher: '<PERSON><PERSON>', students: 22, status: 'active' },
    { id: 3, name: 'English', teacher: 'Mrs<PERSON> <PERSON>', students: 28, status: 'active' },
  ];

  const renderClass = ({ item }) => (
    <TouchableOpacity style={styles.classItem}>
      <View style={styles.classHeader}>
        <Text style={styles.className}>{item.name}</Text>
        <View style={[styles.statusBadge, { backgroundColor: item.status === 'active' ? '#10B981' : '#6B7280' }]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
      <Text style={styles.teacher}>Teacher: {item.teacher}</Text>
      <Text style={styles.students}>{item.students} students enrolled</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={classes}
        renderItem={renderClass}
        keyExtractor={(item) => item.id.toString()}
        style={styles.list}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 15,
  },
  classItem: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  classHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  className: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  teacher: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 5,
  },
  students: {
    fontSize: 14,
    color: '#6B7280',
  },
});

export default ClassesScreen;