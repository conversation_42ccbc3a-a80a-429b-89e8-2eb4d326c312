# 🚀 FamEduConnect - Quick Start Guide

## 🎯 **Get FamEduConnect Running in 10 Minutes**

### 📋 **Prerequisites**
- Node.js 18+ and npm
- Git
- Modern web browser
- (Optional) PostgreSQL for production

### ⚡ **Quick Start**

#### **1. <PERSON><PERSON> and Install**
```bash
# Clone the repository
git clone <repository-url>
cd FamEduConnect_Full_Codebase

# Install all dependencies
npm run install:all
```

#### **2. Start Development Servers**
```bash
# Start all services (backend, frontend, admin)
npm run dev
```

This will start:
- **Backend**: http://localhost:5555
- **Frontend**: http://localhost:3000
- **Admin**: http://localhost:3001

#### **3. Access the Application**
- **Main App**: http://localhost:3000
- **Admin Dashboard**: http://localhost:3001
- **API Health Check**: http://localhost:5555/health
- **API Test**: http://localhost:5555/api/test

### 🔧 **Configuration**

#### **Backend Configuration**
The backend is configured to run in development mode with SQLite database by default. No additional configuration needed for basic testing.

#### **Frontend Configuration**
The frontend automatically connects to the backend on port 5555. No additional configuration needed.

#### **Mobile App**
```bash
# Start mobile development
npm run dev:mobile
```

### 🧪 **Testing the Application**

#### **1. Test Backend API**
```bash
# Test API health
curl http://localhost:5555/health

# Test API endpoint
curl http://localhost:5555/api/test
```

#### **2. Test Frontend**
1. Open http://localhost:3000
2. Try the registration/login flow
3. Test the dashboard and messaging features

#### **3. Test Admin Dashboard**
1. Open http://localhost:3001
2. Login with admin credentials
3. Test user management and analytics

### 🐛 **Troubleshooting**

#### **Common Issues**

**Backend won't start:**
```bash
# Check if port 5555 is available
lsof -i :5555

# Kill process if needed
kill -9 <PID>
```

**Frontend won't start:**
```bash
# Clear cache and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install
```

**Database connection issues:**
- The app runs in test mode without database by default
- Check console logs for connection status

#### **Logs and Debugging**
```bash
# View backend logs
cd backend && npm run dev

# View frontend logs
cd frontend && npm start

# View admin logs
cd admin && npm start
```

### 📱 **Mobile Development**

#### **Start Mobile App**
```bash
cd mobile
npm start
```

#### **Test on Device**
1. Install Expo Go app on your phone
2. Scan the QR code from the terminal
3. Test the mobile app features

### 🚀 **Production Deployment**

#### **Quick Production Test**
```bash
# Build for production
npm run build

# Test production build locally
cd frontend && npx serve -s build
```

#### **Deploy to Vercel**
```bash
# Deploy frontend
cd frontend && vercel --prod

# Deploy admin
cd admin && vercel --prod
```

### 📊 **Current Status**

#### **✅ Working Features**
- User authentication (login/register)
- Dashboard with widgets
- Real-time messaging
- File upload and sharing
- Video call interface
- Admin dashboard
- Mobile app structure

#### **⚠️ Development Mode Features**
- SQLite database (development)
- Basic socket connections
- Test data and mock responses
- Local file storage

#### **🔧 Needs Production Setup**
- PostgreSQL database
- Production environment variables
- SSL certificates
- Domain configuration
- Monitoring and logging

### 🎯 **Next Steps**

1. **Test the application** using the quick start guide
2. **Set up production database** when ready for deployment
3. **Configure environment variables** for production
4. **Deploy to production** using the deployment scripts

### 📞 **Support**

If you encounter issues:
1. Check the console logs for error messages
2. Verify all services are running on correct ports
3. Ensure all dependencies are installed
4. Check the troubleshooting section above

---

**Happy Coding! 🎉**

The FamEduConnect application is now ready for development and testing. 