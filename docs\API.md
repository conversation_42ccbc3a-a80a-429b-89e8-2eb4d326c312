# API Documentation

## Authentication

### POST /auth/register
Register a new user.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "parent"
}
```

**Response:**
```json
{
  "token": "jwt_token_here",
  "user": {
    "id": 1,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "role": "parent"
  }
}
```

### POST /auth/login
Login with existing credentials.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

## Classes

### GET /classes
Get all classes for the authenticated user.

### POST /classes
Create a new class (teachers only).

### GET /classes/:id
Get specific class details.

## Messages

### GET /messages/:conversationId
Get messages for a conversation.

### POST /messages
Send a new message.

## Students

### GET /students
Get students (filtered by role).

### POST /students
Add a new student.

## WebRTC

### POST /webrtc/call
Initiate a video call.

### POST /webrtc/answer
Answer a video call.

### POST /webrtc/ice-candidate
Exchange ICE candidates.