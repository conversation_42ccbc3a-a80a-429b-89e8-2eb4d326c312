const axios = require('axios');
const fs = require('fs');

// Configuration for quick backend stress test
const CONFIG = {
  BACKEND_URL: 'http://localhost:3002',
  TEST_DURATION: 60000, // 1 minute
  CONCURRENT_USERS: 10,
  REQUESTS_PER_SECOND: 20,
  TIMEOUT: 5000
};

// Test Results
const results = {
  health: { success: 0, failed: 0 },
  auth: { success: 0, failed: 0 },
  api: { success: 0, failed: 0 },
  performance: { 
    responseTimes: [],
    avgResponseTime: 0,
    maxResponseTime: 0,
    minResponseTime: Infinity
  },
  startTime: Date.now()
};

// Test credentials
const TEST_CREDENTIALS = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' }
];

// Performance metrics
function updatePerformanceMetrics(responseTime) {
  results.performance.responseTimes.push(responseTime);
  const times = results.performance.responseTimes;
  results.performance.avgResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
  results.performance.maxResponseTime = Math.max(results.performance.maxResponseTime, responseTime);
  results.performance.minResponseTime = Math.min(results.performance.minResponseTime, responseTime);
}

// Health endpoint test
async function testHealthEndpoint() {
  console.log('🏥 Testing Health Endpoint...');
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, {
          timeout: CONFIG.TIMEOUT
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.health.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.health.failed++;
        }
      } catch (error) {
        results.health.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Auth endpoint test
async function testAuthEndpoint() {
  console.log('🔐 Testing Authentication...');
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    const credentials = TEST_CREDENTIALS[i % TEST_CREDENTIALS.length];
    
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.post(`${CONFIG.BACKEND_URL}/api/auth/login`, credentials, {
          timeout: CONFIG.TIMEOUT,
          headers: { 'Content-Type': 'application/json' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.auth.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.auth.failed++;
        }
      } catch (error) {
        results.auth.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// API endpoints test
async function testApiEndpoints() {
  console.log('🔧 Testing API Endpoints...');
  
  const endpoints = ['/api/test', '/api/users/profile', '/api/messages'];
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    setInterval(async () => {
      try {
        const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}${endpoint}`, {
          timeout: CONFIG.TIMEOUT
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.api.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.api.failed++;
        }
      } catch (error) {
        results.api.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Main test function
async function runQuickBackendStressTest() {
  console.log('🚀 Starting Quick Backend Stress Test...');
  console.log(`⏱️  Duration: ${CONFIG.TEST_DURATION / 1000}s`);
  console.log(`👥 Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`📡 Requests/Second: ${CONFIG.REQUESTS_PER_SECOND}`);
  console.log('');
  
  // Health check
  try {
    const healthResponse = await axios.get(`${CONFIG.BACKEND_URL}/api/health`);
    console.log('✅ Backend is healthy');
    console.log(`📊 Backend Status: ${JSON.stringify(healthResponse.data)}`);
  } catch (error) {
    console.log('❌ Backend health check failed:', error.message);
    process.exit(1);
  }
  console.log('');
  
  // Start tests
  testHealthEndpoint();
  testAuthEndpoint();
  testApiEndpoints();
  
  // Progress updates
  const progressInterval = setInterval(() => {
    const elapsed = Date.now() - results.startTime;
    const progress = Math.round((elapsed / CONFIG.TEST_DURATION) * 100);
    const totalRequests = results.health.success + results.health.failed + 
                         results.auth.success + results.auth.failed +
                         results.api.success + results.api.failed;
    console.log(`📊 Progress: ${progress}% | Requests: ${totalRequests} | Avg Response: ${Math.round(results.performance.avgResponseTime)}ms`);
  }, 15000);
  
  // Complete test after duration
  setTimeout(() => {
    clearInterval(progressInterval);
    
    const totalTime = Date.now() - results.startTime;
    const totalRequests = results.health.success + results.health.failed + 
                         results.auth.success + results.auth.failed +
                         results.api.success + results.api.failed;
    const successfulRequests = results.health.success + results.auth.success + results.api.success;
    
    console.log('\n' + '='.repeat(60));
    console.log('📈 QUICK BACKEND STRESS TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`⏱️  Duration: ${Math.round(totalTime / 1000)}s`);
    console.log(`📡 Total Requests: ${totalRequests}`);
    console.log(`⚡ Requests/Second: ${Math.round(totalRequests / (totalTime / 1000))}`);
    console.log(`✅ Success Rate: ${Math.round((successfulRequests / totalRequests) * 100)}%`);
    console.log('');
    
    console.log('🏥 Health Endpoint:');
    console.log(`   ✅ Success: ${results.health.success}`);
    console.log(`   ❌ Failed: ${results.health.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.health.success / (results.health.success + results.health.failed)) * 100)}%`);
    console.log('');
    
    console.log('🔐 Authentication:');
    console.log(`   ✅ Success: ${results.auth.success}`);
    console.log(`   ❌ Failed: ${results.auth.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.auth.success / (results.auth.success + results.auth.failed)) * 100)}%`);
    console.log('');
    
    console.log('🔧 API Endpoints:');
    console.log(`   ✅ Success: ${results.api.success}`);
    console.log(`   ❌ Failed: ${results.api.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.api.success / (results.api.success + results.api.failed)) * 100)}%`);
    console.log('');
    
    console.log('⚡ Performance:');
    console.log(`   📊 Avg Response Time: ${Math.round(results.performance.avgResponseTime)}ms`);
    console.log(`   🐌 Min Response Time: ${Math.round(results.performance.minResponseTime)}ms`);
    console.log(`   🚀 Max Response Time: ${Math.round(results.performance.maxResponseTime)}ms`);
    console.log('');
    
    const overallSuccessRate = Math.round((successfulRequests / totalRequests) * 100);
    if (overallSuccessRate >= 95) {
      console.log('✅ Backend is performing excellently under stress!');
    } else if (overallSuccessRate >= 80) {
      console.log('⚠️  Backend is performing well but could be optimized.');
    } else {
      console.log('❌ Backend needs optimization - high failure rate.');
    }
    
    console.log('\n' + '='.repeat(60));
    
    // Save report
    const report = {
      timestamp: new Date().toISOString(),
      duration: Math.round(totalTime / 1000),
      totalRequests,
      successRate: overallSuccessRate,
      results,
      performance: {
        avgResponseTime: Math.round(results.performance.avgResponseTime),
        minResponseTime: Math.round(results.performance.minResponseTime),
        maxResponseTime: Math.round(results.performance.maxResponseTime)
      }
    };
    
    fs.writeFileSync('quick-backend-stress-report.json', JSON.stringify(report, null, 2));
    console.log('📄 Report saved to: quick-backend-stress-report.json');
    
    process.exit(0);
  }, CONFIG.TEST_DURATION);
}

// Run the test
runQuickBackendStressTest().catch(console.error);
