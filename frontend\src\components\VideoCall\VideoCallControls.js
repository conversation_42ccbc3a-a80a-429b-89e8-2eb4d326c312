import React from 'react';
import {
  MicrophoneIcon,
  VideoCameraIcon,
  PhoneXMarkIcon,
  SpeakerWaveIcon,
  ComputerDesktopIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import {
  MicrophoneIcon as MicrophoneIconSolid,
  VideoCameraIcon as VideoCameraIconSolid,
  SpeakerWaveIcon as SpeakerWaveIconSolid
} from '@heroicons/react/24/solid';

const VideoCallControls = ({
  isMuted = false,
  isVideoOff = false,
  isSpeakerOn = true,
  isScreenSharing = false,
  onToggleMute,
  onToggleVideo,
  onToggleSpeaker,
  onToggleScreenShare,
  onToggleChat,
  onOpenSettings,
  onEndCall,
  showChat = false
}) => {
  return (
    <div className="flex items-center justify-center space-x-4 p-4 bg-gray-900 bg-opacity-75 rounded-lg">
      {/* Mute/Unmute */}
      <button
        onClick={onToggleMute}
        className={`p-3 rounded-full transition-colors ${
          isMuted
            ? 'bg-red-600 hover:bg-red-700 text-white'
            : 'bg-gray-700 hover:bg-gray-600 text-white'
        }`}
        title={isMuted ? 'Unmute' : 'Mute'}
      >
        {isMuted ? (
          <MicrophoneIconSolid className="h-6 w-6" />
        ) : (
          <MicrophoneIcon className="h-6 w-6" />
        )}
      </button>

      {/* Video On/Off */}
      <button
        onClick={onToggleVideo}
        className={`p-3 rounded-full transition-colors ${
          isVideoOff
            ? 'bg-red-600 hover:bg-red-700 text-white'
            : 'bg-gray-700 hover:bg-gray-600 text-white'
        }`}
        title={isVideoOff ? 'Turn on camera' : 'Turn off camera'}
      >
        {isVideoOff ? (
          <VideoCameraIconSolid className="h-6 w-6" />
        ) : (
          <VideoCameraIcon className="h-6 w-6" />
        )}
      </button>

      {/* Speaker On/Off */}
      <button
        onClick={onToggleSpeaker}
        className={`p-3 rounded-full transition-colors ${
          isSpeakerOn
            ? 'bg-gray-700 hover:bg-gray-600 text-white'
            : 'bg-red-600 hover:bg-red-700 text-white'
        }`}
        title={isSpeakerOn ? 'Turn off speaker' : 'Turn on speaker'}
      >
        {isSpeakerOn ? (
          <SpeakerWaveIcon className="h-6 w-6" />
        ) : (
          <SpeakerWaveIconSolid className="h-6 w-6" />
        )}
      </button>

      {/* Screen Share */}
      <button
        onClick={onToggleScreenShare}
        className={`p-3 rounded-full transition-colors ${
          isScreenSharing
            ? 'bg-blue-600 hover:bg-blue-700 text-white'
            : 'bg-gray-700 hover:bg-gray-600 text-white'
        }`}
        title={isScreenSharing ? 'Stop sharing' : 'Share screen'}
      >
        <ComputerDesktopIcon className="h-6 w-6" />
      </button>

      {/* Chat Toggle */}
      <button
        onClick={onToggleChat}
        className={`p-3 rounded-full transition-colors ${
          showChat
            ? 'bg-blue-600 hover:bg-blue-700 text-white'
            : 'bg-gray-700 hover:bg-gray-600 text-white'
        }`}
        title="Toggle chat"
      >
        <ChatBubbleLeftRightIcon className="h-6 w-6" />
      </button>

      {/* Settings */}
      <button
        onClick={onOpenSettings}
        className="p-3 rounded-full bg-gray-700 hover:bg-gray-600 text-white transition-colors"
        title="Settings"
      >
        <Cog6ToothIcon className="h-6 w-6" />
      </button>

      {/* End Call */}
      <button
        onClick={onEndCall}
        className="p-3 rounded-full bg-red-600 hover:bg-red-700 text-white transition-colors"
        title="End call"
      >
        <PhoneXMarkIcon className="h-6 w-6" />
      </button>
    </div>
  );
};

export default VideoCallControls;