const express = require('express');
const { Message, Translation, User, Class } = require('../models');
const { body, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const { Translate } = require('@google-cloud/translate').v2;

const router = express.Router();
const translate = new Translate();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/messages/');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${Math.round(Math.random() * 1E9)}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt|mp3|wav|mp4/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// Get messages for a user
router.get('/', authMiddleware, async (req, res) => {
  try {
    const { page = 1, limit = 20, classId, recipientId } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {
      [require('sequelize').Op.or]: [
        { senderId: req.user.userId },
        { recipientId: req.user.userId }
      ]
    };

    if (classId) {
      whereClause.classId = classId;
    }

    if (recipientId) {
      whereClause[require('sequelize').Op.or] = [
        { senderId: req.user.userId, recipientId },
        { senderId: recipientId, recipientId: req.user.userId }
      ];
    }

    const messages = await Message.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: User,
          as: 'recipient',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: Class,
          as: 'class',
          attributes: ['id', 'className', 'classCode']
        },
        {
          model: Translation,
          as: 'translations'
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      messages: messages.rows,
      totalCount: messages.count,
      currentPage: parseInt(page),
      totalPages: Math.ceil(messages.count / limit)
    });
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Send a message
router.post('/', authMiddleware, upload.array('attachments', 5), [
  body('content').trim().isLength({ min: 1 }),
  body('messageType').isIn(['text', 'voice', 'image', 'file', 'video', 'announcement']),
  body('priority').optional().isIn(['low', 'normal', 'high', 'urgent'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      content,
      recipientId,
      classId,
      messageType = 'text',
      priority = 'normal',
      scheduledFor
    } = req.body;

    // Validate recipient or class
    if (!recipientId && !classId) {
      return res.status(400).json({ message: 'Either recipientId or classId is required' });
    }

    // Process attachments
    const attachments = req.files ? req.files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path
    })) : [];

    // Create message
    const message = await Message.create({
      senderId: req.user.userId,
      recipientId,
      classId,
      content,
      messageType,
      priority,
      attachments,
      scheduledFor: scheduledFor ? new Date(scheduledFor) : null,
      isSent: !scheduledFor,
      sentAt: !scheduledFor ? new Date() : null
    });

    // Load message with associations
    const fullMessage = await Message.findByPk(message.id, {
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: User,
          as: 'recipient',
          attributes: ['id', 'firstName', 'lastName', 'profilePicture']
        },
        {
          model: Class,
          as: 'class',
          attributes: ['id', 'className', 'classCode']
        }
      ]
    });

    // Auto-translate if needed
    if (recipientId) {
      const recipient = await User.findByPk(recipientId);
      if (recipient && recipient.preferredLanguage !== 'en') {
        await translateMessage(message.id, content, 'en', recipient.preferredLanguage);
      }
    }

    // Emit real-time message via Socket.IO
    const io = req.app.get('io');
    if (io) {
      if (recipientId) {
        io.to(`user_${recipientId}`).emit('new_message', fullMessage);
      }
      if (classId) {
        io.to(`class_${classId}`).emit('new_message', fullMessage);
      }
    }

    res.status(201).json(fullMessage);
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Mark message as read
router.patch('/:messageId/read', authMiddleware, async (req, res) => {
  try {
    const { messageId } = req.params;

    const message = await Message.findOne({
      where: {
        id: messageId,
        recipientId: req.user.userId
      }
    });

    if (!message) {
      return res.status(404).json({ message: 'Message not found' });
    }

    await message.update({
      isRead: true,
      readAt: new Date()
    });

    res.json({ message: 'Message marked as read' });
  } catch (error) {
    console.error('Mark read error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get message translations
router.get('/:messageId/translations', authMiddleware, async (req, res) => {
  try {
    const { messageId } = req.params;
    const { targetLanguage } = req.query;

    const message = await Message.findByPk(messageId);
    if (!message) {
      return res.status(404).json({ message: 'Message not found' });
    }

    let translation = await Translation.findOne({
      where: {
        messageId,
        targetLanguage
      }
    });

    if (!translation) {
      // Create new translation
      translation = await translateMessage(messageId, message.content, message.originalLanguage, targetLanguage);
    }

    res.json(translation);
  } catch (error) {
    console.error('Get translation error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete message
router.delete('/:messageId', authMiddleware, async (req, res) => {
  try {
    const { messageId } = req.params;

    const message = await Message.findOne({
      where: {
        id: messageId,
        senderId: req.user.userId
      }
    });

    if (!message) {
      return res.status(404).json({ message: 'Message not found or unauthorized' });
    }

    await message.update({
      isDeleted: true,
      deletedAt: new Date()
    });

    res.json({ message: 'Message deleted successfully' });
  } catch (error) {
    console.error('Delete message error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function to translate message
async function translateMessage(messageId, text, sourceLanguage, targetLanguage) {
  try {
    const [translation] = await translate.translate(text, {
      from: sourceLanguage,
      to: targetLanguage
    });

    return await Translation.create({
      messageId,
      originalText: text,
      translatedText: translation,
      sourceLanguage,
      targetLanguage,
      translationService: 'google',
      confidence: 0.95 // Google Translate doesn't provide confidence scores
    });
  } catch (error) {
    console.error('Translation error:', error);
    throw error;
  }
}

module.exports = router;