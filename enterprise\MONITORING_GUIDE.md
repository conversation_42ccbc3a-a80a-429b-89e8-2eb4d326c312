# FamEduConnect Enterprise Monitoring Guide

## Overview
This guide provides comprehensive instructions for monitoring the FamEduConnect enterprise deployment using Grafana dashboards, Prometheus metrics, and alerting systems. It covers setup, configuration, and best practices for monitoring system health and performance.

## 🎯 Monitoring Status: READY TO DEPLOY

### ✅ What You Need to Monitor

#### 1. **Application Performance Monitoring**
**Purpose:** Track application health, performance, and user experience
**Key Metrics:**
- API response times (p50, p95, p99)
- Request rate and throughput
- Error rates (4xx, 5xx)
- Active users and sessions
- Database query performance
- Real-time communication metrics

**Grafana Dashboard:** Application Overview
**Access:** http://grafana.fameduconnect.com/d/app-overview

#### 2. **Infrastructure Monitoring**
**Purpose:** Monitor Kubernetes cluster and infrastructure health
**Key Metrics:**
- CPU usage per node
- Memory utilization
- Disk space and I/O
- Network traffic
- Pod status and restarts
- Service mesh metrics

**Grafana Dashboard:** Infrastructure
**Access:** http://grafana.fameduconnect.com/d/infrastructure

#### 3. **Security Monitoring**
**Purpose:** Track security events and potential threats
**Key Metrics:**
- Failed login attempts
- Suspicious activities
- SSL certificate expiry
- Authentication failures
- Network policy violations
- Audit log analysis

**Grafana Dashboard:** Security Monitoring
**Access:** http://grafana.fameduconnect.com/d/security

## 🔧 Monitoring Setup

### Step 1: Deploy Monitoring Infrastructure
```powershell
# Run the monitoring setup script
cd FamEduConnect_Full_Codebase
.\enterprise\scripts\setup-monitoring.ps1 -Environment production

# Deploy monitoring configuration
cd enterprise/monitoring/production
.\deploy-monitoring.ps1
```

### Step 2: Configure Alert Notifications
Update the following in `alertmanager-config.yaml`:
- **Slack Webhook URL**: For team notifications
- **PagerDuty Routing Key**: For critical alerts
- **Email Configuration**: For admin notifications

### Step 3: Access Monitoring Dashboards
- **Grafana**: http://grafana.fameduconnect.com
- **Prometheus**: http://prometheus.fameduconnect.com
- **AlertManager**: http://alertmanager.fameduconnect.com
- **Elasticsearch**: http://elasticsearch.fameduconnect.com

## 📊 Dashboard Details

### Application Overview Dashboard
**Purpose:** Monitor application performance and user experience

**Panels:**
1. **API Response Time**
   - Metric: `rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])`
   - Thresholds: < 200ms (green), < 500ms (yellow), > 500ms (red)

2. **Active Users**
   - Metric: `sum(active_users_total)`
   - Real-time count of active users

3. **Error Rate**
   - Metric: `rate(http_requests_total{status=~"5.."}[5m])`
   - Alert threshold: > 0.1 (10% error rate)

4. **Database Connections**
   - Metric: `pg_stat_database_numbackends`
   - Monitor connection pool usage

### Infrastructure Dashboard
**Purpose:** Monitor Kubernetes cluster and infrastructure health

**Panels:**
1. **CPU Usage**
   - Metric: `100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)`
   - Alert threshold: > 80%

2. **Memory Usage**
   - Metric: `(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100`
   - Alert threshold: > 85%

3. **Disk Usage**
   - Metric: `(node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100`
   - Alert threshold: > 90%

4. **Pod Status**
   - Metric: `sum(kube_pod_status_phase)`
   - Monitor pod health across all phases

### Security Dashboard
**Purpose:** Monitor security events and potential threats

**Panels:**
1. **Failed Login Attempts**
   - Metric: `rate(failed_login_attempts_total[5m])`
   - Track authentication failures

2. **Suspicious Activities**
   - Metric: `rate(suspicious_activities_total[5m])`
   - Monitor potential security threats

3. **SSL Certificate Expiry**
   - Metric: `ssl_certificate_expiry_days`
   - Alert threshold: < 30 days

## 🚨 Alerting Rules

### Critical Alerts
**High Error Rate**
- Condition: `rate(http_requests_total{status=~"5.."}[5m]) > 0.1`
- Severity: Critical
- Action: Immediate investigation required

**Pod Restarting**
- Condition: `increase(kube_pod_container_status_restarts_total[1h]) > 5`
- Severity: Critical
- Action: Check pod logs and restart if necessary

**Database Connection Issues**
- Condition: `pg_stat_database_numbackends > 80`
- Severity: Critical
- Action: Investigate connection pool and database health

### Warning Alerts
**High CPU Usage**
- Condition: `100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80`
- Severity: Warning
- Action: Monitor and consider scaling

**High Memory Usage**
- Condition: `(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85`
- Severity: Warning
- Action: Check memory usage patterns

**SSL Certificate Expiring**
- Condition: `ssl_certificate_expiry_days < 30`
- Severity: Warning
- Action: Renew SSL certificate

## 📈 Performance Monitoring

### Real-Time Monitoring Script
```powershell
# Run continuous monitoring
cd enterprise/monitoring/production
.\monitor-performance.ps1 -Continuous -Interval 30

# Run single snapshot
.\monitor-performance.ps1
```

### Key Performance Indicators (KPIs)

#### Application KPIs
- **Response Time**: < 200ms (p95)
- **Error Rate**: < 1%
- **Availability**: > 99.9%
- **Throughput**: Monitor request rate trends

#### Infrastructure KPIs
- **CPU Utilization**: < 80%
- **Memory Usage**: < 85%
- **Disk Space**: > 10% free
- **Pod Health**: 100% running

#### Security KPIs
- **Failed Logins**: < 10 per hour
- **Suspicious Activities**: 0
- **SSL Certificate**: > 30 days validity

## 🔍 Troubleshooting

### Common Monitoring Issues

#### 1. **Dashboard Not Loading**
```bash
# Check Grafana pod status
kubectl get pods -n fameduconnect -l app=grafana

# Check Grafana logs
kubectl logs -n fameduconnect -l app=grafana

# Verify service endpoints
kubectl get endpoints -n fameduconnect grafana
```

#### 2. **No Metrics Available**
```bash
# Check Prometheus targets
kubectl port-forward -n fameduconnect svc/prometheus 9090:9090
# Then visit http://localhost:9090/targets

# Check Prometheus pod status
kubectl get pods -n fameduconnect -l app=prometheus

# Verify metrics endpoints
kubectl exec -it <prometheus-pod> -- curl localhost:9090/metrics
```

#### 3. **Alerts Not Firing**
```bash
# Check PrometheusRule status
kubectl get prometheusrules -n fameduconnect

# Check alert rules
kubectl describe prometheusrule fameduconnect-alerts -n fameduconnect

# Test alert expression
kubectl port-forward -n fameduconnect svc/prometheus 9090:9090
# Then visit http://localhost:9090/graph and test expressions
```

#### 4. **No Notifications**
```bash
# Check AlertManager status
kubectl get pods -n fameduconnect -l app=alertmanager

# Check AlertManager logs
kubectl logs -n fameduconnect -l app=alertmanager

# Verify configuration
kubectl get configmap alertmanager-config -n fameduconnect -o yaml
```

### Performance Optimization

#### Dashboard Optimization
- Use appropriate time ranges (1h for real-time, 24h for trends)
- Limit query complexity and use rate() functions
- Aggregate data when possible
- Use caching for frequently accessed data

#### Alert Optimization
- Set appropriate thresholds based on baseline metrics
- Use `for:` clause to prevent alert flapping
- Group related alerts to reduce noise
- Test alert rules regularly

#### Resource Optimization
- Monitor Prometheus storage usage
- Configure retention policies
- Use recording rules for complex queries
- Optimize scrape intervals

## 📋 Monitoring Checklist

### Pre-Deployment Checklist
- [ ] **Monitoring Infrastructure**: Prometheus, Grafana, AlertManager deployed
- [ ] **Dashboard Configuration**: All dashboards created and configured
- [ ] **Alert Rules**: PrometheusRule created with appropriate thresholds
- [ ] **Notification Channels**: Slack, PagerDuty, email configured
- [ ] **Metrics Collection**: Application metrics exposed and scraped

### Post-Deployment Checklist
- [ ] **Dashboard Access**: All dashboards accessible and loading data
- [ ] **Alert Testing**: Test alert rules and notification delivery
- [ ] **Performance Baseline**: Establish baseline metrics for all KPIs
- [ ] **Documentation**: Team trained on monitoring tools and procedures
- [ ] **Escalation Procedures**: Define alert escalation and response procedures

### Ongoing Monitoring Checklist
- [ ] **Daily**: Review dashboard metrics and alerts
- [ ] **Weekly**: Analyze performance trends and optimize thresholds
- [ ] **Monthly**: Review and update monitoring configuration
- [ ] **Quarterly**: Conduct monitoring system health check

## 🛠️ Advanced Monitoring

### Custom Metrics
Add custom metrics to your application:
```javascript
// Example: Track active users
const activeUsers = new prometheus.Gauge({
  name: 'active_users_total',
  help: 'Total number of active users'
});

// Update metric
activeUsers.set(connectedUsers.length);
```

### Custom Dashboards
Create custom dashboards for specific use cases:
- **Business Metrics**: User engagement, feature usage
- **Operational Metrics**: Deployment frequency, rollback rates
- **Cost Metrics**: Resource utilization, cost optimization

### Log Monitoring
Integrate with ELK stack for log analysis:
- **Elasticsearch**: Log storage and indexing
- **Logstash**: Log processing and enrichment
- **Kibana**: Log visualization and analysis

## 📞 Support & Documentation

### Available Resources
- `enterprise/scripts/setup-monitoring.ps1` - Monitoring setup script
- `enterprise/monitoring/{environment}/` - Environment-specific configurations
- `enterprise/monitoring/{environment}/MONITORING_GUIDE.md` - Detailed guide
- `enterprise/monitoring/{environment}/monitor-performance.ps1` - Performance monitoring script

### Useful Commands
```bash
# Check all monitoring pods
kubectl get pods -n fameduconnect -l app=grafana
kubectl get pods -n fameduconnect -l app=prometheus
kubectl get pods -n fameduconnect -l app=alertmanager

# Check monitoring services
kubectl get svc -n fameduconnect | grep -E "(grafana|prometheus|alertmanager)"

# View monitoring logs
kubectl logs -n fameduconnect -l app=grafana --tail=100
kubectl logs -n fameduconnect -l app=prometheus --tail=100

# Port forward for local access
kubectl port-forward -n fameduconnect svc/grafana 3000:3000
kubectl port-forward -n fameduconnect svc/prometheus 9090:9090
```

### Best Practices
1. **Start Simple**: Begin with basic metrics and expand gradually
2. **Set Realistic Thresholds**: Base alerts on actual performance data
3. **Document Everything**: Maintain runbooks for common issues
4. **Regular Reviews**: Schedule regular monitoring system reviews
5. **Team Training**: Ensure all team members understand monitoring tools

---

## 🎉 Ready for Monitoring!

Your monitoring infrastructure is now ready. Deploy the monitoring configuration and start tracking your system health with comprehensive dashboards and alerting.

**Next Action**: Deploy monitoring configuration and access Grafana dashboards to monitor system health in real-time. 