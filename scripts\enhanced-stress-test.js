const axios = require('axios');
const fs = require('fs');

// Enhanced stress test configuration
const CONFIG = {
  BACKEND_URL: 'http://localhost:3002',
  FRONTEND_URL: 'http://localhost:3000',
  TEST_DURATION: 180000, // 3 minutes
  CONCURRENT_USERS: 50,
  REQUESTS_PER_SECOND: 100,
  TIMEOUT: 15000,
  RAMP_UP_TIME: 30000, // 30 seconds to ramp up
  COOL_DOWN_TIME: 30000 // 30 seconds to cool down
};

// Enhanced test results tracking
const results = {
  backend: {
    health: { success: 0, failed: 0, errors: [] },
    auth: { success: 0, failed: 0, errors: [] },
    api: { success: 0, failed: 0, errors: [] },
    cache: { hits: 0, misses: 0 }
  },
  frontend: {
    pages: { success: 0, failed: 0, errors: [] },
    static: { success: 0, failed: 0, errors: [] },
    serviceWorker: { active: false, cacheHits: 0 }
  },
  performance: {
    backend: { 
      responseTimes: [], 
      avgResponseTime: 0, 
      maxResponseTime: 0, 
      minResponseTime: Infinity,
      p95ResponseTime: 0,
      p99ResponseTime: 0
    },
    frontend: { 
      responseTimes: [], 
      avgResponseTime: 0, 
      maxResponseTime: 0, 
      minResponseTime: Infinity,
      p95ResponseTime: 0,
      p99ResponseTime: 0
    }
  },
  system: {
    memoryUsage: [],
    cpuUsage: [],
    errorRates: []
  },
  startTime: Date.now(),
  phases: {
    rampUp: { start: 0, end: 0 },
    steady: { start: 0, end: 0 },
    coolDown: { start: 0, end: 0 }
  }
};

// Test data and endpoints
const TEST_CREDENTIALS = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' }
];

const BACKEND_ENDPOINTS = [
  '/api/health',
  '/api/test',
  '/api/auth/login',
  '/api/users/profile',
  '/api/messages',
  '/api/students',
  '/api/classes'
];

const FRONTEND_PAGES = [
  '/',
  '/login',
  '/dashboard',
  '/messages',
  '/profile',
  '/classes',
  '/assignments'
];

// Performance tracking utilities
function updateBackendPerformance(responseTime) {
  results.performance.backend.responseTimes.push(responseTime);
  const times = results.performance.backend.responseTimes;
  results.performance.backend.avgResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
  results.performance.backend.maxResponseTime = Math.max(results.performance.backend.maxResponseTime, responseTime);
  results.performance.backend.minResponseTime = Math.min(results.performance.backend.minResponseTime, responseTime);
}

function updateFrontendPerformance(responseTime) {
  results.performance.frontend.responseTimes.push(responseTime);
  const times = results.performance.frontend.responseTimes;
  results.performance.frontend.avgResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
  results.performance.frontend.maxResponseTime = Math.max(results.performance.frontend.maxResponseTime, responseTime);
  results.performance.frontend.minResponseTime = Math.min(results.performance.frontend.minResponseTime, responseTime);
}

function calculatePercentile(arr, percentile) {
  if (arr.length === 0) return 0;
  const sorted = arr.slice().sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index] || 0;
}

// Enhanced backend stress tests
async function stressTestBackend(intensity = 1.0) {
  const adjustedUsers = Math.floor(CONFIG.CONCURRENT_USERS * intensity);
  const adjustedRPS = Math.floor(CONFIG.REQUESTS_PER_SECOND * intensity);
  
  // Health endpoint test
  for (let i = 0; i < Math.floor(adjustedUsers / 3); i++) {
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, { 
          timeout: CONFIG.TIMEOUT,
          headers: { 'Cache-Control': 'no-cache' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.backend.health.success++;
          updateBackendPerformance(responseTime);
          
          // Check for cache indicators
          if (response.headers['x-cache'] === 'HIT') {
            results.backend.cache.hits++;
          } else {
            results.backend.cache.misses++;
          }
        } else {
          results.backend.health.failed++;
        }
      } catch (error) {
        results.backend.health.failed++;
        results.backend.health.errors.push(error.message);
      }
    }, 1000 / adjustedRPS);
  }
  
  // Auth endpoint test
  for (let i = 0; i < Math.floor(adjustedUsers / 3); i++) {
    const credentials = TEST_CREDENTIALS[i % TEST_CREDENTIALS.length];
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.post(`${CONFIG.BACKEND_URL}/api/auth/login`, credentials, {
          timeout: CONFIG.TIMEOUT,
          headers: { 'Content-Type': 'application/json' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.backend.auth.success++;
          updateBackendPerformance(responseTime);
        } else {
          results.backend.auth.failed++;
        }
      } catch (error) {
        results.backend.auth.failed++;
        results.backend.auth.errors.push(error.message);
      }
    }, 1000 / adjustedRPS);
  }
  
  // API endpoints test
  for (let i = 0; i < Math.floor(adjustedUsers / 3); i++) {
    setInterval(async () => {
      try {
        const endpoint = BACKEND_ENDPOINTS[Math.floor(Math.random() * BACKEND_ENDPOINTS.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}${endpoint}`, { 
          timeout: CONFIG.TIMEOUT 
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.backend.api.success++;
          updateBackendPerformance(responseTime);
        } else {
          results.backend.api.failed++;
        }
      } catch (error) {
        results.backend.api.failed++;
        results.backend.api.errors.push(error.message);
      }
    }, 1000 / adjustedRPS);
  }
}

// Enhanced frontend stress tests
async function stressTestFrontend(intensity = 1.0) {
  const adjustedUsers = Math.floor(CONFIG.CONCURRENT_USERS * intensity);
  const adjustedRPS = Math.floor(CONFIG.REQUESTS_PER_SECOND * intensity);
  
  // Check for service worker
  try {
    const swResponse = await axios.get(`${CONFIG.FRONTEND_URL}/sw.js`);
    if (swResponse.status === 200) {
      results.frontend.serviceWorker.active = true;
    }
  } catch (error) {
    console.log('Service worker not found or not active');
  }
  
  // Pages test
  for (let i = 0; i < Math.floor(adjustedUsers / 2); i++) {
    setInterval(async () => {
      try {
        const page = FRONTEND_PAGES[Math.floor(Math.random() * FRONTEND_PAGES.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.FRONTEND_URL}${page}`, {
          timeout: CONFIG.TIMEOUT,
          headers: { 
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
          }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.frontend.pages.success++;
          updateFrontendPerformance(responseTime);
          
          // Check for service worker cache
          if (response.headers['x-sw-cache'] === 'HIT') {
            results.frontend.serviceWorker.cacheHits++;
          }
        } else {
          results.frontend.pages.failed++;
        }
      } catch (error) {
        results.frontend.pages.failed++;
        results.frontend.pages.errors.push(error.message);
      }
    }, 1000 / adjustedRPS);
  }
  
  // Static resources test
  for (let i = 0; i < Math.floor(adjustedUsers / 4); i++) {
    setInterval(async () => {
      try {
        const resources = ['/favicon.ico', '/manifest.json', '/static/css/main.css'];
        const resource = resources[Math.floor(Math.random() * resources.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.FRONTEND_URL}${resource}`, { 
          timeout: CONFIG.TIMEOUT 
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 400) {
          results.frontend.static.success++;
          updateFrontendPerformance(responseTime);
        } else {
          results.frontend.static.failed++;
        }
      } catch (error) {
        results.frontend.static.failed++;
      }
    }, 1000 / (adjustedRPS / 2));
  }
}

// System health monitoring
async function monitorSystemHealth() {
  setInterval(async () => {
    try {
      // Get backend health with memory info
      const healthResponse = await axios.get(`${CONFIG.BACKEND_URL}/api/health`);
      if (healthResponse.data.memory) {
        results.system.memoryUsage.push({
          timestamp: Date.now(),
          ...healthResponse.data.memory
        });
      }
      
      // Calculate current error rates
      const totalRequests = results.backend.health.success + results.backend.health.failed +
                           results.backend.auth.success + results.backend.auth.failed +
                           results.backend.api.success + results.backend.api.failed +
                           results.frontend.pages.success + results.frontend.pages.failed +
                           results.frontend.static.success + results.frontend.static.failed;
      
      const totalErrors = results.backend.health.failed + results.backend.auth.failed +
                         results.backend.api.failed + results.frontend.pages.failed +
                         results.frontend.static.failed;
      
      const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;
      
      results.system.errorRates.push({
        timestamp: Date.now(),
        errorRate
      });
      
    } catch (error) {
      console.error('System monitoring error:', error.message);
    }
  }, 10000); // Every 10 seconds
}

// Enhanced reporting
function generateEnhancedReport() {
  const totalTime = Date.now() - results.startTime;
  
  // Calculate percentiles
  results.performance.backend.p95ResponseTime = calculatePercentile(results.performance.backend.responseTimes, 95);
  results.performance.backend.p99ResponseTime = calculatePercentile(results.performance.backend.responseTimes, 99);
  results.performance.frontend.p95ResponseTime = calculatePercentile(results.performance.frontend.responseTimes, 95);
  results.performance.frontend.p99ResponseTime = calculatePercentile(results.performance.frontend.responseTimes, 99);
  
  const backendTotal = results.backend.health.success + results.backend.health.failed +
                      results.backend.auth.success + results.backend.auth.failed +
                      results.backend.api.success + results.backend.api.failed;
  
  const frontendTotal = results.frontend.pages.success + results.frontend.pages.failed +
                       results.frontend.static.success + results.frontend.static.failed;
  
  const backendSuccess = results.backend.health.success + results.backend.auth.success + results.backend.api.success;
  const frontendSuccess = results.frontend.pages.success + results.frontend.static.success;
  
  const totalRequests = backendTotal + frontendTotal;
  const totalSuccess = backendSuccess + frontendSuccess;
  
  return {
    timestamp: new Date().toISOString(),
    testConfig: CONFIG,
    duration: Math.round(totalTime / 1000),
    totalRequests,
    requestsPerSecond: Math.round(totalRequests / (totalTime / 1000)),
    overallSuccessRate: Math.round((totalSuccess / totalRequests) * 100),
    backend: {
      totalRequests: backendTotal,
      successRate: Math.round((backendSuccess / backendTotal) * 100),
      performance: {
        avgResponseTime: Math.round(results.performance.backend.avgResponseTime),
        minResponseTime: Math.round(results.performance.backend.minResponseTime),
        maxResponseTime: Math.round(results.performance.backend.maxResponseTime),
        p95ResponseTime: Math.round(results.performance.backend.p95ResponseTime),
        p99ResponseTime: Math.round(results.performance.backend.p99ResponseTime)
      },
      cache: results.backend.cache,
      results: results.backend
    },
    frontend: {
      totalRequests: frontendTotal,
      successRate: Math.round((frontendSuccess / frontendTotal) * 100),
      performance: {
        avgResponseTime: Math.round(results.performance.frontend.avgResponseTime),
        minResponseTime: Math.round(results.performance.frontend.minResponseTime),
        maxResponseTime: Math.round(results.performance.frontend.maxResponseTime),
        p95ResponseTime: Math.round(results.performance.frontend.p95ResponseTime),
        p99ResponseTime: Math.round(results.performance.frontend.p99ResponseTime)
      },
      serviceWorker: results.frontend.serviceWorker,
      results: results.frontend
    },
    system: results.system,
    phases: results.phases
  };
}

// Main enhanced stress test execution
async function runEnhancedStressTest() {
  console.log('🚀 Starting Enhanced Stress Test with Optimizations...');
  console.log(`⏱️  Total Duration: ${CONFIG.TEST_DURATION / 1000}s`);
  console.log(`📈 Ramp-up: ${CONFIG.RAMP_UP_TIME / 1000}s`);
  console.log(`⚡ Steady State: ${(CONFIG.TEST_DURATION - CONFIG.RAMP_UP_TIME - CONFIG.COOL_DOWN_TIME) / 1000}s`);
  console.log(`📉 Cool-down: ${CONFIG.COOL_DOWN_TIME / 1000}s`);
  console.log(`👥 Max Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`📡 Max Requests/Second: ${CONFIG.REQUESTS_PER_SECOND}`);
  console.log('');

  // System health checks
  console.log('🏥 Performing System Health Checks...');
  try {
    const backendHealth = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, { timeout: 5000 });
    console.log('✅ Backend is healthy');
    console.log(`📊 Backend Status: ${JSON.stringify(backendHealth.data)}`);
  } catch (error) {
    console.log('❌ Backend health check failed:', error.message);
    process.exit(1);
  }

  try {
    const frontendHealth = await axios.get(`${CONFIG.FRONTEND_URL}/`, { timeout: 5000 });
    console.log('✅ Frontend is healthy');
    console.log(`📊 Frontend response size: ${frontendHealth.data.length} bytes`);
  } catch (error) {
    console.log('❌ Frontend health check failed:', error.message);
    process.exit(1);
  }
  console.log('');

  // Start system monitoring
  monitorSystemHealth();

  // Phase 1: Ramp-up
  console.log('📈 Phase 1: Ramp-up (Gradual Load Increase)');
  results.phases.rampUp.start = Date.now();

  const rampUpSteps = 10;
  const rampUpInterval = CONFIG.RAMP_UP_TIME / rampUpSteps;

  for (let step = 1; step <= rampUpSteps; step++) {
    const intensity = step / rampUpSteps;
    console.log(`   Step ${step}/${rampUpSteps}: ${Math.round(intensity * 100)}% intensity`);

    stressTestBackend(intensity);
    stressTestFrontend(intensity);

    await new Promise(resolve => setTimeout(resolve, rampUpInterval));
  }

  results.phases.rampUp.end = Date.now();

  // Phase 2: Steady state
  console.log('⚡ Phase 2: Steady State (Full Load)');
  results.phases.steady.start = Date.now();

  stressTestBackend(1.0);
  stressTestFrontend(1.0);

  // Progress reporting during steady state
  const steadyDuration = CONFIG.TEST_DURATION - CONFIG.RAMP_UP_TIME - CONFIG.COOL_DOWN_TIME;
  const progressInterval = setInterval(() => {
    const elapsed = Date.now() - results.phases.steady.start;
    const progress = Math.round((elapsed / steadyDuration) * 100);

    const backendTotal = results.backend.health.success + results.backend.health.failed +
                        results.backend.auth.success + results.backend.auth.failed +
                        results.backend.api.success + results.backend.api.failed;

    const frontendTotal = results.frontend.pages.success + results.frontend.pages.failed +
                         results.frontend.static.success + results.frontend.static.failed;

    const currentRPS = Math.round((backendTotal + frontendTotal) / ((Date.now() - results.startTime) / 1000));

    console.log(`📊 Steady State Progress: ${progress}% | Backend: ${backendTotal} | Frontend: ${frontendTotal} | RPS: ${currentRPS}`);
  }, 15000);

  await new Promise(resolve => setTimeout(resolve, steadyDuration));
  clearInterval(progressInterval);

  results.phases.steady.end = Date.now();

  // Phase 3: Cool-down
  console.log('📉 Phase 3: Cool-down (Gradual Load Decrease)');
  results.phases.coolDown.start = Date.now();

  // Gradually reduce load (this is simulated by not starting new intervals)
  await new Promise(resolve => setTimeout(resolve, CONFIG.COOL_DOWN_TIME));

  results.phases.coolDown.end = Date.now();

  // Generate and display results
  console.log('\n📊 Generating Enhanced Stress Test Report...');
  const report = generateEnhancedReport();

  console.log('\n' + '='.repeat(100));
  console.log('📈 ENHANCED STRESS TEST RESULTS WITH OPTIMIZATIONS');
  console.log('='.repeat(100));
  console.log(`⏱️  Total Duration: ${report.duration}s`);
  console.log(`📡 Total Requests: ${report.totalRequests.toLocaleString()}`);
  console.log(`⚡ Average RPS: ${report.requestsPerSecond}`);
  console.log(`✅ Overall Success Rate: ${report.overallSuccessRate}%`);
  console.log('');

  console.log('🔧 BACKEND PERFORMANCE:');
  console.log(`   📡 Total Requests: ${report.backend.totalRequests.toLocaleString()}`);
  console.log(`   ✅ Success Rate: ${report.backend.successRate}%`);
  console.log(`   📊 Avg Response: ${report.backend.performance.avgResponseTime}ms`);
  console.log(`   🐌 Min Response: ${report.backend.performance.minResponseTime}ms`);
  console.log(`   🚀 Max Response: ${report.backend.performance.maxResponseTime}ms`);
  console.log(`   📈 95th Percentile: ${report.backend.performance.p95ResponseTime}ms`);
  console.log(`   📈 99th Percentile: ${report.backend.performance.p99ResponseTime}ms`);
  console.log(`   💾 Cache Hits: ${report.backend.cache.hits} | Misses: ${report.backend.cache.misses}`);
  console.log(`   🏥 Health: ${results.backend.health.success}✅ / ${results.backend.health.failed}❌`);
  console.log(`   🔐 Auth: ${results.backend.auth.success}✅ / ${results.backend.auth.failed}❌`);
  console.log(`   🔧 API: ${results.backend.api.success}✅ / ${results.backend.api.failed}❌`);
  console.log('');

  console.log('📱 FRONTEND PERFORMANCE:');
  console.log(`   📡 Total Requests: ${report.frontend.totalRequests.toLocaleString()}`);
  console.log(`   ✅ Success Rate: ${report.frontend.successRate}%`);
  console.log(`   📊 Avg Response: ${report.frontend.performance.avgResponseTime}ms`);
  console.log(`   🐌 Min Response: ${report.frontend.performance.minResponseTime}ms`);
  console.log(`   🚀 Max Response: ${report.frontend.performance.maxResponseTime}ms`);
  console.log(`   📈 95th Percentile: ${report.frontend.performance.p95ResponseTime}ms`);
  console.log(`   📈 99th Percentile: ${report.frontend.performance.p99ResponseTime}ms`);
  console.log(`   🔧 Service Worker: ${report.frontend.serviceWorker.active ? 'Active' : 'Inactive'}`);
  console.log(`   💾 SW Cache Hits: ${report.frontend.serviceWorker.cacheHits}`);
  console.log(`   📱 Pages: ${results.frontend.pages.success}✅ / ${results.frontend.pages.failed}❌`);
  console.log(`   🎨 Static: ${results.frontend.static.success}✅ / ${results.frontend.static.failed}❌`);
  console.log('');

  // Performance analysis
  console.log('🎯 PERFORMANCE ANALYSIS:');

  const improvements = [];
  const issues = [];

  if (report.backend.successRate >= 95) {
    improvements.push('Backend stability is excellent');
  } else if (report.backend.successRate < 80) {
    issues.push('Backend stability needs immediate attention');
  }

  if (report.frontend.successRate >= 95) {
    improvements.push('Frontend stability is excellent');
  } else if (report.frontend.successRate < 80) {
    issues.push('Frontend stability needs immediate attention');
  }

  if (report.backend.performance.avgResponseTime < 500) {
    improvements.push('Backend response times are excellent');
  } else if (report.backend.performance.avgResponseTime > 1000) {
    issues.push('Backend response times need optimization');
  }

  if (report.frontend.performance.avgResponseTime < 1000) {
    improvements.push('Frontend response times are good');
  } else if (report.frontend.performance.avgResponseTime > 2000) {
    issues.push('Frontend response times need optimization');
  }

  if (report.backend.cache.hits > report.backend.cache.misses) {
    improvements.push('Backend caching is working effectively');
  } else {
    issues.push('Backend cache hit rate could be improved');
  }

  if (report.frontend.serviceWorker.active) {
    improvements.push('Service Worker is active and caching resources');
  } else {
    issues.push('Service Worker is not active - missing performance benefits');
  }

  if (improvements.length > 0) {
    console.log('   ✅ IMPROVEMENTS DETECTED:');
    improvements.forEach(improvement => console.log(`      • ${improvement}`));
  }

  if (issues.length > 0) {
    console.log('   ⚠️  ISSUES TO ADDRESS:');
    issues.forEach(issue => console.log(`      • ${issue}`));
  }

  if (issues.length === 0) {
    console.log('   🎉 EXCELLENT! All performance metrics are within acceptable ranges!');
  }

  console.log('\n' + '='.repeat(100));

  // Save detailed report
  fs.writeFileSync('enhanced-stress-test-report.json', JSON.stringify(report, null, 2));
  console.log('📄 Detailed report saved to: enhanced-stress-test-report.json');

  // Save CSV for analysis
  const csvData = [
    'Timestamp,Component,Endpoint,ResponseTime,Success',
    ...results.performance.backend.responseTimes.map((time, i) =>
      `${Date.now()},Backend,API,${time},true`
    ),
    ...results.performance.frontend.responseTimes.map((time, i) =>
      `${Date.now()},Frontend,Page,${time},true`
    )
  ].join('\n');

  fs.writeFileSync('enhanced-stress-test-data.csv', csvData);
  console.log('📊 Performance data saved to: enhanced-stress-test-data.csv');

  process.exit(0);
}

// Run the enhanced stress test
runEnhancedStressTest().catch(console.error);
