const axios = require('axios');
const fs = require('fs');

// Light optimized test configuration
const CONFIG = {
  BACKEND_URL: 'http://localhost:3002',
  FRONTEND_URL: 'http://localhost:3000',
  TEST_DURATION: 60000, // 1 minute
  CONCURRENT_USERS: 15,
  REQUESTS_PER_SECOND: 30,
  TIMEOUT: 8000
};

// Test results
const results = {
  backend: {
    health: { success: 0, failed: 0 },
    auth: { success: 0, failed: 0 },
    api: { success: 0, failed: 0 },
    cache: { hits: 0, misses: 0 }
  },
  frontend: {
    pages: { success: 0, failed: 0 },
    static: { success: 0, failed: 0 }
  },
  performance: {
    backend: { responseTimes: [], avgResponseTime: 0, maxResponseTime: 0, minResponseTime: Infinity },
    frontend: { responseTimes: [], avgResponseTime: 0, maxResponseTime: 0, minResponseTime: Infinity }
  },
  startTime: Date.now()
};

// Test data
const TEST_CREDENTIALS = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' }
];

const BACKEND_ENDPOINTS = ['/api/health', '/api/test'];
const FRONTEND_PAGES = ['/', '/login', '/dashboard'];

// Performance tracking
function updateBackendPerformance(responseTime) {
  results.performance.backend.responseTimes.push(responseTime);
  const times = results.performance.backend.responseTimes;
  results.performance.backend.avgResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
  results.performance.backend.maxResponseTime = Math.max(results.performance.backend.maxResponseTime, responseTime);
  results.performance.backend.minResponseTime = Math.min(results.performance.backend.minResponseTime, responseTime);
}

function updateFrontendPerformance(responseTime) {
  results.performance.frontend.responseTimes.push(responseTime);
  const times = results.performance.frontend.responseTimes;
  results.performance.frontend.avgResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
  results.performance.frontend.maxResponseTime = Math.max(results.performance.frontend.maxResponseTime, responseTime);
  results.performance.frontend.minResponseTime = Math.min(results.performance.frontend.minResponseTime, responseTime);
}

// Light backend stress test
async function lightBackendTest() {
  console.log('🔧 Testing Optimized Backend...');
  
  // Health endpoint test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 3); i++) {
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, { 
          timeout: CONFIG.TIMEOUT 
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.backend.health.success++;
          updateBackendPerformance(responseTime);
          
          // Check for cache indicators
          if (response.headers['x-cache'] === 'HIT') {
            results.backend.cache.hits++;
          } else {
            results.backend.cache.misses++;
          }
        } else {
          results.backend.health.failed++;
        }
      } catch (error) {
        results.backend.health.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
  
  // Auth endpoint test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 3); i++) {
    const credentials = TEST_CREDENTIALS[i % TEST_CREDENTIALS.length];
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.post(`${CONFIG.BACKEND_URL}/api/auth/login`, credentials, {
          timeout: CONFIG.TIMEOUT,
          headers: { 'Content-Type': 'application/json' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.backend.auth.success++;
          updateBackendPerformance(responseTime);
        } else {
          results.backend.auth.failed++;
        }
      } catch (error) {
        results.backend.auth.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
  
  // API endpoints test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 3); i++) {
    setInterval(async () => {
      try {
        const endpoint = BACKEND_ENDPOINTS[Math.floor(Math.random() * BACKEND_ENDPOINTS.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}${endpoint}`, { 
          timeout: CONFIG.TIMEOUT 
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.backend.api.success++;
          updateBackendPerformance(responseTime);
        } else {
          results.backend.api.failed++;
        }
      } catch (error) {
        results.backend.api.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Light frontend stress test
async function lightFrontendTest() {
  console.log('📱 Testing Optimized Frontend...');
  
  // Pages test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 2); i++) {
    setInterval(async () => {
      try {
        const page = FRONTEND_PAGES[Math.floor(Math.random() * FRONTEND_PAGES.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.FRONTEND_URL}${page}`, {
          timeout: CONFIG.TIMEOUT,
          headers: { 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.frontend.pages.success++;
          updateFrontendPerformance(responseTime);
        } else {
          results.frontend.pages.failed++;
        }
      } catch (error) {
        results.frontend.pages.failed++;
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
  
  // Static resources test
  for (let i = 0; i < Math.floor(CONFIG.CONCURRENT_USERS / 4); i++) {
    setInterval(async () => {
      try {
        const resources = ['/favicon.ico', '/manifest.json'];
        const resource = resources[Math.floor(Math.random() * resources.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.FRONTEND_URL}${resource}`, { 
          timeout: CONFIG.TIMEOUT 
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 400) {
          results.frontend.static.success++;
          updateFrontendPerformance(responseTime);
        } else {
          results.frontend.static.failed++;
        }
      } catch (error) {
        results.frontend.static.failed++;
      }
    }, 1000 / (CONFIG.REQUESTS_PER_SECOND / 2));
  }
}

// Main light test function
async function runLightOptimizedTest() {
  console.log('🚀 Starting Light Optimized Stress Test...');
  console.log(`⏱️  Duration: ${CONFIG.TEST_DURATION / 1000}s`);
  console.log(`👥 Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`📡 Requests/Second: ${CONFIG.REQUESTS_PER_SECOND}`);
  console.log('');
  
  // Health checks
  console.log('🏥 Checking System Health...');
  try {
    const backendHealth = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, { timeout: 5000 });
    console.log('✅ Backend is healthy and optimized');
    console.log(`📊 Cache: ${backendHealth.data.cache}`);
    console.log(`💾 Memory: ${Math.round(backendHealth.data.memory.heapUsed / 1024 / 1024)}MB`);
  } catch (error) {
    console.log('❌ Backend health check failed:', error.message);
    process.exit(1);
  }
  
  try {
    const frontendHealth = await axios.get(`${CONFIG.FRONTEND_URL}/`, { timeout: 5000 });
    console.log('✅ Frontend is healthy and optimized');
    console.log(`📊 Response size: ${frontendHealth.data.length} bytes`);
  } catch (error) {
    console.log('❌ Frontend health check failed:', error.message);
    process.exit(1);
  }
  console.log('');
  
  // Start tests
  lightBackendTest();
  lightFrontendTest();
  
  // Progress reporting
  const progressInterval = setInterval(() => {
    const elapsed = Date.now() - results.startTime;
    const progress = Math.round((elapsed / CONFIG.TEST_DURATION) * 100);
    
    const backendTotal = results.backend.health.success + results.backend.health.failed +
                        results.backend.auth.success + results.backend.auth.failed +
                        results.backend.api.success + results.backend.api.failed;
    
    const frontendTotal = results.frontend.pages.success + results.frontend.pages.failed +
                         results.frontend.static.success + results.frontend.static.failed;
    
    console.log(`📊 Progress: ${progress}% | Backend: ${backendTotal} | Frontend: ${frontendTotal}`);
  }, 15000);
  
  // Complete test
  setTimeout(() => {
    clearInterval(progressInterval);
    
    const totalTime = Date.now() - results.startTime;
    
    const backendTotal = results.backend.health.success + results.backend.health.failed +
                        results.backend.auth.success + results.backend.auth.failed +
                        results.backend.api.success + results.backend.api.failed;
    
    const frontendTotal = results.frontend.pages.success + results.frontend.pages.failed +
                         results.frontend.static.success + results.frontend.static.failed;
    
    const backendSuccess = results.backend.health.success + results.backend.auth.success + results.backend.api.success;
    const frontendSuccess = results.frontend.pages.success + results.frontend.static.success;
    
    const totalRequests = backendTotal + frontendTotal;
    const totalSuccess = backendSuccess + frontendSuccess;
    
    console.log('\n' + '='.repeat(80));
    console.log('📈 LIGHT OPTIMIZED STRESS TEST RESULTS');
    console.log('='.repeat(80));
    console.log(`⏱️  Duration: ${Math.round(totalTime / 1000)}s`);
    console.log(`📡 Total Requests: ${totalRequests.toLocaleString()}`);
    console.log(`⚡ Requests/Second: ${Math.round(totalRequests / (totalTime / 1000))}`);
    console.log(`✅ Overall Success Rate: ${Math.round((totalSuccess / totalRequests) * 100)}%`);
    console.log('');
    
    console.log('🔧 BACKEND RESULTS (OPTIMIZED):');
    console.log(`   📡 Total Requests: ${backendTotal.toLocaleString()}`);
    console.log(`   ✅ Success Rate: ${Math.round((backendSuccess / backendTotal) * 100)}%`);
    console.log(`   📊 Avg Response: ${Math.round(results.performance.backend.avgResponseTime)}ms`);
    console.log(`   🐌 Min Response: ${Math.round(results.performance.backend.minResponseTime)}ms`);
    console.log(`   🚀 Max Response: ${Math.round(results.performance.backend.maxResponseTime)}ms`);
    console.log(`   💾 Cache Hits: ${results.backend.cache.hits} | Misses: ${results.backend.cache.misses}`);
    console.log(`   🏥 Health: ${results.backend.health.success}✅ / ${results.backend.health.failed}❌`);
    console.log(`   🔐 Auth: ${results.backend.auth.success}✅ / ${results.backend.auth.failed}❌`);
    console.log(`   🔧 API: ${results.backend.api.success}✅ / ${results.backend.api.failed}❌`);
    console.log('');
    
    console.log('📱 FRONTEND RESULTS (OPTIMIZED):');
    console.log(`   📡 Total Requests: ${frontendTotal.toLocaleString()}`);
    console.log(`   ✅ Success Rate: ${Math.round((frontendSuccess / frontendTotal) * 100)}%`);
    console.log(`   📊 Avg Response: ${Math.round(results.performance.frontend.avgResponseTime)}ms`);
    console.log(`   🐌 Min Response: ${Math.round(results.performance.frontend.minResponseTime)}ms`);
    console.log(`   🚀 Max Response: ${Math.round(results.performance.frontend.maxResponseTime)}ms`);
    console.log(`   📱 Pages: ${results.frontend.pages.success}✅ / ${results.frontend.pages.failed}❌`);
    console.log(`   🎨 Static: ${results.frontend.static.success}✅ / ${results.frontend.static.failed}❌`);
    console.log('');
    
    // Comparison with previous results
    console.log('🎯 OPTIMIZATION IMPACT:');
    
    const backendSuccessRate = Math.round((backendSuccess / backendTotal) * 100);
    const frontendSuccessRate = Math.round((frontendSuccess / frontendTotal) * 100);
    
    if (backendSuccessRate >= 90) {
      console.log('   ✅ Backend optimization SUCCESS! Massive improvement from 0.3% to ' + backendSuccessRate + '%');
    } else if (backendSuccessRate >= 70) {
      console.log('   ⚠️  Backend optimization GOOD! Significant improvement from 0.3% to ' + backendSuccessRate + '%');
    } else {
      console.log('   ❌ Backend optimization needs more work. Improved from 0.3% to ' + backendSuccessRate + '%');
    }
    
    if (frontendSuccessRate >= 90) {
      console.log('   ✅ Frontend optimization SUCCESS! Improved from 72% to ' + frontendSuccessRate + '%');
    } else if (frontendSuccessRate >= 80) {
      console.log('   ⚠️  Frontend optimization GOOD! Maintained performance at ' + frontendSuccessRate + '%');
    } else {
      console.log('   ❌ Frontend optimization needs work. Declined from 72% to ' + frontendSuccessRate + '%');
    }
    
    if (results.performance.backend.avgResponseTime < 500) {
      console.log('   ✅ Backend response times EXCELLENT! Under 500ms average');
    } else {
      console.log('   ⚠️  Backend response times need optimization');
    }
    
    if (results.performance.frontend.avgResponseTime < 1000) {
      console.log('   ✅ Frontend response times EXCELLENT! Under 1000ms average');
    } else {
      console.log('   ⚠️  Frontend response times need optimization');
    }
    
    const cacheHitRate = results.backend.cache.hits + results.backend.cache.misses > 0 ? 
      Math.round((results.backend.cache.hits / (results.backend.cache.hits + results.backend.cache.misses)) * 100) : 0;
    
    if (cacheHitRate > 0) {
      console.log(`   ✅ Caching is working! ${cacheHitRate}% hit rate`);
    } else {
      console.log('   ⚠️  Caching not detected in this test');
    }
    
    console.log('\n' + '='.repeat(80));
    
    // Save report
    const report = {
      timestamp: new Date().toISOString(),
      duration: Math.round(totalTime / 1000),
      totalRequests,
      successRate: Math.round((totalSuccess / totalRequests) * 100),
      backend: {
        totalRequests: backendTotal,
        successRate: backendSuccessRate,
        avgResponseTime: Math.round(results.performance.backend.avgResponseTime),
        cache: results.backend.cache,
        results: results.backend
      },
      frontend: {
        totalRequests: frontendTotal,
        successRate: frontendSuccessRate,
        avgResponseTime: Math.round(results.performance.frontend.avgResponseTime),
        results: results.frontend
      }
    };
    
    fs.writeFileSync('light-optimized-test-report.json', JSON.stringify(report, null, 2));
    console.log('📄 Report saved to: light-optimized-test-report.json');
    
    process.exit(0);
  }, CONFIG.TEST_DURATION);
}

// Run the light optimized test
runLightOptimizedTest().catch(console.error);
