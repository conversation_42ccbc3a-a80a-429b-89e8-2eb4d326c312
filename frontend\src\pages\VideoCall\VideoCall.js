import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MicrophoneIcon,
  VideoCameraIcon,
  PhoneXMarkIcon,
  ComputerDesktopIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon,
  UserGroupIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon
} from '@heroicons/react/24/outline';
import {
  MicrophoneIcon as MicrophoneIconSolid,
  VideoCameraIcon as VideoCameraIconSolid,
  ComputerDesktopIcon as ComputerDesktopIconSolid
} from '@heroicons/react/24/solid';
import VideoCallControls from '../../components/VideoCall/VideoCallControls';
import ParticipantGrid from '../../components/VideoCall/ParticipantGrid';
import CallChat from '../../components/VideoCall/CallChat';
import CallSettings from '../../components/VideoCall/CallSettings';
import WaitingRoom from '../../components/VideoCall/WaitingRoom';
import { 
  joinCall, 
  leaveCall, 
  toggleMute, 
  toggleVideo, 
  toggleScreenShare,
  endCall 
} from '../../store/slices/videoCallSlice';

const VideoCall = () => {
  const { callId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const { 
    currentCall, 
    participants, 
    localStream, 
    isConnected, 
    isMuted, 
    isVideoOff, 
    isScreenSharing,
    chatMessages,
    loading 
  } = useSelector((state) => state.videoCall);
  const { user } = useSelector((state) => state.auth);
  const { socket } = useSelector((state) => state.socket);

  const [showChat, setShowChat] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showParticipants, setShowParticipants] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [connectionQuality, setConnectionQuality] = useState('good');

  const localVideoRef = useRef(null);
  const callStartTimeRef = useRef(null);
  const durationIntervalRef = useRef(null);

  useEffect(() => {
    if (callId) {
      dispatch(joinCall(callId));
    }

    return () => {
      if (isConnected) {
        dispatch(leaveCall());
      }
    };
  }, [callId, dispatch]);

  useEffect(() => {
    // Start call duration timer
    if (isConnected && !callStartTimeRef.current) {
      callStartTimeRef.current = Date.now();
      durationIntervalRef.current = setInterval(() => {
        setCallDuration(Math.floor((Date.now() - callStartTimeRef.current) / 1000));
      }, 1000);
    }

    return () => {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    };
  }, [isConnected]);

  useEffect(() => {
    // Set up local video stream
    if (localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleMuteToggle = () => {
    dispatch(toggleMute());
  };

  const handleVideoToggle = () => {
    dispatch(toggleVideo());
  };

  const handleScreenShare = () => {
    dispatch(toggleScreenShare());
  };

  const handleEndCall = () => {
    dispatch(endCall());
    navigate('/dashboard');
  };

  const handleLeaveCall = () => {
    dispatch(leaveCall());
    navigate('/dashboard');
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  if (loading) {
    return <WaitingRoom callId={callId} />;
  }

  if (!currentCall) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Call not found</h2>
          <button
            onClick={() => navigate('/dashboard')}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white relative overflow-hidden">
      {/* Call Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="absolute top-0 left-0 right-0 z-20 p-4 bg-gradient-to-b from-black/50 to-transparent"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-semibold">{currentCall.title}</h1>
            <div className="flex items-center space-x-2 text-sm text-gray-300">
              <div className={`w-2 h-2 rounded-full ${
                connectionQuality === 'good' ? 'bg-green-500' :
                connectionQuality === 'fair' ? 'bg-yellow-500' : 'bg-red-500'
              }`} />
              <span>{formatDuration(callDuration)}</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Participants count */}
            <button
              onClick={() => setShowParticipants(!showParticipants)}
              className="flex items-center space-x-2 px-3 py-1 bg-black/30 rounded-lg hover:bg-black/50 transition-colors"
            >
              <UserGroupIcon className="h-4 w-4" />
              <span className="text-sm">{participants.length}</span>
            </button>

            {/* Settings */}
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 bg-black/30 rounded-lg hover:bg-black/50 transition-colors"
            >
              <Cog6ToothIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </motion.div>

      {/* Main Video Area */}
      <div className="relative h-screen">
        <ParticipantGrid
          participants={participants}
          localStream={localStream}
          isScreenSharing={isScreenSharing}
          currentUserId={user.id}
        />

        {/* Local Video (Picture-in-Picture) */}
        <motion.div
          drag
          dragConstraints={{ left: 0, right: 300, top: 0, bottom: 300 }}
          className="absolute top-20 right-4 w-48 h-36 bg-gray-800 rounded-lg overflow-hidden border-2 border-gray-600 cursor-move z-10"
          whileDrag={{ scale: 1.05 }}
        >
          <video
            ref={localVideoRef}
            autoPlay
            muted
            playsInline
            className="w-full h-full object-cover"
          />
          
          {/* Local video overlay */}
          <div className="absolute bottom-2 left-2 flex items-center space-x-1">
            <span className="text-xs bg-black/50 px-2 py-1 rounded">You</span>
            {isMuted && (
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                <SpeakerXMarkIcon className="h-3 w-3" />
              </div>
            )}
          </div>
        </motion.div>
      </div>

      {/* Call Controls */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="absolute bottom-0 left-0 right-0 z-20 p-6 bg-gradient-to-t from-black/70 to-transparent"
      >
        <div className="flex items-center justify-center space-x-4">
          {/* Mute Button */}
          <button
            onClick={handleMuteToggle}
            className={`p-4 rounded-full transition-all duration-200 ${
              isMuted 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
            title={isMuted ? 'Unmute' : 'Mute'}
          >
            {isMuted ? (
              <SpeakerXMarkIcon className="h-6 w-6" />
            ) : (
              <MicrophoneIcon className="h-6 w-6" />
            )}
          </button>

          {/* Video Button */}
          <button
            onClick={handleVideoToggle}
            className={`p-4 rounded-full transition-all duration-200 ${
              isVideoOff 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
            title={isVideoOff ? 'Turn on camera' : 'Turn off camera'}
          >
            {isVideoOff ? (
              <VideoCameraIcon className="h-6 w-6" />
            ) : (
              <VideoCameraIconSolid className="h-6 w-6" />
            )}
          </button>

          {/* Screen Share Button */}
          <button
            onClick={handleScreenShare}
            className={`p-4 rounded-full transition-all duration-200 ${
              isScreenSharing 
                ? 'bg-blue-600 hover:bg-blue-700' 
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
            title={isScreenSharing ? 'Stop sharing' : 'Share screen'}
          >
            {isScreenSharing ? (
              <ComputerDesktopIconSolid className="h-6 w-6" />
            ) : (
              <ComputerDesktopIcon className="h-6 w-6" />
            )}
          </button>

          {/* Chat Button */}
          <button
            onClick={() => setShowChat(!showChat)}
            className={`p-4 rounded-full transition-all duration-200 ${
              showChat 
                ? 'bg-indigo-600 hover:bg-indigo-700' 
                : 'bg-gray-700 hover:bg-gray-600'
            }`}
            title="Toggle chat"
          >
            <ChatBubbleLeftRightIcon className="h-6 w-6" />
            {chatMessages.length > 0 && (
              <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs flex items-center justify-center">
                {chatMessages.length > 9 ? '9+' : chatMessages.length}
              </span>
            )}
          </button>

          {/* End Call Button */}
          <button
            onClick={user.id === currentCall.hostId ? handleEndCall : handleLeaveCall}
            className="p-4 bg-red-600 hover:bg-red-700 rounded-full transition-all duration-200"
            title={user.id === currentCall.hostId ? 'End call' : 'Leave call'}
          >
            <PhoneXMarkIcon className="h-6 w-6" />
          </button>
        </div>
      </motion.div>

      {/* Side Panels */}
      <AnimatePresence>
        {/* Chat Panel */}
        {showChat && (
          <CallChat
            messages={chatMessages}
            onClose={() => setShowChat(false)}
            callId={callId}
          />
        )}

        {/* Settings Panel */}
        {showSettings && (
          <CallSettings
            onClose={() => setShowSettings(false)}
            currentCall={currentCall}
          />
        )}
      </AnimatePresence>

      {/* Fullscreen Toggle */}
      <button
        onClick={toggleFullscreen}
        className="absolute top-4 right-4 p-2 bg-black/30 rounded-lg hover:bg-black/50 transition-colors z-30"
        title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
      >
        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          {isFullscreen ? (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          ) : (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
          )}
        </svg>
      </button>

      {/* Connection Status */}
      {connectionQuality !== 'good' && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-16 left-1/2 transform -translate-x-1/2 bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm z-30"
        >
          {connectionQuality === 'fair' ? 'Poor connection quality' : 'Connection issues detected'}
        </motion.div>
      )}
    </div>
  );
};

export default VideoCall;