version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: famedu-postgres
    environment:
      POSTGRES_DB: fameduconnect
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - famedu-network

  redis:
    image: redis:7-alpine
    container_name: famedu-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - famedu-network

  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: famedu-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - famedu-network

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:

networks:
  famedu-network:
    driver: bridge 