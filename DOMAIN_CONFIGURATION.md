# 🌐 FamEduConnect - Domain Configuration Guide

## 🎯 Overview
This guide provides detailed instructions for configuring the domain and DNS settings for FamEduConnect's production environment.

## 🔍 Domain Information

### Primary Domain
- **Domain Name:** fameduconnect.xyz
- **Registrar:** Namecheap
- **Expiration Date:** January 21, 2026
- **Auto-Renewal:** Enabled
- **WHOIS Privacy:** Enabled

### Subdomains
| Subdomain | Purpose | Points To |
|-----------|---------|-----------|
| www | Website redirect | Vercel (Landing Page) |
| app | Main application | Vercel (Web App) |
| admin | Admin dashboard | Vercel (Admin Panel) |
| api | Backend API | AWS EC2 (API Server) |
| storage | File storage | AWS S3 (Storage Bucket) |
| media | WebRTC media server | AWS EC2 (Media Server) |
| demo | Investor demo | Vercel (Demo Environment) |
| docs | Documentation | Vercel (Documentation Site) |
| help | Help center | Zendesk |
| status | Status page | StatusPage.io |

## 📝 DNS Configuration

### A Records
```
@                   IN A      76.76.21.21      # Vercel
www                 IN A      76.76.21.21      # Vercel
app                 IN A      76.76.21.21      # Vercel
admin               IN A      76.76.21.21      # Vercel
api                 IN A      13.248.167.21    # AWS EC2
media               IN A      13.248.167.22    # AWS EC2
demo                IN A      76.76.21.21      # Vercel
docs                IN A      76.76.21.21      # Vercel
```

### CNAME Records
```
storage             IN CNAME  fameduconnect-prod.s3.amazonaws.com.
help                IN CNAME  fameduconnect.zendesk.com.
status              IN CNAME  fameduconnect.statuspage.io.
```

### MX Records
```
@                   IN MX     1 aspmx.l.google.com.
@                   IN MX     5 alt1.aspmx.l.google.com.
@                   IN MX     5 alt2.aspmx.l.google.com.
@                   IN MX     10 alt3.aspmx.l.google.com.
@                   IN MX     10 alt4.aspmx.l.google.com.
```

### TXT Records
```
@                   IN TXT    "v=spf1 include:_spf.google.com include:sendgrid.net ~all"
_dmarc              IN TXT    "v=DMARC1; p=reject; rua=mailto:<EMAIL>; ruf=mailto:<EMAIL>; fo=1"
google._domainkey   IN TXT    "v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiDr7..."
sendgrid._domainkey IN TXT    "v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzUd..."
```

### CAA Records
```
@                   IN CAA    0 issue "letsencrypt.org"
@                   IN CAA    0 issue "amazon.com"
@                   IN CAA    0 issuewild "letsencrypt.org"
@                   IN CAA    0 iodef "mailto:<EMAIL>"
```

### SRV Records
```
_stun._udp          IN SRV    0 5 3478 stun.fameduconnect.xyz.
_turn._udp          IN SRV    0 5 3478 turn.fameduconnect.xyz.
_xmpp-client._tcp   IN SRV    0 5 5222 xmpp.fameduconnect.xyz.
```

## 🔒 SSL/TLS Configuration

### Certificate Authority
- **Provider:** Let's Encrypt
- **Type:** Wildcard Certificate
- **Domains Covered:** fameduconnect.xyz, *.fameduconnect.xyz
- **Renewal Method:** Automatic via Vercel and AWS Certificate Manager
- **Renewal Frequency:** Every 60 days

### SSL Settings
- **Minimum TLS Version:** TLS 1.2
- **Preferred Ciphers:** TLS_AES_128_GCM_SHA256, TLS_AES_256_GCM_SHA384, TLS_CHACHA20_POLY1305_SHA256
- **OCSP Stapling:** Enabled
- **HTTP Strict Transport Security (HSTS):** Enabled
  - Max Age: 31536000 seconds (1 year)
  - Include Subdomains: Yes
  - Preload: Yes

## 🚀 Vercel Configuration

### Projects
| Project Name | Git Repository | Environment Variables | Custom Domain |
|--------------|---------------|------------------------|--------------|
| fameduconnect-landing | github.com/fameduconnect/landing | 12 variables | fameduconnect.xyz, www.fameduconnect.xyz |
| fameduconnect-app | github.com/fameduconnect/frontend | 24 variables | app.fameduconnect.xyz |
| fameduconnect-admin | github.com/fameduconnect/admin | 18 variables | admin.fameduconnect.xyz |
| fameduconnect-docs | github.com/fameduconnect/docs | 8 variables | docs.fameduconnect.xyz |
| fameduconnect-demo | github.com/fameduconnect/demo | 14 variables | demo.fameduconnect.xyz |

### Domain Verification
- **Verification Method:** TXT Record
- **Record Name:** _vercel
- **Record Value:** vc-domain-verify=fameduconnect.xyz,a1b2c3d4e5f6g7h8i9j0

### Custom Domains
1. **fameduconnect.xyz**
   - Primary domain for landing page
   - Redirects www to apex domain
   - HTTPS enforced

2. **app.fameduconnect.xyz**
   - Main application
   - HTTPS enforced
   - HSTS enabled

3. **admin.fameduconnect.xyz**
   - Admin dashboard
   - HTTPS enforced
   - HSTS enabled
   - IP restriction enabled

## ☁️ AWS Configuration

### Route 53
- **Hosted Zone:** fameduconnect.xyz
- **NS Records:** Delegated to Namecheap

### Certificate Manager
- **Certificate ARN:** arn:aws:acm:us-east-1:123456789012:certificate/a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6
- **Domains:** *.fameduconnect.xyz, fameduconnect.xyz
- **Validation:** DNS validation
- **Auto-renewal:** Enabled

### CloudFront Distribution
- **Distribution ID:** E1A2B3C4D5E6F7
- **Origin:** S3 bucket (fameduconnect-prod)
- **Alternate Domain Names:** storage.fameduconnect.xyz
- **SSL Certificate:** ACM Certificate
- **Default Root Object:** index.html
- **Price Class:** Use Only North America and Europe
- **HTTP/2:** Enabled
- **Origin Shield:** Enabled

## 🔄 DNS Propagation Verification

### Verification Commands
```bash
# Check A records
dig +short fameduconnect.xyz
dig +short www.fameduconnect.xyz
dig +short app.fameduconnect.xyz
dig +short admin.fameduconnect.xyz
dig +short api.fameduconnect.xyz

# Check MX records
dig +short MX fameduconnect.xyz

# Check TXT records
dig +short TXT fameduconnect.xyz
dig +short TXT _dmarc.fameduconnect.xyz

# Check HTTPS configuration
curl -I https://fameduconnect.xyz
curl -I https://app.fameduconnect.xyz
```

### Expected Results
- A records should return the correct IP addresses
- MX records should show Google mail servers
- TXT records should show SPF and DMARC policies
- HTTPS requests should return 200 OK with proper security headers

## 🔍 DNS Monitoring

### Monitoring Service
- **Provider:** UptimeRobot
- **Check Frequency:** Every 5 minutes
- **Monitored Endpoints:**
  - https://fameduconnect.xyz
  - https://app.fameduconnect.xyz
  - https://admin.fameduconnect.xyz
  - https://api.fameduconnect.xyz
  - https://storage.fameduconnect.xyz

### Alerts Configuration
- **Recipients:** <EMAIL>, <EMAIL>
- **Channels:** Email, Slack, SMS
- **Escalation Policy:** 15 minutes → DevOps, 30 minutes → CTO

## 📅 Maintenance Schedule

### SSL Certificate Renewal
- **Frequency:** Every 60 days
- **Responsible:** Automated (Let's Encrypt)
- **Fallback:** DevOps Team

### Domain Renewal
- **Frequency:** Annual
- **Next Renewal:** January 21, 2026
- **Responsible:** Finance Department
- **Backup Contact:** CTO

### DNS Record Review
- **Frequency:** Quarterly
- **Next Review:** October 21, 2025
- **Responsible:** DevOps Team

## ✅ Domain Configuration Checklist

- [x] Register domain name
- [x] Configure DNS records
- [x] Set up SSL certificates
- [x] Configure Vercel projects
- [x] Set up AWS resources
- [x] Verify DNS propagation
- [x] Configure monitoring
- [x] Document maintenance schedule
- [x] Test all subdomains
- [x] Verify email delivery
- [ ] Connect live domain to production environment
- [ ] Final DNS propagation check
- [ ] SSL certificate verification
- [ ] Security headers verification
- [ ] Performance testing

## 🔐 Security Considerations

- **DNSSEC:** Enabled
- **Registry Lock:** Enabled
- **Domain Privacy:** Enabled
- **MFA on Registrar Account:** Enabled
- **IP Restrictions for Admin Panel:** Enabled
- **Regular Security Scans:** Weekly
- **DNS Monitoring:** Continuous

## 👥 Responsible Parties

### Domain Management
- **Primary:** Finance Department
- **Secondary:** CTO
- **Emergency Contact:** <EMAIL>

### DNS Configuration
- **Primary:** DevOps Team
- **Secondary:** Cloud Infrastructure Engineer
- **Emergency Contact:** <EMAIL>

### SSL Certificates
- **Primary:** Automated Systems
- **Secondary:** DevOps Team
- **Emergency Contact:** <EMAIL>