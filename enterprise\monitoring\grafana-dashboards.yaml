apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: fameduconnect
data:
  fameduconnect-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "FamEduConnect Overview",
        "tags": ["fameduconnect", "overview"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "API Response Time",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
                "legendFormat": "95th percentile"
              }
            ]
          },
          {
            "id": 2,
            "title": "Active Users",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(active_users_total)",
                "legendFormat": "Active Users"
              }
            ]
          },
          {
            "id": 3,
            "title": "Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) / sum(rate(http_requests_total[5m])) * 100",
                "legendFormat": "Error Rate %"
              }
            ]
          }
        ]
      }
    }
  fameduconnect-security.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Security Monitoring",
        "tags": ["fameduconnect", "security"],
        "panels": [
          {
            "id": 1,
            "title": "Failed Login Attempts",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(auth_failed_logins_total[5m]))",
                "legendFormat": "Failed Logins/min"
              }
            ]
          },
          {
            "id": 2,
            "title": "Suspicious Activities",
            "type": "table",
            "targets": [
              {
                "expr": "sum by (ip_address, user_agent) (rate(security_events_total[5m]))",
                "legendFormat": "{{ip_address}} - {{user_agent}}"
              }
            ]
          }
        ]
      }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: fameduconnect
data:
  alertmanager.yml: |
    global:
      resolve_timeout: 5m
      slack_api_url: 'https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK'
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'
      pagerduty_routing_key: 'YOUR_PAGERDUTY_ROUTING_KEY'
    
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'pagerduty-critical'
      routes:
      - match:
          severity: critical
        receiver: 'pagerduty-critical'
        continue: true
      - match:
          severity: warning
        receiver: 'slack-warning'
        continue: true
      - match:
          severity: info
        receiver: 'slack-info'
    
    receivers:
    - name: 'pagerduty-critical'
      pagerduty_configs:
      - routing_key: 'YOUR_PAGERDUTY_ROUTING_KEY'
        description: '{{ .GroupLabels.alertname }}'
        client: 'FamEduConnect AlertManager'
        client_url: '{{ template "pagerduty.default.clientURL" . }}'
        severity: '{{ if eq .GroupLabels.severity "critical" }}critical{{ else }}warning{{ end }}'
        class: '{{ .GroupLabels.alertname }}'
        group: 'FamEduConnect'
        details:
          firing: '{{ template "pagerduty.default.instances" .Alerts.Firing }}'
          status: '{{ template "pagerduty.default.status" . }}'
          description: '{{ template "pagerduty.default.description" . }}'
          summary: '{{ template "pagerduty.default.summary" . }}'
    
    - name: 'slack-warning'
      slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK'
        channel: '#fameduconnect-alerts'
        title: '{{ template "slack.default.title" . }}'
        text: '{{ template "slack.default.text" . }}'
        send_resolved: true
    
    - name: 'slack-info'
      slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK'
        channel: '#fameduconnect-info'
        title: '{{ template "slack.default.title" . }}'
        text: '{{ template "slack.default.text" . }}'
        send_resolved: true
---
apiVersion: monitoring.coreos.com/v1alpha1
kind: PrometheusRule
metadata:
  name: fameduconnect-alerts
  namespace: fameduconnect
spec:
  groups:
  - name: fameduconnect.rules
    rules:
    - alert: HighErrorRate
      expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) * 100 > 5
      for: 2m
      labels:
        severity: critical
      annotations:
        summary: "High error rate detected"
        description: "Error rate is {{ $value }}% for the last 5 minutes"
    
    - alert: HighResponseTime
      expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le)) > 2
      for: 2m
      labels:
        severity: warning
      annotations:
        summary: "High response time detected"
        description: "95th percentile response time is {{ $value }}s"
    
    - alert: DatabaseConnectionIssues
      expr: up{job="postgres"} == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Database connection issues"
        description: "PostgreSQL database is down"
    
    - alert: HighMemoryUsage
      expr: (container_memory_usage_bytes{container!=""} / container_spec_memory_limit_bytes{container!=""}) * 100 > 85
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High memory usage"
        description: "Container {{ $labels.container }} is using {{ $value }}% memory"
    
    - alert: HighCPUUsage
      expr: (rate(container_cpu_usage_seconds_total{container!=""}[5m]) / container_spec_cpu_quota{container!=""}) * 100 > 80
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High CPU usage"
        description: "Container {{ $labels.container }} is using {{ $value }}% CPU"
    
    - alert: FailedLoginAttempts
      expr: sum(rate(auth_failed_logins_total[5m])) > 10
      for: 1m
      labels:
        severity: warning
      annotations:
        summary: "High number of failed login attempts"
        description: "{{ $value }} failed login attempts per minute"
    
    - alert: SecurityBreachAttempt
      expr: sum(rate(security_events_total{type="breach_attempt"}[5m])) > 0
      for: 0m
      labels:
        severity: critical
      annotations:
        summary: "Security breach attempt detected"
        description: "Potential security breach attempt detected"
    
    - alert: BackupFailure
      expr: up{job="backup"} == 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Backup job failure"
        description: "Database backup job has failed"
    
    - alert: CertificateExpiry
      expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 30
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "SSL certificate expiring soon"
        description: "SSL certificate will expire in {{ $value }} seconds" 