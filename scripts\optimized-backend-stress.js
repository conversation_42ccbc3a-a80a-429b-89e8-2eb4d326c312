const axios = require('axios');
const fs = require('fs');

// Optimized backend stress test configuration
const CONFIG = {
  BACKEND_URL: 'http://localhost:3002',
  TEST_DURATION: 120000, // 2 minutes
  CONCURRENT_USERS: 75,
  REQUESTS_PER_SECOND: 150,
  TIMEOUT: 10000,
  RAMP_UP_TIME: 20000, // 20 seconds
  COOL_DOWN_TIME: 20000 // 20 seconds
};

// Enhanced test results tracking
const results = {
  health: { success: 0, failed: 0, errors: [] },
  auth: { success: 0, failed: 0, errors: [] },
  api: { success: 0, failed: 0, errors: [] },
  cache: { hits: 0, misses: 0 },
  performance: { 
    responseTimes: [], 
    avgResponseTime: 0, 
    maxResponseTime: 0, 
    minResponseTime: Infinity,
    p95ResponseTime: 0,
    p99ResponseTime: 0
  },
  system: {
    memoryUsage: [],
    errorRates: []
  },
  startTime: Date.now(),
  phases: {
    rampUp: { start: 0, end: 0 },
    steady: { start: 0, end: 0 },
    coolDown: { start: 0, end: 0 }
  }
};

// Test data
const TEST_CREDENTIALS = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' }
];

const BACKEND_ENDPOINTS = [
  '/api/health',
  '/api/test',
  '/api/auth/login',
  '/api/users/profile',
  '/api/messages',
  '/api/students',
  '/api/classes'
];

// Performance tracking
function updatePerformanceMetrics(responseTime) {
  results.performance.responseTimes.push(responseTime);
  const times = results.performance.responseTimes;
  results.performance.avgResponseTime = times.reduce((a, b) => a + b, 0) / times.length;
  results.performance.maxResponseTime = Math.max(results.performance.maxResponseTime, responseTime);
  results.performance.minResponseTime = Math.min(results.performance.minResponseTime, responseTime);
}

function calculatePercentile(arr, percentile) {
  if (arr.length === 0) return 0;
  const sorted = arr.slice().sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index] || 0;
}

// Enhanced backend stress tests
async function stressTestBackend(intensity = 1.0) {
  const adjustedUsers = Math.floor(CONFIG.CONCURRENT_USERS * intensity);
  const adjustedRPS = Math.floor(CONFIG.REQUESTS_PER_SECOND * intensity);
  
  console.log(`   Starting tests with ${adjustedUsers} users at ${adjustedRPS} RPS`);
  
  // Health endpoint test (1/3 of users)
  for (let i = 0; i < Math.floor(adjustedUsers / 3); i++) {
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, { 
          timeout: CONFIG.TIMEOUT,
          headers: { 'Cache-Control': 'no-cache' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.health.success++;
          updatePerformanceMetrics(responseTime);
          
          // Check for cache indicators
          if (response.headers['x-cache'] === 'HIT') {
            results.cache.hits++;
          } else {
            results.cache.misses++;
          }
        } else {
          results.health.failed++;
        }
      } catch (error) {
        results.health.failed++;
        if (results.health.errors.length < 10) {
          results.health.errors.push(error.message);
        }
      }
    }, 1000 / adjustedRPS);
  }
  
  // Auth endpoint test (1/3 of users)
  for (let i = 0; i < Math.floor(adjustedUsers / 3); i++) {
    const credentials = TEST_CREDENTIALS[i % TEST_CREDENTIALS.length];
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.post(`${CONFIG.BACKEND_URL}/api/auth/login`, credentials, {
          timeout: CONFIG.TIMEOUT,
          headers: { 'Content-Type': 'application/json' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.auth.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.auth.failed++;
        }
      } catch (error) {
        results.auth.failed++;
        if (results.auth.errors.length < 10) {
          results.auth.errors.push(error.message);
        }
      }
    }, 1000 / adjustedRPS);
  }
  
  // API endpoints test (1/3 of users)
  for (let i = 0; i < Math.floor(adjustedUsers / 3); i++) {
    setInterval(async () => {
      try {
        const endpoint = BACKEND_ENDPOINTS[Math.floor(Math.random() * BACKEND_ENDPOINTS.length)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}${endpoint}`, { 
          timeout: CONFIG.TIMEOUT 
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.api.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.api.failed++;
        }
      } catch (error) {
        results.api.failed++;
        if (results.api.errors.length < 10) {
          results.api.errors.push(error.message);
        }
      }
    }, 1000 / adjustedRPS);
  }
}

// System monitoring
async function monitorSystem() {
  setInterval(async () => {
    try {
      const healthResponse = await axios.get(`${CONFIG.BACKEND_URL}/api/health`);
      if (healthResponse.data.memory) {
        results.system.memoryUsage.push({
          timestamp: Date.now(),
          ...healthResponse.data.memory
        });
      }
      
      // Calculate error rate
      const totalRequests = results.health.success + results.health.failed +
                           results.auth.success + results.auth.failed +
                           results.api.success + results.api.failed;
      
      const totalErrors = results.health.failed + results.auth.failed + results.api.failed;
      const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;
      
      results.system.errorRates.push({
        timestamp: Date.now(),
        errorRate
      });
      
    } catch (error) {
      console.error('System monitoring error:', error.message);
    }
  }, 5000); // Every 5 seconds
}

// Generate optimized report
function generateOptimizedReport() {
  const totalTime = Date.now() - results.startTime;
  
  // Calculate percentiles
  results.performance.p95ResponseTime = calculatePercentile(results.performance.responseTimes, 95);
  results.performance.p99ResponseTime = calculatePercentile(results.performance.responseTimes, 99);
  
  const totalRequests = results.health.success + results.health.failed +
                       results.auth.success + results.auth.failed +
                       results.api.success + results.api.failed;
  
  const totalSuccess = results.health.success + results.auth.success + results.api.success;
  
  return {
    timestamp: new Date().toISOString(),
    testConfig: CONFIG,
    duration: Math.round(totalTime / 1000),
    totalRequests,
    requestsPerSecond: Math.round(totalRequests / (totalTime / 1000)),
    successRate: Math.round((totalSuccess / totalRequests) * 100),
    performance: {
      avgResponseTime: Math.round(results.performance.avgResponseTime),
      minResponseTime: Math.round(results.performance.minResponseTime),
      maxResponseTime: Math.round(results.performance.maxResponseTime),
      p95ResponseTime: Math.round(results.performance.p95ResponseTime),
      p99ResponseTime: Math.round(results.performance.p99ResponseTime)
    },
    cache: results.cache,
    results: results,
    system: results.system,
    phases: results.phases
  };
}

// Main optimized stress test
async function runOptimizedBackendStressTest() {
  console.log('🚀 Starting Optimized Backend Stress Test...');
  console.log(`⏱️  Total Duration: ${CONFIG.TEST_DURATION / 1000}s`);
  console.log(`📈 Ramp-up: ${CONFIG.RAMP_UP_TIME / 1000}s`);
  console.log(`⚡ Steady State: ${(CONFIG.TEST_DURATION - CONFIG.RAMP_UP_TIME - CONFIG.COOL_DOWN_TIME) / 1000}s`);
  console.log(`📉 Cool-down: ${CONFIG.COOL_DOWN_TIME / 1000}s`);
  console.log(`👥 Max Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`📡 Max Requests/Second: ${CONFIG.REQUESTS_PER_SECOND}`);
  console.log('');
  
  // Health check
  console.log('🏥 Checking Backend Health...');
  try {
    const healthResponse = await axios.get(`${CONFIG.BACKEND_URL}/api/health`, { timeout: 5000 });
    console.log('✅ Backend is healthy and optimized');
    console.log(`📊 Cache Status: ${healthResponse.data.cache}`);
    console.log(`💾 Memory Usage: ${Math.round(healthResponse.data.memory.heapUsed / 1024 / 1024)}MB`);
    console.log(`⚡ Uptime: ${Math.round(healthResponse.data.uptime)}s`);
  } catch (error) {
    console.log('❌ Backend health check failed:', error.message);
    process.exit(1);
  }
  console.log('');
  
  // Start system monitoring
  monitorSystem();
  
  // Phase 1: Ramp-up
  console.log('📈 Phase 1: Ramp-up (Gradual Load Increase)');
  results.phases.rampUp.start = Date.now();
  
  const rampUpSteps = 5;
  const rampUpInterval = CONFIG.RAMP_UP_TIME / rampUpSteps;
  
  for (let step = 1; step <= rampUpSteps; step++) {
    const intensity = step / rampUpSteps;
    console.log(`   Step ${step}/${rampUpSteps}: ${Math.round(intensity * 100)}% intensity`);
    
    stressTestBackend(intensity);
    await new Promise(resolve => setTimeout(resolve, rampUpInterval));
  }
  
  results.phases.rampUp.end = Date.now();
  
  // Phase 2: Steady state
  console.log('⚡ Phase 2: Steady State (Full Load)');
  results.phases.steady.start = Date.now();
  
  stressTestBackend(1.0);
  
  // Progress reporting
  const steadyDuration = CONFIG.TEST_DURATION - CONFIG.RAMP_UP_TIME - CONFIG.COOL_DOWN_TIME;
  const progressInterval = setInterval(() => {
    const elapsed = Date.now() - results.phases.steady.start;
    const progress = Math.round((elapsed / steadyDuration) * 100);
    
    const totalRequests = results.health.success + results.health.failed +
                         results.auth.success + results.auth.failed +
                         results.api.success + results.api.failed;
    
    const currentRPS = Math.round(totalRequests / ((Date.now() - results.startTime) / 1000));
    const successRate = Math.round(((results.health.success + results.auth.success + results.api.success) / totalRequests) * 100);
    
    console.log(`📊 Progress: ${progress}% | Requests: ${totalRequests.toLocaleString()} | RPS: ${currentRPS} | Success: ${successRate}%`);
  }, 10000);
  
  await new Promise(resolve => setTimeout(resolve, steadyDuration));
  clearInterval(progressInterval);
  
  results.phases.steady.end = Date.now();
  
  // Phase 3: Cool-down
  console.log('📉 Phase 3: Cool-down');
  results.phases.coolDown.start = Date.now();
  await new Promise(resolve => setTimeout(resolve, CONFIG.COOL_DOWN_TIME));
  results.phases.coolDown.end = Date.now();
  
  // Generate and display results
  console.log('\n📊 Generating Optimized Backend Stress Test Report...');
  const report = generateOptimizedReport();
  
  console.log('\n' + '='.repeat(100));
  console.log('📈 OPTIMIZED BACKEND STRESS TEST RESULTS');
  console.log('='.repeat(100));
  console.log(`⏱️  Duration: ${report.duration}s`);
  console.log(`📡 Total Requests: ${report.totalRequests.toLocaleString()}`);
  console.log(`⚡ Average RPS: ${report.requestsPerSecond}`);
  console.log(`✅ Success Rate: ${report.successRate}%`);
  console.log('');
  
  console.log('🔧 PERFORMANCE METRICS:');
  console.log(`   📊 Avg Response Time: ${report.performance.avgResponseTime}ms`);
  console.log(`   🐌 Min Response Time: ${report.performance.minResponseTime}ms`);
  console.log(`   🚀 Max Response Time: ${report.performance.maxResponseTime}ms`);
  console.log(`   📈 95th Percentile: ${report.performance.p95ResponseTime}ms`);
  console.log(`   📈 99th Percentile: ${report.performance.p99ResponseTime}ms`);
  console.log('');
  
  console.log('💾 CACHING PERFORMANCE:');
  console.log(`   🎯 Cache Hits: ${report.cache.hits}`);
  console.log(`   ❌ Cache Misses: ${report.cache.misses}`);
  const cacheHitRate = report.cache.hits + report.cache.misses > 0 ? 
    Math.round((report.cache.hits / (report.cache.hits + report.cache.misses)) * 100) : 0;
  console.log(`   📊 Cache Hit Rate: ${cacheHitRate}%`);
  console.log('');
  
  console.log('📊 ENDPOINT BREAKDOWN:');
  console.log(`   🏥 Health: ${results.health.success.toLocaleString()}✅ / ${results.health.failed.toLocaleString()}❌ (${Math.round((results.health.success / (results.health.success + results.health.failed)) * 100)}%)`);
  console.log(`   🔐 Auth: ${results.auth.success.toLocaleString()}✅ / ${results.auth.failed.toLocaleString()}❌ (${Math.round((results.auth.success / (results.auth.success + results.auth.failed)) * 100)}%)`);
  console.log(`   🔧 API: ${results.api.success.toLocaleString()}✅ / ${results.api.failed.toLocaleString()}❌ (${Math.round((results.api.success / (results.api.success + results.api.failed)) * 100)}%)`);
  console.log('');
  
  // Performance analysis
  console.log('🎯 OPTIMIZATION ANALYSIS:');
  
  const improvements = [];
  const issues = [];
  
  if (report.successRate >= 95) {
    improvements.push('✅ Excellent stability - 95%+ success rate achieved');
  } else if (report.successRate >= 85) {
    improvements.push('⚠️  Good stability but room for improvement');
  } else {
    issues.push('❌ Poor stability - needs immediate attention');
  }
  
  if (report.performance.avgResponseTime < 300) {
    improvements.push('✅ Excellent response times - under 300ms average');
  } else if (report.performance.avgResponseTime < 500) {
    improvements.push('⚠️  Good response times but could be optimized');
  } else {
    issues.push('❌ Slow response times - optimization needed');
  }
  
  if (cacheHitRate > 50) {
    improvements.push('✅ Caching is working effectively');
  } else if (cacheHitRate > 0) {
    improvements.push('⚠️  Caching is active but could be improved');
  } else {
    issues.push('❌ Caching not working - check implementation');
  }
  
  if (report.requestsPerSecond >= CONFIG.REQUESTS_PER_SECOND * 0.8) {
    improvements.push('✅ High throughput achieved');
  } else {
    issues.push('❌ Lower than expected throughput');
  }
  
  improvements.forEach(improvement => console.log(`   ${improvement}`));
  issues.forEach(issue => console.log(`   ${issue}`));
  
  if (issues.length === 0) {
    console.log('   🎉 OUTSTANDING! All optimizations are working perfectly!');
  }
  
  console.log('\n' + '='.repeat(100));
  
  // Save reports
  fs.writeFileSync('optimized-backend-stress-report.json', JSON.stringify(report, null, 2));
  console.log('📄 Detailed report saved to: optimized-backend-stress-report.json');
  
  process.exit(0);
}

// Run the optimized stress test
runOptimizedBackendStressTest().catch(console.error);
