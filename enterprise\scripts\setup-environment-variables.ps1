# FamEduConnect Enterprise Environment Variables Setup Script
# This script helps configure all environment variables and secrets for enterprise deployment

param(
    [switch]$Force,
    [string]$Environment = "production"
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "================================`n$Message`n================================" -ForegroundColor Blue
}

# Function to prompt for input with default value
function Read-InputWithDefault {
    param(
        [string]$Prompt,
        [string]$Default = ""
    )
    
    if ($Default) {
        $input = Read-Host "$Prompt [$Default]"
        if (-not $input) {
            $input = $Default
        }
    } else {
        $input = Read-Host $Prompt
    }
    
    return $input
}

# Function to generate secure password
function New-SecurePassword {
    $bytes = New-Object Byte[] 32
    (New-Object Security.Cryptography.RNGCryptoServiceProvider).GetBytes($bytes)
    return [Convert]::ToBase64String($bytes) -replace '[=+/]', '' | Select-Object -First 25
}

# Function to base64 encode
function Convert-ToBase64 {
    param([string]$String)
    $bytes = [System.Text.Encoding]::UTF8.GetBytes($String)
    return [Convert]::ToBase64String($bytes)
}

# Function to create Kubernetes secret
function New-KubernetesSecret {
    param(
        [string]$SecretName,
        [hashtable]$Data,
        [string]$Namespace = "fameduconnect"
    )
    
    $secretYaml = @"
apiVersion: v1
kind: Secret
metadata:
  name: $SecretName
  namespace: $Namespace
type: Opaque
data:
"@
    
    foreach ($key in $Data.Keys) {
        $encodedValue = Convert-ToBase64 $Data[$key]
        $secretYaml += "`n  $key`: $encodedValue"
    }
    
    return $secretYaml
}

# Function to create ConfigMap
function New-ConfigMap {
    param(
        [string]$ConfigMapName,
        [hashtable]$Data,
        [string]$Namespace = "fameduconnect"
    )
    
    $configMapYaml = @"
apiVersion: v1
kind: ConfigMap
metadata:
  name: $ConfigMapName
  namespace: $Namespace
data:
"@
    
    foreach ($key in $Data.Keys) {
        $configMapYaml += "`n  $key`: '$($Data[$key])'"
    }
    
    return $configMapYaml
}

# Main script
function Main {
    Write-Header "FamEduConnect Enterprise Environment Variables Setup"
    
    # Check if running from the correct directory
    if (-not (Test-Path "enterprise\ENTERPRISE_DEPLOYMENT_GUIDE.md")) {
        Write-Error "Please run this script from the FamEduConnect_Full_Codebase directory"
        exit 1
    }
    
    Write-Status "Setting up environment variables for: $Environment"
    
    # Create environment directory
    $envDir = "enterprise\environment-variables\$Environment"
    if (Test-Path $envDir) {
        if (-not $Force) {
            $response = Read-Host "Environment directory already exists. Overwrite? (y/N)"
            if ($response -notmatch '^[Yy]$') {
                Write-Status "Operation cancelled."
                exit 0
            }
        }
        Remove-Item $envDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $envDir -Force | Out-Null
    
    # Step 1: Database Configuration
    Write-Header "Step 1: Database Configuration"
    
    $DB_HOST = Read-InputWithDefault "Enter database host" "postgres-primary.fameduconnect.svc.cluster.local"
    $DB_PORT = Read-InputWithDefault "Enter database port" "5432"
    $DB_NAME = Read-InputWithDefault "Enter database name" "fameduconnect_prod"
    $DB_USER = Read-InputWithDefault "Enter database user" "fameduconnect"
    
    # Generate secure passwords if not provided
    $DB_PASSWORD = Read-InputWithDefault "Enter database password (leave empty to generate)" ""
    if (-not $DB_PASSWORD) {
        $DB_PASSWORD = New-SecurePassword
        Write-Status "Generated secure database password"
    }
    
    $DB_REPLICATION_PASSWORD = Read-InputWithDefault "Enter replication password (leave empty to generate)" ""
    if (-not $DB_REPLICATION_PASSWORD) {
        $DB_REPLICATION_PASSWORD = New-SecurePassword
        Write-Status "Generated secure replication password"
    }
    
    $DB_BACKUP_PASSWORD = Read-InputWithDefault "Enter backup password (leave empty to generate)" ""
    if (-not $DB_BACKUP_PASSWORD) {
        $DB_BACKUP_PASSWORD = New-SecurePassword
        Write-Status "Generated secure backup password"
    }
    
    # Step 2: Redis Configuration
    Write-Header "Step 2: Redis Configuration"
    
    $REDIS_HOST = Read-InputWithDefault "Enter Redis host" "redis-cluster.fameduconnect.svc.cluster.local"
    $REDIS_PORT = Read-InputWithDefault "Enter Redis port" "6379"
    $REDIS_PASSWORD = Read-InputWithDefault "Enter Redis password (leave empty to generate)" ""
    if (-not $REDIS_PASSWORD) {
        $REDIS_PASSWORD = New-SecurePassword
        Write-Status "Generated secure Redis password"
    }
    
    # Step 3: JWT Configuration
    Write-Header "Step 3: JWT Configuration"
    
    $JWT_SECRET = Read-InputWithDefault "Enter JWT secret (leave empty to generate)" ""
    if (-not $JWT_SECRET) {
        $JWT_SECRET = New-SecurePassword
        Write-Status "Generated secure JWT secret"
    }
    
    $JWT_EXPIRES_IN = Read-InputWithDefault "Enter JWT expiration time" "24h"
    
    # Step 4: Email Configuration
    Write-Header "Step 4: Email Configuration"
    
    $SMTP_HOST = Read-InputWithDefault "Enter SMTP host" "smtp.gmail.com"
    $SMTP_PORT = Read-InputWithDefault "Enter SMTP port" "587"
    $SMTP_USER = Read-InputWithDefault "Enter SMTP username" ""
    $SMTP_PASSWORD = Read-InputWithDefault "Enter SMTP password" ""
    $EMAIL_FROM = Read-InputWithDefault "Enter from email address" "<EMAIL>"
    
    # Step 5: Storage Configuration
    Write-Header "Step 5: Storage Configuration"
    
    $S3_BUCKET = Read-InputWithDefault "Enter S3 bucket name" "fameduconnect-storage"
    $S3_REGION = Read-InputWithDefault "Enter S3 region" "us-east-1"
    $S3_ACCESS_KEY = Read-InputWithDefault "Enter S3 access key" ""
    $S3_SECRET_KEY = Read-InputWithDefault "Enter S3 secret key" ""
    
    # Step 6: Monitoring Configuration
    Write-Header "Step 6: Monitoring Configuration"
    
    $ELASTICSEARCH_HOST = Read-InputWithDefault "Enter Elasticsearch host" "elasticsearch-master.fameduconnect.svc.cluster.local"
    $ELASTICSEARCH_PORT = Read-InputWithDefault "Enter Elasticsearch port" "9200"
    $ELASTICSEARCH_USERNAME = Read-InputWithDefault "Enter Elasticsearch username" "elastic"
    $ELASTICSEARCH_PASSWORD = Read-InputWithDefault "Enter Elasticsearch password (leave empty to generate)" ""
    if (-not $ELASTICSEARCH_PASSWORD) {
        $ELASTICSEARCH_PASSWORD = New-SecurePassword
        Write-Status "Generated secure Elasticsearch password"
    }
    
    $GRAFANA_URL = Read-InputWithDefault "Enter Grafana URL" "https://grafana.fameduconnect.com"
    $PROMETHEUS_URL = Read-InputWithDefault "Enter Prometheus URL" "https://prometheus.fameduconnect.com"
    
    # Step 7: External Services
    Write-Header "Step 7: External Services"
    
    $SENTRY_DSN = Read-InputWithDefault "Enter Sentry DSN (leave empty if not using)" ""
    $TWILIO_ACCOUNT_SID = Read-InputWithDefault "Enter Twilio Account SID (leave empty if not using)" ""
    $TWILIO_AUTH_TOKEN = Read-InputWithDefault "Enter Twilio Auth Token (leave empty if not using)" ""
    $GOOGLE_TRANSLATE_API_KEY = Read-InputWithDefault "Enter Google Translate API Key (leave empty if not using)" ""
    
    # Step 8: SSO Configuration (Optional)
    Write-Header "Step 8: SSO Configuration (Optional)"
    
    $configure_sso = Read-Host "Do you want to configure SSO (SAML/OIDC)? (y/N)"
    if ($configure_sso -match '^[Yy]$') {
        $SSO_PROVIDER = Read-InputWithDefault "Enter SSO provider (saml/oidc)" "saml"
        $SSO_ENTITY_ID = Read-InputWithDefault "Enter SSO Entity ID" "https://api.fameduconnect.com/saml/metadata"
        $SSO_ACS_URL = Read-InputWithDefault "Enter SSO ACS URL" "https://api.fameduconnect.com/saml/acs"
        $SSO_IDP_SSO_URL = Read-InputWithDefault "Enter SSO IdP SSO URL" ""
        $SSO_IDP_CERT = Read-InputWithDefault "Enter SSO IdP Certificate (base64)" ""
        $SSO_CLIENT_ID = Read-InputWithDefault "Enter SSO Client ID (for OIDC)" ""
        $SSO_CLIENT_SECRET = Read-InputWithDefault "Enter SSO Client Secret (for OIDC)" ""
    } else {
        $SSO_PROVIDER = ""
        $SSO_ENTITY_ID = ""
        $SSO_ACS_URL = ""
        $SSO_IDP_SSO_URL = ""
        $SSO_IDP_CERT = ""
        $SSO_CLIENT_ID = ""
        $SSO_CLIENT_SECRET = ""
    }
    
    # Step 9: Application Configuration
    Write-Header "Step 9: Application Configuration"
    
    $APP_URL = Read-InputWithDefault "Enter application URL" "https://app.fameduconnect.com"
    $API_URL = Read-InputWithDefault "Enter API URL" "https://api.fameduconnect.com"
    $ADMIN_URL = Read-InputWithDefault "Enter admin URL" "https://admin.fameduconnect.com"
    
    $NODE_ENV = $Environment
    $PORT = Read-InputWithDefault "Enter application port" "5555"
    $CORS_ORIGIN = Read-InputWithDefault "Enter CORS origin" "https://app.fameduconnect.com"
    
    # Step 10: Generate Configuration Files
    Write-Header "Step 10: Generating Configuration Files"
    
    # Create backend environment file
    $backendEnv = @"
# FamEduConnect Backend Environment Variables - $Environment
# Generated on: $(Get-Date)

# Server Configuration
NODE_ENV=$NODE_ENV
PORT=$PORT
HOST=0.0.0.0
TRUST_PROXY=true
API_VERSION=v1
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
MAX_FILE_SIZE=50mb
COMPRESSION_LEVEL=6

# Database Configuration
DATABASE_URL=postgresql://$DB_USER`:$DB_PASSWORD@$DB_HOST`:$DB_PORT/$DB_NAME
DATABASE_SSL=true
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20
DATABASE_IDLE_TIMEOUT=30000
DATABASE_CONNECTION_TIMEOUT=2000

# Redis Configuration
REDIS_URL=redis://:$REDIS_PASSWORD@$REDIS_HOST`:$REDIS_PORT
REDIS_CLUSTER_MODE=true
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000

# JWT Configuration
JWT_SECRET=$JWT_SECRET
JWT_EXPIRES_IN=$JWT_EXPIRES_IN
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=$SMTP_HOST
SMTP_PORT=$SMTP_PORT
SMTP_USER=$SMTP_USER
SMTP_PASSWORD=$SMTP_PASSWORD
EMAIL_FROM=$EMAIL_FROM
EMAIL_TEMPLATES_PATH=./email-templates

# Storage Configuration
S3_BUCKET=$S3_BUCKET
S3_REGION=$S3_REGION
S3_ACCESS_KEY=$S3_ACCESS_KEY
S3_SECRET_KEY=$S3_SECRET_KEY
S3_ENDPOINT=https://s3.$S3_REGION.amazonaws.com

# Monitoring Configuration
ELASTICSEARCH_HOST=$ELASTICSEARCH_HOST
ELASTICSEARCH_PORT=$ELASTICSEARCH_PORT
ELASTICSEARCH_USERNAME=$ELASTICSEARCH_USERNAME
ELASTICSEARCH_PASSWORD=$ELASTICSEARCH_PASSWORD
GRAFANA_URL=$GRAFANA_URL
PROMETHEUS_URL=$PROMETHEUS_URL

# External Services
SENTRY_DSN=$SENTRY_DSN
TWILIO_ACCOUNT_SID=$TWILIO_ACCOUNT_SID
TWILIO_AUTH_TOKEN=$TWILIO_AUTH_TOKEN
GOOGLE_TRANSLATE_API_KEY=$GOOGLE_TRANSLATE_API_KEY

# SSO Configuration
SSO_PROVIDER=$SSO_PROVIDER
SSO_ENTITY_ID=$SSO_ENTITY_ID
SSO_ACS_URL=$SSO_ACS_URL
SSO_IDP_SSO_URL=$SSO_IDP_SSO_URL
SSO_IDP_CERT=$SSO_IDP_CERT
SSO_CLIENT_ID=$SSO_CLIENT_ID
SSO_CLIENT_SECRET=$SSO_CLIENT_SECRET

# Application URLs
APP_URL=$APP_URL
API_URL=$API_URL
ADMIN_URL=$ADMIN_URL
CORS_ORIGIN=$CORS_ORIGIN

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=$JWT_SECRET
COOKIE_SECRET=$JWT_SECRET
CSRF_SECRET=$JWT_SECRET

# Feature Flags
ENABLE_VIDEO_CALLS=true
ENABLE_TRANSLATION=true
ENABLE_AI_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=100mb
LOG_MAX_FILES=10

# Performance Configuration
CACHE_TTL=3600
RATE_LIMIT_ENABLED=true
COMPRESSION_ENABLED=true
HELMET_ENABLED=true
"@
    
    Set-Content "$envDir\backend.env" $backendEnv
    Write-Status "Created backend environment file: $envDir\backend.env"
    
    # Create frontend environment file
    $frontendEnv = @"
# FamEduConnect Frontend Environment Variables - $Environment
# Generated on: $(Get-Date)

# API Configuration
REACT_APP_API_URL=$API_URL
REACT_APP_SOCKET_URL=wss://api.fameduconnect.com
REACT_APP_APP_URL=$APP_URL
REACT_APP_ADMIN_URL=$ADMIN_URL

# External Services
REACT_APP_SENTRY_DSN=$SENTRY_DSN
REACT_APP_GOOGLE_ANALYTICS_ID=
REACT_APP_MIXPANEL_TOKEN=

# Feature Flags
REACT_APP_ENABLE_VIDEO_CALLS=true
REACT_APP_ENABLE_TRANSLATION=true
REACT_APP_ENABLE_AI_FEATURES=true
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_NOTIFICATIONS=true

# UI Configuration
REACT_APP_THEME=default
REACT_APP_LANGUAGE=en
REACT_APP_TIMEZONE=UTC
REACT_APP_DATE_FORMAT=MM/DD/YYYY
REACT_APP_TIME_FORMAT=HH:mm

# Security Configuration
REACT_APP_ENABLE_HTTPS=true
REACT_APP_ENABLE_CSP=true
REACT_APP_ENABLE_HSTS=true

# Performance Configuration
REACT_APP_ENABLE_CACHE=true
REACT_APP_CACHE_TTL=3600
REACT_APP_ENABLE_COMPRESSION=true
"@
    
    Set-Content "$envDir\frontend.env" $frontendEnv
    Write-Status "Created frontend environment file: $envDir\frontend.env"
    
    # Create mobile environment file
    $mobileEnv = @"
# FamEduConnect Mobile Environment Variables - $Environment
# Generated on: $(Get-Date)

# API Configuration
EXPO_PUBLIC_API_URL=$API_URL
EXPO_PUBLIC_SOCKET_URL=wss://api.fameduconnect.com
EXPO_PUBLIC_APP_URL=$APP_URL

# External Services
EXPO_PUBLIC_SENTRY_DSN=$SENTRY_DSN
EXPO_PUBLIC_GOOGLE_ANALYTICS_ID=

# Feature Flags
EXPO_PUBLIC_ENABLE_VIDEO_CALLS=true
EXPO_PUBLIC_ENABLE_TRANSLATION=true
EXPO_PUBLIC_ENABLE_AI_FEATURES=true
EXPO_PUBLIC_ENABLE_ANALYTICS=true
EXPO_PUBLIC_ENABLE_NOTIFICATIONS=true

# UI Configuration
EXPO_PUBLIC_THEME=default
EXPO_PUBLIC_LANGUAGE=en
EXPO_PUBLIC_TIMEZONE=UTC
"@
    
    Set-Content "$envDir\mobile.env" $mobileEnv
    Write-Status "Created mobile environment file: $envDir\mobile.env"
    
    # Create Kubernetes secrets
    Write-Status "Creating Kubernetes secrets..."
    
    # Database secrets
    $dbSecrets = @{
        "POSTGRES_PASSWORD" = $DB_PASSWORD
        "POSTGRES_REPLICATION_PASSWORD" = $DB_REPLICATION_PASSWORD
        "POSTGRES_BACKUP_PASSWORD" = $DB_BACKUP_PASSWORD
    }
    
    $dbSecretYaml = New-KubernetesSecret "postgres-secret" $dbSecrets
    Set-Content "$envDir\k8s-secrets\postgres-secret.yaml" $dbSecretYaml
    
    # Redis secrets
    $redisSecrets = @{
        "REDIS_PASSWORD" = $REDIS_PASSWORD
    }
    
    $redisSecretYaml = New-KubernetesSecret "redis-secret" $redisSecrets
    Set-Content "$envDir\k8s-secrets\redis-secret.yaml" $redisSecretYaml
    
    # Application secrets
    $appSecrets = @{
        "JWT_SECRET" = $JWT_SECRET
        "SESSION_SECRET" = $JWT_SECRET
        "COOKIE_SECRET" = $JWT_SECRET
        "CSRF_SECRET" = $JWT_SECRET
    }
    
    $appSecretYaml = New-KubernetesSecret "app-secret" $appSecrets
    Set-Content "$envDir\k8s-secrets\app-secret.yaml" $appSecretYaml
    
    # Email secrets
    if ($SMTP_USER -and $SMTP_PASSWORD) {
        $emailSecrets = @{
            "SMTP_USER" = $SMTP_USER
            "SMTP_PASSWORD" = $SMTP_PASSWORD
        }
        
        $emailSecretYaml = New-KubernetesSecret "email-secret" $emailSecrets
        Set-Content "$envDir\k8s-secrets\email-secret.yaml" $emailSecretYaml
    }
    
    # Storage secrets
    if ($S3_ACCESS_KEY -and $S3_SECRET_KEY) {
        $storageSecrets = @{
            "S3_ACCESS_KEY" = $S3_ACCESS_KEY
            "S3_SECRET_KEY" = $S3_SECRET_KEY
        }
        
        $storageSecretYaml = New-KubernetesSecret "storage-secret" $storageSecrets
        Set-Content "$envDir\k8s-secrets\storage-secret.yaml" $storageSecretYaml
    }
    
    # Monitoring secrets
    $monitoringSecrets = @{
        "ELASTICSEARCH_USERNAME" = $ELASTICSEARCH_USERNAME
        "ELASTICSEARCH_PASSWORD" = $ELASTICSEARCH_PASSWORD
    }
    
    $monitoringSecretYaml = New-KubernetesSecret "monitoring-secret" $monitoringSecrets
    Set-Content "$envDir\k8s-secrets\monitoring-secret.yaml" $monitoringSecretYaml
    
    # SSO secrets (if configured)
    if ($SSO_PROVIDER) {
        $ssoSecrets = @{
            "SSO_ENTITY_ID" = $SSO_ENTITY_ID
            "SSO_ACS_URL" = $SSO_ACS_URL
            "SSO_IDP_SSO_URL" = $SSO_IDP_SSO_URL
            "SSO_IDP_CERT" = $SSO_IDP_CERT
        }
        
        if ($SSO_CLIENT_ID -and $SSO_CLIENT_SECRET) {
            $ssoSecrets["SSO_CLIENT_ID"] = $SSO_CLIENT_ID
            $ssoSecrets["SSO_CLIENT_SECRET"] = $SSO_CLIENT_SECRET
        }
        
        $ssoSecretYaml = New-KubernetesSecret "sso-secret" $ssoSecrets
        Set-Content "$envDir\k8s-secrets\sso-secret.yaml" $ssoSecretYaml
    }
    
    # External services secrets
    $externalSecrets = @{}
    if ($SENTRY_DSN) { $externalSecrets["SENTRY_DSN"] = $SENTRY_DSN }
    if ($TWILIO_ACCOUNT_SID) { $externalSecrets["TWILIO_ACCOUNT_SID"] = $TWILIO_ACCOUNT_SID }
    if ($TWILIO_AUTH_TOKEN) { $externalSecrets["TWILIO_AUTH_TOKEN"] = $TWILIO_AUTH_TOKEN }
    if ($GOOGLE_TRANSLATE_API_KEY) { $externalSecrets["GOOGLE_TRANSLATE_API_KEY"] = $GOOGLE_TRANSLATE_API_KEY }
    
    if ($externalSecrets.Count -gt 0) {
        $externalSecretYaml = New-KubernetesSecret "external-services-secret" $externalSecrets
        Set-Content "$envDir\k8s-secrets\external-services-secret.yaml" $externalSecretYaml
    }
    
    # Create ConfigMaps
    Write-Status "Creating Kubernetes ConfigMaps..."
    
    # Application ConfigMap
    $appConfig = @{
        "NODE_ENV" = $NODE_ENV
        "PORT" = $PORT
        "API_VERSION" = "v1"
        "APP_URL" = $APP_URL
        "API_URL" = $API_URL
        "ADMIN_URL" = $ADMIN_URL
        "CORS_ORIGIN" = $CORS_ORIGIN
        "ENABLE_VIDEO_CALLS" = "true"
        "ENABLE_TRANSLATION" = "true"
        "ENABLE_AI_FEATURES" = "true"
        "ENABLE_ANALYTICS" = "true"
        "ENABLE_NOTIFICATIONS" = "true"
    }
    
    $appConfigMapYaml = New-ConfigMap "app-config" $appConfig
    Set-Content "$envDir\k8s-configmaps\app-config.yaml" $appConfigMapYaml
    
    # Database ConfigMap
    $dbConfig = @{
        "DB_HOST" = $DB_HOST
        "DB_PORT" = $DB_PORT
        "DB_NAME" = $DB_NAME
        "DB_USER" = $DB_USER
        "DATABASE_URL" = "postgresql://$DB_USER`:$DB_PASSWORD@$DB_HOST`:$DB_PORT/$DB_NAME"
    }
    
    $dbConfigMapYaml = New-ConfigMap "database-config" $dbConfig
    Set-Content "$envDir\k8s-configmaps\database-config.yaml" $dbConfigMapYaml
    
    # Create deployment script
    $deployScript = @"
# FamEduConnect Environment Variables Deployment Script - $Environment
# Generated on: $(Get-Date)

Write-Host "Deploying environment variables for $Environment..." -ForegroundColor Green

# Create namespace if it doesn't exist
kubectl create namespace fameduconnect --dry-run=client -o yaml | kubectl apply -f -

# Apply ConfigMaps
Write-Host "Applying ConfigMaps..." -ForegroundColor Yellow
kubectl apply -f k8s-configmaps/

# Apply Secrets
Write-Host "Applying Secrets..." -ForegroundColor Yellow
kubectl apply -f k8s-secrets/

Write-Host "Environment variables deployment completed!" -ForegroundColor Green
Write-Host "Files deployed:" -ForegroundColor Cyan
Write-Host "  - ConfigMaps: k8s-configmaps/" -ForegroundColor White
Write-Host "  - Secrets: k8s-secrets/" -ForegroundColor White
"@
    
    Set-Content "$envDir\deploy-env.ps1" $deployScript
    
    # Create summary file
    $summary = @"
# FamEduConnect Environment Variables Summary - $Environment
# Generated on: $(Get-Date)

## Environment Configuration
- Environment: $Environment
- Node Environment: $NODE_ENV
- Application Port: $PORT

## Database Configuration
- Host: $DB_HOST
- Port: $DB_PORT
- Database: $DB_NAME
- User: $DB_USER
- Password: [SECURE - Generated]

## Redis Configuration
- Host: $REDIS_HOST
- Port: $REDIS_PORT
- Password: [SECURE - Generated]

## JWT Configuration
- Secret: [SECURE - Generated]
- Expires In: $JWT_EXPIRES_IN

## Email Configuration
- SMTP Host: $SMTP_HOST
- SMTP Port: $SMTP_PORT
- From Email: $EMAIL_FROM

## Storage Configuration
- S3 Bucket: $S3_BUCKET
- S3 Region: $S3_REGION

## Monitoring Configuration
- Elasticsearch Host: $ELASTICSEARCH_HOST
- Grafana URL: $GRAFANA_URL
- Prometheus URL: $PROMETHEUS_URL

## Application URLs
- App URL: $APP_URL
- API URL: $API_URL
- Admin URL: $ADMIN_URL

## SSO Configuration
- Provider: $SSO_PROVIDER
- Entity ID: $SSO_ENTITY_ID

## Generated Files
- Backend Environment: backend.env
- Frontend Environment: frontend.env
- Mobile Environment: mobile.env
- Kubernetes Secrets: k8s-secrets/
- Kubernetes ConfigMaps: k8s-configmaps/
- Deployment Script: deploy-env.ps1

## Next Steps
1. Review all generated files
2. Update any missing or incorrect values
3. Run: .\deploy-env.ps1
4. Verify secrets and configmaps are applied
5. Update application deployments to use these configurations
"@
    
    Set-Content "$envDir\SUMMARY.md" $summary
    
    # Step 11: Summary
    Write-Header "Environment Variables Setup Complete!"
    
    Write-Status "Generated files for $Environment environment:"
    Write-Host "  - $envDir\backend.env" -ForegroundColor White
    Write-Host "  - $envDir\frontend.env" -ForegroundColor White
    Write-Host "  - $envDir\mobile.env" -ForegroundColor White
    Write-Host "  - $envDir\k8s-secrets\" -ForegroundColor Yellow
    Write-Host "  - $envDir\k8s-configmaps\" -ForegroundColor White
    Write-Host "  - $envDir\deploy-env.ps1" -ForegroundColor White
    Write-Host "  - $envDir\SUMMARY.md" -ForegroundColor White
    
    Write-Warning "IMPORTANT:"
    Write-Host "  1. Review all generated files before deployment" -ForegroundColor White
    Write-Host "  2. Keep secrets secure and never commit to version control" -ForegroundColor White
    Write-Host "  3. Update any missing or incorrect values" -ForegroundColor White
    Write-Host "  4. Test the configuration in a staging environment first" -ForegroundColor White
    
    Write-Status "Next steps:"
    Write-Host "  1. Review the generated files" -ForegroundColor White
    Write-Host "  2. Update any missing values" -ForegroundColor White
    Write-Host "  3. Run: cd $envDir && .\deploy-env.ps1" -ForegroundColor White
    Write-Host "  4. Verify secrets and configmaps are applied" -ForegroundColor White
    
    Write-Status "Environment variables setup completed successfully!"
}

# Run main function
Main 