FamEduConnect AI IDE Execution Rules

1. Architecture & Tech Stack
Frontend: React (Web), React Native / Flutter (Mobile), Tailwind CSS
Backend: Node.js + Express or Laravel
Database: PostgreSQL or Firebase
Realtime: WebSockets + WebRTC
Multilingual: Google Cloud Translate API or custom NLP
Security:
- End-to-end encryption
- Blockchain-based event logging
- Biometric login (for tablet)
- FERPA, HIPAA, GDPR compliant
Offline Support: Service workers, IndexedDB

2. Core Features
MESSAGING:
- Real-time chat with message scheduling
- Voice messages and image attachments
- Auto-translate into selected language
- AI moderation layer

WEBRTC VIDEO/AUDIO:
- Multi-user video calls (teacher + parents)
- Live multilingual captions and translation
- Auto-recording, transcriptions saved to profile

AI ASSISTANT:
- Summarizes student performance
- Suggests replies for teachers
- Flags concerns to admins (e.g., absences)

SMARTTABLET:
- Kiosk-mode locked to FamEduConnect
- Auto-login with student ID
- Multi-child support per household
- Syncs only via trusted Wi-Fi

3. UI/UX Rules
- Dark mode + High contrast mode
- Animated onboarding
- One-click QR/SMS login
- Large tap zones (for elderly)
- Voice navigation for low-literacy users

4. Connectivity & Offline Support
- Cache messages offline and auto-sync later
- Fall back to voice when video fails
- Critical SMS fallback (absences, emergencies)
- Auto-download school data on Wi-Fi connect

5. Admin & Teacher Tools
- Live attendance tracker
- Student performance dashboards
- Predictive analytics (AI risk alerts)
- Exportable reports (PDF, CSV)
- Classwide calendar and notices

6. Testing & Deployment
- Unit, integration, and end-to-end testing
- Tablet + iOS/Android device testing
- Simulate poor network & battery failovers
- Confirm encryption at rest + in transit
- CI/CD pipeline (GitHub Actions, Firebase Hosting)

AI IDE Prompt:
Using this PRD and rule set, generate full production-ready code for FamEduConnect. Include all major components: messaging, WebRTC video, multilingual AI assistant, admin dashboard, mobile apps, and offline SmartTablet app. Apply full accessibility, encryption, and compliance layers. Stack: React, React Native, Node.js, PostgreSQL, WebRTC, Tailwind. Ensure all UI/UX flows match the provided mockups. Include fallback systems, tablet kiosk mode, and multilingual onboarding.