# Production Environment Configuration
NODE_ENV=production
PORT=5555

# Database Configuration (Production)
DB_HOST=your_production_db_host
DB_PORT=5432
DB_NAME=fameduconnect_prod
DB_USER=fameduconnect_user
DB_PASSWORD=your_secure_production_password
DB_SSL=true

# JWT Configuration (Production)
JWT_SECRET=your_ultra_secure_production_jwt_secret_256_bit_key_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_ultra_secure_refresh_token_secret_here
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your_ultra_secure_session_secret_here
CORS_ORIGIN=https://fameduconnect.app,https://fameduconnect.xyz,https://fameduconnect.com

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/ssl/certs/fameduconnect.crt
SSL_KEY_PATH=/etc/ssl/private/fameduconnect.key

# WebRTC Configuration (Production)
TURN_SERVER_URL=turn:turn.fameduconnect.xyz:3478
TURN_USERNAME=fameduconnect_turn_user
TURN_CREDENTIAL=your_secure_turn_password
STUN_SERVER_URL=stun:stun.fameduconnect.xyz:3478

# Google Cloud Translation (Production)
GOOGLE_CLOUD_PROJECT_ID=fameduconnect-prod
GOOGLE_CLOUD_KEY_FILE=/app/config/google-cloud-key.json

# File Upload Configuration (Production)
MAX_FILE_SIZE=52428800
UPLOAD_PATH=/app/uploads
CDN_URL=https://cdn.fameduconnect.xyz

# Email Configuration (Production)
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>

# Frontend URLs (Production)
FRONTEND_URL=https://fameduconnect.app
ADMIN_URL=https://fameduconnect.xyz
LANDING_URL=https://fameduconnect.com

# Redis Configuration (Production)
REDIS_URL=redis://redis.fameduconnect.xyz:6379
REDIS_PASSWORD=your_secure_redis_password

# Rate Limiting (Production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# Monitoring & Logging
SENTRY_DSN=https://<EMAIL>/project_id
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# Blockchain Configuration
BLOCKCHAIN_NETWORK=mainnet
BLOCKCHAIN_PRIVATE_KEY=your_blockchain_private_key
BLOCKCHAIN_CONTRACT_ADDRESS=0x...

# Compliance & Security
FERPA_COMPLIANCE=true
HIPAA_COMPLIANCE=true
GDPR_COMPLIANCE=true
AUDIT_LOG_RETENTION_DAYS=2555
ENCRYPTION_KEY=your_256_bit_encryption_key_here

# Performance
MAX_CONNECTIONS=100
CONNECTION_TIMEOUT=30000
QUERY_TIMEOUT=10000

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/app/backups