module.exports = (sequelize, DataTypes) => {
  const Message = sequelize.define('Message', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    senderId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    recipientId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    classId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Classes',
        key: 'id'
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    originalLanguage: {
      type: DataTypes.STRING,
      defaultValue: 'en'
    },
    messageType: {
      type: DataTypes.ENUM('text', 'voice', 'image', 'file', 'video', 'announcement'),
      defaultValue: 'text'
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      defaultValue: 'normal'
    },
    isRead: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    readAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    isEncrypted: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    encryptionKey: {
      type: DataTypes.STRING,
      allowNull: true
    },
    attachments: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    metadata: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    scheduledFor: {
      type: DataTypes.DATE,
      allowNull: true
    },
    isSent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    sentAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    parentMessageId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Messages',
        key: 'id'
      }
    },
    threadId: {
      type: DataTypes.UUID,
      allowNull: true
    },
    aiModerated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    moderationFlags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    sentiment: {
      type: DataTypes.ENUM('positive', 'neutral', 'negative'),
      allowNull: true
    },
    urgencyScore: {
      type: DataTypes.DECIMAL(3, 2),
      defaultValue: 0.00,
      validate: {
        min: 0,
        max: 1
      }
    }
  }, {
    indexes: [
      { fields: ['senderId'] },
      { fields: ['recipientId'] },
      { fields: ['classId'] },
      { fields: ['messageType'] },
      { fields: ['priority'] },
      { fields: ['isRead'] },
      { fields: ['scheduledFor'] },
      { fields: ['threadId'] },
      { fields: ['createdAt'] }
    ]
  });

  Message.associate = function(models) {
    Message.belongsTo(models.User, {
      foreignKey: 'senderId',
      as: 'sender'
    });
    Message.belongsTo(models.User, {
      foreignKey: 'recipientId',
      as: 'recipient'
    });
    Message.belongsTo(models.Class, {
      foreignKey: 'classId',
      as: 'class'
    });
    Message.belongsTo(models.Message, {
      foreignKey: 'parentMessageId',
      as: 'parentMessage'
    });
    Message.hasMany(models.Message, {
      foreignKey: 'parentMessageId',
      as: 'replies'
    });
    Message.hasMany(models.Translation, {
      foreignKey: 'messageId',
      as: 'translations'
    });
  };

  return Message;
};