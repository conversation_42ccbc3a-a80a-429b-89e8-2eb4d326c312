#!/bin/bash

# FamEduConnect Enterprise Environment Variables Setup Script
# This script helps configure all environment variables and secrets for enterprise deployment

set -e

# Default values
FORCE=false
ENVIRONMENT="production"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE=true
            shift
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
    fi
    
    eval "$var_name='$input'"
}

# Function to generate secure password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Function to base64 encode
base64_encode() {
    echo -n "$1" | base64
}

# Function to create Kubernetes secret
create_k8s_secret() {
    local secret_name="$1"
    local namespace="$2"
    shift 2
    local data=("$@")
    
    cat << EOF
apiVersion: v1
kind: Secret
metadata:
  name: $secret_name
  namespace: $namespace
type: Opaque
data:
EOF
    
    for item in "${data[@]}"; do
        IFS='=' read -r key value <<< "$item"
        encoded_value=$(base64_encode "$value")
        echo "  $key: $encoded_value"
    done
}

# Function to create ConfigMap
create_k8s_configmap() {
    local configmap_name="$1"
    local namespace="$2"
    shift 2
    local data=("$@")
    
    cat << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: $configmap_name
  namespace: $namespace
data:
EOF
    
    for item in "${data[@]}"; do
        IFS='=' read -r key value <<< "$item"
        echo "  $key: '$value'"
    done
}

# Main script
main() {
    print_header "FamEduConnect Enterprise Environment Variables Setup"
    
    # Check if running from the correct directory
    if [ ! -f "enterprise/ENTERPRISE_DEPLOYMENT_GUIDE.md" ]; then
        print_error "Please run this script from the FamEduConnect_Full_Codebase directory"
        exit 1
    fi
    
    print_status "Setting up environment variables for: $ENVIRONMENT"
    
    # Create environment directory
    env_dir="enterprise/environment-variables/$ENVIRONMENT"
    if [ -d "$env_dir" ]; then
        if [ "$FORCE" != "true" ]; then
            read -p "Environment directory already exists. Overwrite? (y/N): " response
            if [[ ! $response =~ ^[Yy]$ ]]; then
                print_status "Operation cancelled."
                exit 0
            fi
        fi
        rm -rf "$env_dir"
    fi
    mkdir -p "$env_dir"
    
    # Step 1: Database Configuration
    print_header "Step 1: Database Configuration"
    
    prompt_with_default "Enter database host" "postgres-primary.fameduconnect.svc.cluster.local" DB_HOST
    prompt_with_default "Enter database port" "5432" DB_PORT
    prompt_with_default "Enter database name" "fameduconnect_prod" DB_NAME
    prompt_with_default "Enter database user" "fameduconnect" DB_USER
    
    # Generate secure passwords if not provided
    prompt_with_default "Enter database password (leave empty to generate)" "" DB_PASSWORD
    if [ -z "$DB_PASSWORD" ]; then
        DB_PASSWORD=$(generate_password)
        print_status "Generated secure database password"
    fi
    
    prompt_with_default "Enter replication password (leave empty to generate)" "" DB_REPLICATION_PASSWORD
    if [ -z "$DB_REPLICATION_PASSWORD" ]; then
        DB_REPLICATION_PASSWORD=$(generate_password)
        print_status "Generated secure replication password"
    fi
    
    prompt_with_default "Enter backup password (leave empty to generate)" "" DB_BACKUP_PASSWORD
    if [ -z "$DB_BACKUP_PASSWORD" ]; then
        DB_BACKUP_PASSWORD=$(generate_password)
        print_status "Generated secure backup password"
    fi
    
    # Step 2: Redis Configuration
    print_header "Step 2: Redis Configuration"
    
    prompt_with_default "Enter Redis host" "redis-cluster.fameduconnect.svc.cluster.local" REDIS_HOST
    prompt_with_default "Enter Redis port" "6379" REDIS_PORT
    prompt_with_default "Enter Redis password (leave empty to generate)" "" REDIS_PASSWORD
    if [ -z "$REDIS_PASSWORD" ]; then
        REDIS_PASSWORD=$(generate_password)
        print_status "Generated secure Redis password"
    fi
    
    # Step 3: JWT Configuration
    print_header "Step 3: JWT Configuration"
    
    prompt_with_default "Enter JWT secret (leave empty to generate)" "" JWT_SECRET
    if [ -z "$JWT_SECRET" ]; then
        JWT_SECRET=$(generate_password)
        print_status "Generated secure JWT secret"
    fi
    
    prompt_with_default "Enter JWT expiration time" "24h" JWT_EXPIRES_IN
    
    # Step 4: Email Configuration
    print_header "Step 4: Email Configuration"
    
    prompt_with_default "Enter SMTP host" "smtp.gmail.com" SMTP_HOST
    prompt_with_default "Enter SMTP port" "587" SMTP_PORT
    prompt_with_default "Enter SMTP username" "" SMTP_USER
    prompt_with_default "Enter SMTP password" "" SMTP_PASSWORD
    prompt_with_default "Enter from email address" "<EMAIL>" EMAIL_FROM
    
    # Step 5: Storage Configuration
    print_header "Step 5: Storage Configuration"
    
    prompt_with_default "Enter S3 bucket name" "fameduconnect-storage" S3_BUCKET
    prompt_with_default "Enter S3 region" "us-east-1" S3_REGION
    prompt_with_default "Enter S3 access key" "" S3_ACCESS_KEY
    prompt_with_default "Enter S3 secret key" "" S3_SECRET_KEY
    
    # Step 6: Monitoring Configuration
    print_header "Step 6: Monitoring Configuration"
    
    prompt_with_default "Enter Elasticsearch host" "elasticsearch-master.fameduconnect.svc.cluster.local" ELASTICSEARCH_HOST
    prompt_with_default "Enter Elasticsearch port" "9200" ELASTICSEARCH_PORT
    prompt_with_default "Enter Elasticsearch username" "elastic" ELASTICSEARCH_USERNAME
    prompt_with_default "Enter Elasticsearch password (leave empty to generate)" "" ELASTICSEARCH_PASSWORD
    if [ -z "$ELASTICSEARCH_PASSWORD" ]; then
        ELASTICSEARCH_PASSWORD=$(generate_password)
        print_status "Generated secure Elasticsearch password"
    fi
    
    prompt_with_default "Enter Grafana URL" "https://grafana.fameduconnect.com" GRAFANA_URL
    prompt_with_default "Enter Prometheus URL" "https://prometheus.fameduconnect.com" PROMETHEUS_URL
    
    # Step 7: External Services
    print_header "Step 7: External Services"
    
    prompt_with_default "Enter Sentry DSN (leave empty if not using)" "" SENTRY_DSN
    prompt_with_default "Enter Twilio Account SID (leave empty if not using)" "" TWILIO_ACCOUNT_SID
    prompt_with_default "Enter Twilio Auth Token (leave empty if not using)" "" TWILIO_AUTH_TOKEN
    prompt_with_default "Enter Google Translate API Key (leave empty if not using)" "" GOOGLE_TRANSLATE_API_KEY
    
    # Step 8: SSO Configuration (Optional)
    print_header "Step 8: SSO Configuration (Optional)"
    
    read -p "Do you want to configure SSO (SAML/OIDC)? (y/N): " configure_sso
    if [[ $configure_sso =~ ^[Yy]$ ]]; then
        prompt_with_default "Enter SSO provider (saml/oidc)" "saml" SSO_PROVIDER
        prompt_with_default "Enter SSO Entity ID" "https://api.fameduconnect.com/saml/metadata" SSO_ENTITY_ID
        prompt_with_default "Enter SSO ACS URL" "https://api.fameduconnect.com/saml/acs" SSO_ACS_URL
        prompt_with_default "Enter SSO IdP SSO URL" "" SSO_IDP_SSO_URL
        prompt_with_default "Enter SSO IdP Certificate (base64)" "" SSO_IDP_CERT
        prompt_with_default "Enter SSO Client ID (for OIDC)" "" SSO_CLIENT_ID
        prompt_with_default "Enter SSO Client Secret (for OIDC)" "" SSO_CLIENT_SECRET
    else
        SSO_PROVIDER=""
        SSO_ENTITY_ID=""
        SSO_ACS_URL=""
        SSO_IDP_SSO_URL=""
        SSO_IDP_CERT=""
        SSO_CLIENT_ID=""
        SSO_CLIENT_SECRET=""
    fi
    
    # Step 9: Application Configuration
    print_header "Step 9: Application Configuration"
    
    prompt_with_default "Enter application URL" "https://app.fameduconnect.com" APP_URL
    prompt_with_default "Enter API URL" "https://api.fameduconnect.com" API_URL
    prompt_with_default "Enter admin URL" "https://admin.fameduconnect.com" ADMIN_URL
    
    NODE_ENV="$ENVIRONMENT"
    prompt_with_default "Enter application port" "5555" PORT
    prompt_with_default "Enter CORS origin" "https://app.fameduconnect.com" CORS_ORIGIN
    
    # Step 10: Generate Configuration Files
    print_header "Step 10: Generating Configuration Files"
    
    # Create backend environment file
    cat > "$env_dir/backend.env" << EOF
# FamEduConnect Backend Environment Variables - $ENVIRONMENT
# Generated on: $(date)

# Server Configuration
NODE_ENV=$NODE_ENV
PORT=$PORT
HOST=0.0.0.0
TRUST_PROXY=true
API_VERSION=v1
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
MAX_FILE_SIZE=50mb
COMPRESSION_LEVEL=6

# Database Configuration
DATABASE_URL=postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME
DATABASE_SSL=true
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20
DATABASE_IDLE_TIMEOUT=30000
DATABASE_CONNECTION_TIMEOUT=2000

# Redis Configuration
REDIS_URL=redis://:$REDIS_PASSWORD@$REDIS_HOST:$REDIS_PORT
REDIS_CLUSTER_MODE=true
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000

# JWT Configuration
JWT_SECRET=$JWT_SECRET
JWT_EXPIRES_IN=$JWT_EXPIRES_IN
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=$SMTP_HOST
SMTP_PORT=$SMTP_PORT
SMTP_USER=$SMTP_USER
SMTP_PASSWORD=$SMTP_PASSWORD
EMAIL_FROM=$EMAIL_FROM
EMAIL_TEMPLATES_PATH=./email-templates

# Storage Configuration
S3_BUCKET=$S3_BUCKET
S3_REGION=$S3_REGION
S3_ACCESS_KEY=$S3_ACCESS_KEY
S3_SECRET_KEY=$S3_SECRET_KEY
S3_ENDPOINT=https://s3.$S3_REGION.amazonaws.com

# Monitoring Configuration
ELASTICSEARCH_HOST=$ELASTICSEARCH_HOST
ELASTICSEARCH_PORT=$ELASTICSEARCH_PORT
ELASTICSEARCH_USERNAME=$ELASTICSEARCH_USERNAME
ELASTICSEARCH_PASSWORD=$ELASTICSEARCH_PASSWORD
GRAFANA_URL=$GRAFANA_URL
PROMETHEUS_URL=$PROMETHEUS_URL

# External Services
SENTRY_DSN=$SENTRY_DSN
TWILIO_ACCOUNT_SID=$TWILIO_ACCOUNT_SID
TWILIO_AUTH_TOKEN=$TWILIO_AUTH_TOKEN
GOOGLE_TRANSLATE_API_KEY=$GOOGLE_TRANSLATE_API_KEY

# SSO Configuration
SSO_PROVIDER=$SSO_PROVIDER
SSO_ENTITY_ID=$SSO_ENTITY_ID
SSO_ACS_URL=$SSO_ACS_URL
SSO_IDP_SSO_URL=$SSO_IDP_SSO_URL
SSO_IDP_CERT=$SSO_IDP_CERT
SSO_CLIENT_ID=$SSO_CLIENT_ID
SSO_CLIENT_SECRET=$SSO_CLIENT_SECRET

# Application URLs
APP_URL=$APP_URL
API_URL=$API_URL
ADMIN_URL=$ADMIN_URL
CORS_ORIGIN=$CORS_ORIGIN

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=$JWT_SECRET
COOKIE_SECRET=$JWT_SECRET
CSRF_SECRET=$JWT_SECRET

# Feature Flags
ENABLE_VIDEO_CALLS=true
ENABLE_TRANSLATION=true
ENABLE_AI_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=100mb
LOG_MAX_FILES=10

# Performance Configuration
CACHE_TTL=3600
RATE_LIMIT_ENABLED=true
COMPRESSION_ENABLED=true
HELMET_ENABLED=true
EOF
    
    print_status "Created backend environment file: $env_dir/backend.env"
    
    # Create frontend environment file
    cat > "$env_dir/frontend.env" << EOF
# FamEduConnect Frontend Environment Variables - $ENVIRONMENT
# Generated on: $(date)

# API Configuration
REACT_APP_API_URL=$API_URL
REACT_APP_SOCKET_URL=wss://api.fameduconnect.com
REACT_APP_APP_URL=$APP_URL
REACT_APP_ADMIN_URL=$ADMIN_URL

# External Services
REACT_APP_SENTRY_DSN=$SENTRY_DSN
REACT_APP_GOOGLE_ANALYTICS_ID=
REACT_APP_MIXPANEL_TOKEN=

# Feature Flags
REACT_APP_ENABLE_VIDEO_CALLS=true
REACT_APP_ENABLE_TRANSLATION=true
REACT_APP_ENABLE_AI_FEATURES=true
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_NOTIFICATIONS=true

# UI Configuration
REACT_APP_THEME=default
REACT_APP_LANGUAGE=en
REACT_APP_TIMEZONE=UTC
REACT_APP_DATE_FORMAT=MM/DD/YYYY
REACT_APP_TIME_FORMAT=HH:mm

# Security Configuration
REACT_APP_ENABLE_HTTPS=true
REACT_APP_ENABLE_CSP=true
REACT_APP_ENABLE_HSTS=true

# Performance Configuration
REACT_APP_ENABLE_CACHE=true
REACT_APP_CACHE_TTL=3600
REACT_APP_ENABLE_COMPRESSION=true
EOF
    
    print_status "Created frontend environment file: $env_dir/frontend.env"
    
    # Create mobile environment file
    cat > "$env_dir/mobile.env" << EOF
# FamEduConnect Mobile Environment Variables - $ENVIRONMENT
# Generated on: $(date)

# API Configuration
EXPO_PUBLIC_API_URL=$API_URL
EXPO_PUBLIC_SOCKET_URL=wss://api.fameduconnect.com
EXPO_PUBLIC_APP_URL=$APP_URL

# External Services
EXPO_PUBLIC_SENTRY_DSN=$SENTRY_DSN
EXPO_PUBLIC_GOOGLE_ANALYTICS_ID=

# Feature Flags
EXPO_PUBLIC_ENABLE_VIDEO_CALLS=true
EXPO_PUBLIC_ENABLE_TRANSLATION=true
EXPO_PUBLIC_ENABLE_AI_FEATURES=true
EXPO_PUBLIC_ENABLE_ANALYTICS=true
EXPO_PUBLIC_ENABLE_NOTIFICATIONS=true

# UI Configuration
EXPO_PUBLIC_THEME=default
EXPO_PUBLIC_LANGUAGE=en
EXPO_PUBLIC_TIMEZONE=UTC
EOF
    
    print_status "Created mobile environment file: $env_dir/mobile.env"
    
    # Create Kubernetes secrets
    print_status "Creating Kubernetes secrets..."
    mkdir -p "$env_dir/k8s-secrets"
    
    # Database secrets
    create_k8s_secret "postgres-secret" "fameduconnect" \
        "POSTGRES_PASSWORD=$DB_PASSWORD" \
        "POSTGRES_REPLICATION_PASSWORD=$DB_REPLICATION_PASSWORD" \
        "POSTGRES_BACKUP_PASSWORD=$DB_BACKUP_PASSWORD" > "$env_dir/k8s-secrets/postgres-secret.yaml"
    
    # Redis secrets
    create_k8s_secret "redis-secret" "fameduconnect" \
        "REDIS_PASSWORD=$REDIS_PASSWORD" > "$env_dir/k8s-secrets/redis-secret.yaml"
    
    # Application secrets
    create_k8s_secret "app-secret" "fameduconnect" \
        "JWT_SECRET=$JWT_SECRET" \
        "SESSION_SECRET=$JWT_SECRET" \
        "COOKIE_SECRET=$JWT_SECRET" \
        "CSRF_SECRET=$JWT_SECRET" > "$env_dir/k8s-secrets/app-secret.yaml"
    
    # Email secrets (if configured)
    if [ -n "$SMTP_USER" ] && [ -n "$SMTP_PASSWORD" ]; then
        create_k8s_secret "email-secret" "fameduconnect" \
            "SMTP_USER=$SMTP_USER" \
            "SMTP_PASSWORD=$SMTP_PASSWORD" > "$env_dir/k8s-secrets/email-secret.yaml"
    fi
    
    # Storage secrets (if configured)
    if [ -n "$S3_ACCESS_KEY" ] && [ -n "$S3_SECRET_KEY" ]; then
        create_k8s_secret "storage-secret" "fameduconnect" \
            "S3_ACCESS_KEY=$S3_ACCESS_KEY" \
            "S3_SECRET_KEY=$S3_SECRET_KEY" > "$env_dir/k8s-secrets/storage-secret.yaml"
    fi
    
    # Monitoring secrets
    create_k8s_secret "monitoring-secret" "fameduconnect" \
        "ELASTICSEARCH_USERNAME=$ELASTICSEARCH_USERNAME" \
        "ELASTICSEARCH_PASSWORD=$ELASTICSEARCH_PASSWORD" > "$env_dir/k8s-secrets/monitoring-secret.yaml"
    
    # SSO secrets (if configured)
    if [ -n "$SSO_PROVIDER" ]; then
        sso_data=("SSO_ENTITY_ID=$SSO_ENTITY_ID" "SSO_ACS_URL=$SSO_ACS_URL" "SSO_IDP_SSO_URL=$SSO_IDP_SSO_URL" "SSO_IDP_CERT=$SSO_IDP_CERT")
        if [ -n "$SSO_CLIENT_ID" ] && [ -n "$SSO_CLIENT_SECRET" ]; then
            sso_data+=("SSO_CLIENT_ID=$SSO_CLIENT_ID" "SSO_CLIENT_SECRET=$SSO_CLIENT_SECRET")
        fi
        create_k8s_secret "sso-secret" "fameduconnect" "${sso_data[@]}" > "$env_dir/k8s-secrets/sso-secret.yaml"
    fi
    
    # External services secrets
    external_data=()
    [ -n "$SENTRY_DSN" ] && external_data+=("SENTRY_DSN=$SENTRY_DSN")
    [ -n "$TWILIO_ACCOUNT_SID" ] && external_data+=("TWILIO_ACCOUNT_SID=$TWILIO_ACCOUNT_SID")
    [ -n "$TWILIO_AUTH_TOKEN" ] && external_data+=("TWILIO_AUTH_TOKEN=$TWILIO_AUTH_TOKEN")
    [ -n "$GOOGLE_TRANSLATE_API_KEY" ] && external_data+=("GOOGLE_TRANSLATE_API_KEY=$GOOGLE_TRANSLATE_API_KEY")
    
    if [ ${#external_data[@]} -gt 0 ]; then
        create_k8s_secret "external-services-secret" "fameduconnect" "${external_data[@]}" > "$env_dir/k8s-secrets/external-services-secret.yaml"
    fi
    
    # Create ConfigMaps
    print_status "Creating Kubernetes ConfigMaps..."
    mkdir -p "$env_dir/k8s-configmaps"
    
    # Application ConfigMap
    create_k8s_configmap "app-config" "fameduconnect" \
        "NODE_ENV=$NODE_ENV" \
        "PORT=$PORT" \
        "API_VERSION=v1" \
        "APP_URL=$APP_URL" \
        "API_URL=$API_URL" \
        "ADMIN_URL=$ADMIN_URL" \
        "CORS_ORIGIN=$CORS_ORIGIN" \
        "ENABLE_VIDEO_CALLS=true" \
        "ENABLE_TRANSLATION=true" \
        "ENABLE_AI_FEATURES=true" \
        "ENABLE_ANALYTICS=true" \
        "ENABLE_NOTIFICATIONS=true" > "$env_dir/k8s-configmaps/app-config.yaml"
    
    # Database ConfigMap
    create_k8s_configmap "database-config" "fameduconnect" \
        "DB_HOST=$DB_HOST" \
        "DB_PORT=$DB_PORT" \
        "DB_NAME=$DB_NAME" \
        "DB_USER=$DB_USER" \
        "DATABASE_URL=postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME" > "$env_dir/k8s-configmaps/database-config.yaml"
    
    # Create deployment script
    cat > "$env_dir/deploy-env.sh" << EOF
#!/bin/bash

# FamEduConnect Environment Variables Deployment Script - $ENVIRONMENT
# Generated on: $(date)

echo "Deploying environment variables for $ENVIRONMENT..."

# Create namespace if it doesn't exist
kubectl create namespace fameduconnect --dry-run=client -o yaml | kubectl apply -f -

# Apply ConfigMaps
echo "Applying ConfigMaps..."
kubectl apply -f k8s-configmaps/

# Apply Secrets
echo "Applying Secrets..."
kubectl apply -f k8s-secrets/

echo "Environment variables deployment completed!"
echo "Files deployed:"
echo "  - ConfigMaps: k8s-configmaps/"
echo "  - Secrets: k8s-secrets/"
EOF
    
    chmod +x "$env_dir/deploy-env.sh"
    
    # Create summary file
    cat > "$env_dir/SUMMARY.md" << EOF
# FamEduConnect Environment Variables Summary - $ENVIRONMENT
# Generated on: $(date)

## Environment Configuration
- Environment: $ENVIRONMENT
- Node Environment: $NODE_ENV
- Application Port: $PORT

## Database Configuration
- Host: $DB_HOST
- Port: $DB_PORT
- Database: $DB_NAME
- User: $DB_USER
- Password: [SECURE - Generated]

## Redis Configuration
- Host: $REDIS_HOST
- Port: $REDIS_PORT
- Password: [SECURE - Generated]

## JWT Configuration
- Secret: [SECURE - Generated]
- Expires In: $JWT_EXPIRES_IN

## Email Configuration
- SMTP Host: $SMTP_HOST
- SMTP Port: $SMTP_PORT
- From Email: $EMAIL_FROM

## Storage Configuration
- S3 Bucket: $S3_BUCKET
- S3 Region: $S3_REGION

## Monitoring Configuration
- Elasticsearch Host: $ELASTICSEARCH_HOST
- Grafana URL: $GRAFANA_URL
- Prometheus URL: $PROMETHEUS_URL

## Application URLs
- App URL: $APP_URL
- API URL: $API_URL
- Admin URL: $ADMIN_URL

## SSO Configuration
- Provider: $SSO_PROVIDER
- Entity ID: $SSO_ENTITY_ID

## Generated Files
- Backend Environment: backend.env
- Frontend Environment: frontend.env
- Mobile Environment: mobile.env
- Kubernetes Secrets: k8s-secrets/
- Kubernetes ConfigMaps: k8s-configmaps/
- Deployment Script: deploy-env.sh

## Next Steps
1. Review all generated files
2. Update any missing or incorrect values
3. Run: ./deploy-env.sh
4. Verify secrets and configmaps are applied
5. Update application deployments to use these configurations
EOF
    
    # Step 11: Summary
    print_header "Environment Variables Setup Complete!"
    
    print_status "Generated files for $ENVIRONMENT environment:"
    echo "  - $env_dir/backend.env"
    echo "  - $env_dir/frontend.env"
    echo "  - $env_dir/mobile.env"
    echo "  - $env_dir/k8s-secrets/"
    echo "  - $env_dir/k8s-configmaps/"
    echo "  - $env_dir/deploy-env.sh"
    echo "  - $env_dir/SUMMARY.md"
    
    print_warning "IMPORTANT:"
    echo "  1. Review all generated files before deployment"
    echo "  2. Keep secrets secure and never commit to version control"
    echo "  3. Update any missing or incorrect values"
    echo "  4. Test the configuration in a staging environment first"
    
    print_status "Next steps:"
    echo "  1. Review the generated files"
    echo "  2. Update any missing values"
    echo "  3. Run: cd $env_dir && ./deploy-env.sh"
    echo "  4. Verify secrets and configmaps are applied"
    
    print_status "Environment variables setup completed successfully!"
}

# Run main function
main "$@" 