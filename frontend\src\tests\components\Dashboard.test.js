import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import Dashboard from '../../pages/Dashboard/Dashboard';
import authSlice from '../../store/slices/authSlice';

const mockStore = configureStore({
  reducer: {
    auth: authSlice,
  },
  preloadedState: {
    auth: {
      user: { name: 'Test User', role: 'parent' },
      token: 'test-token',
    },
  },
});

describe('Dashboard Component', () => {
  it('renders dashboard correctly', () => {
    render(
      <Provider store={mockStore}>
        <Dashboard />
      </Provider>
    );

    expect(screen.getByText(/Welcome/i)).toBeInTheDocument();
  });
});