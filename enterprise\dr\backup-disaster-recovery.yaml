apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-config
  namespace: fameduconnect
data:
  backup-script.sh: |
    #!/bin/bash
    set -e
    
    # Backup configuration
    BACKUP_DIR="/backups"
    DATE=$(date +%Y%m%d_%H%M%S)
    BACKUP_NAME="fameduconnect_backup_${DATE}"
    
    # Database backup
    echo "Starting database backup..."
    pg_dump -h postgres-primary -U fameduconnect -d fameduconnect_prod \
      --format=custom --compress=9 \
      --file="${BACKUP_DIR}/${BACKUP_NAME}.sql"
    
    # Redis backup
    echo "Starting Redis backup..."
    redis-cli -h redis-cluster -a $REDIS_PASSWORD --rdb "${BACKUP_DIR}/${BACKUP_NAME}.rdb"
    
    # File storage backup
    echo "Starting file storage backup..."
    tar -czf "${BACKUP_DIR}/${BACKUP_NAME}_files.tar.gz" /uploads/
    
    # Configuration backup
    echo "Starting configuration backup..."
    kube<PERSON>l get configmaps,secrets -n fameduconnect -o yaml > "${BACKUP_DIR}/${BACKUP_NAME}_config.yaml"
    
    # Create backup manifest
    cat > "${BACKUP_DIR}/${BACKUP_NAME}_manifest.json" << EOF
    {
      "backup_id": "${BACKUP_NAME}",
      "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
      "version": "1.0.0",
      "components": {
        "database": "${BACKUP_NAME}.sql",
        "redis": "${BACKUP_NAME}.rdb",
        "files": "${BACKUP_NAME}_files.tar.gz",
        "config": "${BACKUP_NAME}_config.yaml"
      },
      "checksums": {
        "database": "$(sha256sum ${BACKUP_DIR}/${BACKUP_NAME}.sql | cut -d' ' -f1)",
        "redis": "$(sha256sum ${BACKUP_DIR}/${BACKUP_NAME}.rdb | cut -d' ' -f1)",
        "files": "$(sha256sum ${BACKUP_DIR}/${BACKUP_NAME}_files.tar.gz | cut -d' ' -f1)",
        "config": "$(sha256sum ${BACKUP_DIR}/${BACKUP_NAME}_config.yaml | cut -d' ' -f1)"
      }
    }
    EOF
    
    # Upload to S3
    echo "Uploading backup to S3..."
    aws s3 cp "${BACKUP_DIR}/${BACKUP_NAME}.sql" "s3://fameduconnect-backups/database/"
    aws s3 cp "${BACKUP_DIR}/${BACKUP_NAME}.rdb" "s3://fameduconnect-backups/redis/"
    aws s3 cp "${BACKUP_DIR}/${BACKUP_NAME}_files.tar.gz" "s3://fameduconnect-backups/files/"
    aws s3 cp "${BACKUP_DIR}/${BACKUP_NAME}_config.yaml" "s3://fameduconnect-backups/config/"
    aws s3 cp "${BACKUP_DIR}/${BACKUP_NAME}_manifest.json" "s3://fameduconnect-backups/manifests/"
    
    # Cleanup old backups (keep last 30 days)
    find "${BACKUP_DIR}" -name "fameduconnect_backup_*" -mtime +30 -delete
    
    echo "Backup completed successfully: ${BACKUP_NAME}"
  
  restore-script.sh: |
    #!/bin/bash
    set -e
    
    BACKUP_ID=$1
    RESTORE_DIR="/restore"
    
    if [ -z "$BACKUP_ID" ]; then
      echo "Usage: $0 <backup_id>"
      exit 1
    fi
    
    echo "Starting restore from backup: ${BACKUP_ID}"
    
    # Download from S3
    echo "Downloading backup from S3..."
    aws s3 cp "s3://fameduconnect-backups/database/${BACKUP_ID}.sql" "${RESTORE_DIR}/"
    aws s3 cp "s3://fameduconnect-backups/redis/${BACKUP_ID}.rdb" "${RESTORE_DIR}/"
    aws s3 cp "s3://fameduconnect-backups/files/${BACKUP_ID}_files.tar.gz" "${RESTORE_DIR}/"
    aws s3 cp "s3://fameduconnect-backups/config/${BACKUP_ID}_config.yaml" "${RESTORE_DIR}/"
    aws s3 cp "s3://fameduconnect-backups/manifests/${BACKUP_ID}_manifest.json" "${RESTORE_DIR}/"
    
    # Verify checksums
    echo "Verifying backup integrity..."
    MANIFEST="${RESTORE_DIR}/${BACKUP_ID}_manifest.json"
    EXPECTED_DB_CHECKSUM=$(jq -r '.checksums.database' "$MANIFEST")
    EXPECTED_REDIS_CHECKSUM=$(jq -r '.checksums.redis' "$MANIFEST")
    EXPECTED_FILES_CHECKSUM=$(jq -r '.checksums.files' "$MANIFEST")
    
    ACTUAL_DB_CHECKSUM=$(sha256sum "${RESTORE_DIR}/${BACKUP_ID}.sql" | cut -d' ' -f1)
    ACTUAL_REDIS_CHECKSUM=$(sha256sum "${RESTORE_DIR}/${BACKUP_ID}.rdb" | cut -d' ' -f1)
    ACTUAL_FILES_CHECKSUM=$(sha256sum "${RESTORE_DIR}/${BACKUP_ID}_files.tar.gz" | cut -d' ' -f1)
    
    if [ "$EXPECTED_DB_CHECKSUM" != "$ACTUAL_DB_CHECKSUM" ]; then
      echo "Database backup checksum mismatch!"
      exit 1
    fi
    
    if [ "$EXPECTED_REDIS_CHECKSUM" != "$ACTUAL_REDIS_CHECKSUM" ]; then
      echo "Redis backup checksum mismatch!"
      exit 1
    fi
    
    if [ "$EXPECTED_FILES_CHECKSUM" != "$ACTUAL_FILES_CHECKSUM" ]; then
      echo "Files backup checksum mismatch!"
      exit 1
    fi
    
    # Restore database
    echo "Restoring database..."
    pg_restore -h postgres-primary -U fameduconnect -d fameduconnect_prod \
      --clean --if-exists --no-owner --no-privileges \
      "${RESTORE_DIR}/${BACKUP_ID}.sql"
    
    # Restore Redis
    echo "Restoring Redis..."
    redis-cli -h redis-cluster -a $REDIS_PASSWORD FLUSHALL
    redis-cli -h redis-cluster -a $REDIS_PASSWORD --rdb "${RESTORE_DIR}/${BACKUP_ID}.rdb"
    
    # Restore files
    echo "Restoring files..."
    tar -xzf "${RESTORE_DIR}/${BACKUP_ID}_files.tar.gz" -C /
    
    # Restore configuration
    echo "Restoring configuration..."
    kubectl apply -f "${RESTORE_DIR}/${BACKUP_ID}_config.yaml"
    
    echo "Restore completed successfully from backup: ${BACKUP_ID}"
  
  dr-test-script.sh: |
    #!/bin/bash
    set -e
    
    echo "Starting Disaster Recovery Test..."
    TEST_DATE=$(date +%Y%m%d_%H%M%S)
    TEST_BACKUP_ID="dr_test_${TEST_DATE}"
    
    # Create test backup
    echo "Creating test backup..."
    ./backup-script.sh
    
    # Simulate disaster
    echo "Simulating disaster scenario..."
    kubectl scale deployment fameduconnect-backend --replicas=0 -n fameduconnect
    kubectl scale deployment fameduconnect-frontend --replicas=0 -n fameduconnect
    kubectl scale deployment fameduconnect-admin --replicas=0 -n fameduconnect
    
    # Wait for services to be down
    sleep 30
    
    # Restore from backup
    echo "Restoring from backup..."
    ./restore-script.sh "$TEST_BACKUP_ID"
    
    # Scale services back up
    echo "Scaling services back up..."
    kubectl scale deployment fameduconnect-backend --replicas=3 -n fameduconnect
    kubectl scale deployment fameduconnect-frontend --replicas=2 -n fameduconnect
    kubectl scale deployment fameduconnect-admin --replicas=2 -n fameduconnect
    
    # Wait for services to be ready
    echo "Waiting for services to be ready..."
    kubectl wait --for=condition=available deployment/fameduconnect-backend -n fameduconnect --timeout=300s
    kubectl wait --for=condition=available deployment/fameduconnect-frontend -n fameduconnect --timeout=300s
    kubectl wait --for=condition=available deployment/fameduconnect-admin -n fameduconnect --timeout=300s
    
    # Run health checks
    echo "Running health checks..."
    curl -f http://fameduconnect-backend:5555/health || exit 1
    curl -f http://fameduconnect-frontend:3000/ || exit 1
    curl -f http://fameduconnect-admin:3001/ || exit 1
    
    echo "Disaster Recovery Test completed successfully!"
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: fameduconnect-backup
  namespace: fameduconnect
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: fameduconnect-backup
          containers:
          - name: backup
            image: fameduconnect/backup:latest
            command: ["/scripts/backup-script.sh"]
            env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: POSTGRES_PASSWORD
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: redis-secret
                  key: redis-password
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-secret
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-secret
                  key: secret-access-key
            - name: AWS_DEFAULT_REGION
              value: "us-east-1"
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
            - name: backup-storage
              mountPath: /backups
            resources:
              requests:
                memory: "256Mi"
                cpu: "250m"
              limits:
                memory: "512Mi"
                cpu: "500m"
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-config
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: fameduconnect-dr-test
  namespace: fameduconnect
spec:
  schedule: "0 4 1 */3 *"  # Quarterly on 1st at 4 AM
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: fameduconnect-backup
          containers:
          - name: dr-test
            image: fameduconnect/backup:latest
            command: ["/scripts/dr-test-script.sh"]
            env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: POSTGRES_PASSWORD
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: redis-secret
                  key: redis-password
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-secret
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-secret
                  key: secret-access-key
            - name: AWS_DEFAULT_REGION
              value: "us-east-1"
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
            - name: backup-storage
              mountPath: /backups
            resources:
              requests:
                memory: "512Mi"
                cpu: "500m"
              limits:
                memory: "1Gi"
                cpu: "1000m"
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-config
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-pvc
  namespace: fameduconnect
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: gp3-encrypted
  resources:
    requests:
      storage: 100Gi
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fameduconnect-backup
  namespace: fameduconnect
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: fameduconnect-backup-role
  namespace: fameduconnect
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: fameduconnect-backup-rolebinding
  namespace: fameduconnect
subjects:
- kind: ServiceAccount
  name: fameduconnect-backup
  namespace: fameduconnect
roleRef:
  kind: Role
  name: fameduconnect-backup-role
  apiGroup: rbac.authorization.k8s.io 