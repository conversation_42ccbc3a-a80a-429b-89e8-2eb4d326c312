variable "environment" {
  description = "Environment name"
  type        = string
}

variable "subnet_ids" {
  description = "Subnet IDs for RabbitMQ"
  type        = list(string)
}

variable "security_groups" {
  description = "Security group IDs for RabbitMQ"
  type        = list(string)
}

variable "mq_username" {
  description = "RabbitMQ username"
  type        = string
}

variable "mq_password" {
  description = "RabbitMQ password"
  type        = string
  sensitive   = true
}

variable "mq_instance_type" {
  description = "Amazon MQ instance type"
  type        = string
} 