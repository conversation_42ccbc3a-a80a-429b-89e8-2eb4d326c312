const axios = require('axios');
const fs = require('fs');

// Configuration for backend-only stress test
const CONFIG = {
  BACKEND_URL: 'http://localhost:3002',
  TEST_DURATION: 300000, // 5 minutes
  CONCURRENT_USERS: 25,
  REQUESTS_PER_SECOND: 50,
  TIMEOUT: 10000
};

// Test Results
const results = {
  health: { success: 0, failed: 0, errors: [] },
  auth: { success: 0, failed: 0, errors: [] },
  api: { success: 0, failed: 0, errors: [] },
  performance: { 
    avgResponseTime: 0, 
    maxResponseTime: 0, 
    minResponseTime: Infinity,
    responseTimes: []
  },
  startTime: Date.now()
};

// Backend endpoints to test
const ENDPOINTS = {
  health: '/api/health',
  test: '/api/test',
  auth: '/api/auth/login',
  users: '/api/users/profile',
  messages: '/api/messages',
  students: '/api/students',
  classes: '/api/classes'
};

// Test credentials
const TEST_CREDENTIALS = [
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'password123' }
];

// Utility functions
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const randomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

// Performance metrics
function updatePerformanceMetrics(responseTime) {
  results.performance.responseTimes.push(responseTime);
  results.performance.avgResponseTime = 
    results.performance.responseTimes.reduce((a, b) => a + b, 0) / results.performance.responseTimes.length;
  results.performance.maxResponseTime = Math.max(results.performance.maxResponseTime, responseTime);
  results.performance.minResponseTime = Math.min(results.performance.minResponseTime, responseTime);
}

// Health check stress test
async function testHealthEndpoint() {
  console.log('🏥 Testing Health Endpoint...');
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}${ENDPOINTS.health}`, {
          timeout: CONFIG.TIMEOUT
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status === 200) {
          results.health.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.health.failed++;
          results.health.errors.push(`Health check failed: ${response.status}`);
        }
      } catch (error) {
        results.health.failed++;
        results.health.errors.push(`Health error: ${error.message}`);
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// Authentication stress test
async function testAuthEndpoint() {
  console.log('🔐 Testing Authentication Endpoint...');
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    const userIndex = i % TEST_CREDENTIALS.length;
    const credentials = TEST_CREDENTIALS[userIndex];
    
    setInterval(async () => {
      try {
        const startTime = Date.now();
        const response = await axios.post(`${CONFIG.BACKEND_URL}${ENDPOINTS.auth}`, credentials, {
          timeout: CONFIG.TIMEOUT,
          headers: { 'Content-Type': 'application/json' }
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.auth.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.auth.failed++;
          results.auth.errors.push(`Auth failed: ${response.status}`);
        }
      } catch (error) {
        results.auth.failed++;
        results.auth.errors.push(`Auth error: ${error.message}`);
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// API endpoints stress test
async function testApiEndpoints() {
  console.log('🔧 Testing API Endpoints...');
  
  const apiEndpoints = [ENDPOINTS.test, ENDPOINTS.users, ENDPOINTS.messages, ENDPOINTS.students, ENDPOINTS.classes];
  
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i++) {
    setInterval(async () => {
      try {
        const endpoint = apiEndpoints[randomInt(0, apiEndpoints.length - 1)];
        const startTime = Date.now();
        const response = await axios.get(`${CONFIG.BACKEND_URL}${endpoint}`, {
          timeout: CONFIG.TIMEOUT
        });
        const responseTime = Date.now() - startTime;
        
        if (response.status >= 200 && response.status < 500) {
          results.api.success++;
          updatePerformanceMetrics(responseTime);
        } else {
          results.api.failed++;
          results.api.errors.push(`API ${endpoint} failed: ${response.status}`);
        }
      } catch (error) {
        results.api.failed++;
        results.api.errors.push(`API error: ${error.message}`);
      }
    }, 1000 / CONFIG.REQUESTS_PER_SECOND);
  }
}

// System health check
async function checkBackendHealth() {
  console.log('🏥 Checking Backend Health...');
  
  try {
    const response = await axios.get(`${CONFIG.BACKEND_URL}${ENDPOINTS.health}`);
    if (response.status === 200) {
      console.log('✅ Backend is healthy');
      console.log(`📊 Response: ${JSON.stringify(response.data)}`);
      return true;
    } else {
      console.log(`❌ Backend health check failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Backend health check failed: ${error.message}`);
    return false;
  }
}

// Generate comprehensive report
function generateReport() {
  const totalTime = Date.now() - results.startTime;
  const totalRequests = results.health.success + results.health.failed + 
                       results.auth.success + results.auth.failed +
                       results.api.success + results.api.failed;
  
  const report = {
    timestamp: new Date().toISOString(),
    duration: `${Math.round(totalTime / 1000)}s`,
    totalRequests,
    requestsPerSecond: Math.round(totalRequests / (totalTime / 1000)),
    successRate: Math.round(((results.health.success + results.auth.success + results.api.success) / totalRequests) * 100),
    results,
    performance: {
      avgResponseTime: Math.round(results.performance.avgResponseTime),
      minResponseTime: Math.round(results.performance.minResponseTime),
      maxResponseTime: Math.round(results.performance.maxResponseTime),
      p95ResponseTime: Math.round(calculatePercentile(results.performance.responseTimes, 95)),
      p99ResponseTime: Math.round(calculatePercentile(results.performance.responseTimes, 99))
    },
    recommendations: []
  };
  
  // Generate recommendations
  if (results.health.failed > results.health.success * 0.05) {
    report.recommendations.push('Health endpoint has high failure rate - check server stability');
  }
  
  if (results.auth.failed > results.auth.success * 0.1) {
    report.recommendations.push('Authentication endpoint needs optimization - high failure rate');
  }
  
  if (results.api.failed > results.api.success * 0.1) {
    report.recommendations.push('API endpoints need optimization - high failure rate');
  }
  
  if (results.performance.avgResponseTime > 1000) {
    report.recommendations.push('Average response time is too slow - consider optimization');
  }
  
  if (results.performance.maxResponseTime > 5000) {
    report.recommendations.push('Maximum response time is too high - investigate bottlenecks');
  }
  
  if (report.successRate < 95) {
    report.recommendations.push('Overall success rate is below 95% - system needs attention');
  }
  
  return report;
}

// Calculate percentile
function calculatePercentile(arr, percentile) {
  if (arr.length === 0) return 0;
  const sorted = arr.slice().sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index] || 0;
}

// Main stress test function
async function runBackendStressTest() {
  console.log('🚀 Starting Backend Stress Test...');
  console.log(`⏱️  Duration: ${CONFIG.TEST_DURATION / 1000}s`);
  console.log(`👥 Concurrent Users: ${CONFIG.CONCURRENT_USERS}`);
  console.log(`📡 Requests/Second: ${CONFIG.REQUESTS_PER_SECOND}`);
  console.log('');

  // Initial health check
  const isHealthy = await checkBackendHealth();
  if (!isHealthy) {
    console.log('❌ Backend is not healthy. Aborting stress test.');
    process.exit(1);
  }
  console.log('');

  // Start stress tests
  testHealthEndpoint();
  testAuthEndpoint();
  testApiEndpoints();

  // Progress reporting
  const progressInterval = setInterval(() => {
    const elapsed = Date.now() - results.startTime;
    const progress = Math.round((elapsed / CONFIG.TEST_DURATION) * 100);
    const totalRequests = results.health.success + results.health.failed +
                         results.auth.success + results.auth.failed +
                         results.api.success + results.api.failed;
    console.log(`📊 Progress: ${progress}% | Requests: ${totalRequests} | Avg Response: ${Math.round(results.performance.avgResponseTime)}ms`);
  }, 30000);

  // Run for specified duration
  setTimeout(() => {
    clearInterval(progressInterval);
    console.log('\n📊 Generating Backend Stress Test Report...');
    const report = generateReport();

    console.log('\n' + '='.repeat(70));
    console.log('📈 BACKEND STRESS TEST RESULTS');
    console.log('='.repeat(70));
    console.log(`⏱️  Duration: ${report.duration}`);
    console.log(`📡 Total Requests: ${report.totalRequests}`);
    console.log(`⚡ Requests/Second: ${report.requestsPerSecond}`);
    console.log(`✅ Success Rate: ${report.successRate}%`);
    console.log('');

    console.log('🏥 Health Endpoint Results:');
    console.log(`   ✅ Success: ${results.health.success}`);
    console.log(`   ❌ Failed: ${results.health.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.health.success / (results.health.success + results.health.failed)) * 100)}%`);
    console.log('');

    console.log('🔐 Authentication Results:');
    console.log(`   ✅ Success: ${results.auth.success}`);
    console.log(`   ❌ Failed: ${results.auth.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.auth.success / (results.auth.success + results.auth.failed)) * 100)}%`);
    console.log('');

    console.log('🔧 API Endpoints Results:');
    console.log(`   ✅ Success: ${results.api.success}`);
    console.log(`   ❌ Failed: ${results.api.failed}`);
    console.log(`   📊 Success Rate: ${Math.round((results.api.success / (results.api.success + results.api.failed)) * 100)}%`);
    console.log('');

    console.log('⚡ Performance Metrics:');
    console.log(`   📊 Avg Response Time: ${report.performance.avgResponseTime}ms`);
    console.log(`   🐌 Min Response Time: ${report.performance.minResponseTime}ms`);
    console.log(`   🚀 Max Response Time: ${report.performance.maxResponseTime}ms`);
    console.log(`   📈 95th Percentile: ${report.performance.p95ResponseTime}ms`);
    console.log(`   📈 99th Percentile: ${report.performance.p99ResponseTime}ms`);
    console.log('');

    if (report.recommendations.length > 0) {
      console.log('⚠️  Recommendations:');
      report.recommendations.forEach(rec => console.log(`   • ${rec}`));
    } else {
      console.log('✅ Backend is performing excellently under stress!');
    }

    console.log('\n' + '='.repeat(70));

    // Save report to file
    fs.writeFileSync('backend-stress-test-report.json', JSON.stringify(report, null, 2));
    console.log('📄 Report saved to: backend-stress-test-report.json');

    process.exit(0);
  }, CONFIG.TEST_DURATION);
}

// Run the backend stress test
runBackendStressTest().catch(console.error);
