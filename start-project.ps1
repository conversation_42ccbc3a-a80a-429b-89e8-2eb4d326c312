# FamEduConnect Project Startup Script
# This script will start the project in development mode

param(
    [Parameter(Mandatory=$false)]
    [switch]$Enterprise,
    
    [Parameter(Mandatory=$false)]
    [switch]$Local
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $Color
}

# Success function
function Write-Success {
    param([string]$Message)
    Write-Log $Message "SUCCESS" $Green
}

# Warning function
function Write-Warning {
    param([string]$Message)
    Write-Log $Message "WARNING" $Yellow
}

# Information function
function Write-Info {
    param([string]$Message)
    Write-Log $Message "INFO" $Blue
}

# Error function
function Write-Error {
    param([string]$Message)
    Write-Log $Message "ERROR" $Red
}

# Check if we're in the right directory
function Test-ProjectDirectory {
    if (-not (Test-Path "start-simple.bat")) {
        Write-Error "Please run this script from the FamEduConnect project root directory"
        Write-Info "Expected files: start-simple.bat, backend/, frontend/, mobile/"
        exit 1
    }
    Write-Success "Project directory confirmed"
}

# Check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-Success "Node.js found: $nodeVersion"
    } catch {
        Write-Error "Node.js is not installed. Please install Node.js from https://nodejs.org/"
        exit 1
    }
    
    # Check npm
    try {
        $npmVersion = npm --version
        Write-Success "npm found: $npmVersion"
    } catch {
        Write-Error "npm is not installed"
        exit 1
    }
    
    # Check if backend dependencies are installed
    if (-not (Test-Path "backend/node_modules")) {
        Write-Warning "Backend dependencies not found. Installing..."
        Set-Location backend
        npm install
        Set-Location ..
    }
    
    # Check if frontend dependencies are installed
    if (-not (Test-Path "frontend/node_modules")) {
        Write-Warning "Frontend dependencies not found. Installing..."
        Set-Location frontend
        npm install
        Set-Location ..
    }
    
    Write-Success "All prerequisites are satisfied"
}

# Start local development environment
function Start-LocalDevelopment {
    Write-Info "Starting local development environment..."
    
    # Kill any existing processes on ports 3000 and 3001
    Write-Info "Stopping any existing processes..."
    taskkill /f /im node.exe >nul 2>&1
    Start-Sleep -Seconds 2
    
    # Start backend
    Write-Info "Starting backend server..."
    Start-Process -FilePath "cmd" -ArgumentList "/k", "cd backend && node simple-server.js" -WindowStyle Minimized
    
    # Wait a moment for backend to start
    Start-Sleep -Seconds 3
    
    # Start frontend
    Write-Info "Starting frontend server..."
    Start-Process -FilePath "cmd" -ArgumentList "/k", "cd frontend && npm start" -WindowStyle Minimized
    
    # Wait for frontend to start
    Start-Sleep -Seconds 8
    
    # Open browser
    Write-Info "Opening application in browser..."
    Start-Process "http://localhost:3000"
    
    Write-Success "Local development environment started!"
    Write-Info "Backend API: http://localhost:3001"
    Write-Info "Frontend: http://localhost:3000"
    Write-Info "Press Ctrl+C to stop all services"
}

# Start enterprise validation
function Start-EnterpriseValidation {
    Write-Info "Starting enterprise validation..."
    
    if (Test-Path "scripts/validate-enterprise.ps1") {
        & ".\scripts\validate-enterprise.ps1"
    } else {
        Write-Error "Enterprise validation script not found"
    }
}

# Main execution
function Start-Project {
    Write-Info "=== FamEduConnect Project Startup ==="
    
    Test-ProjectDirectory
    Test-Prerequisites
    
    if ($Enterprise) {
        Write-Info "Enterprise mode selected"
        Start-EnterpriseValidation
    } elseif ($Local) {
        Write-Info "Local development mode selected"
        Start-LocalDevelopment
    } else {
        Write-Info "Starting in local development mode (default)"
        Write-Info "Use -Enterprise flag for enterprise validation"
        Write-Info "Use -Local flag for explicit local development"
        Start-LocalDevelopment
    }
}

# Execute startup
Start-Project 