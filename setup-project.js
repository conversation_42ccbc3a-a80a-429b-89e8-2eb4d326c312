const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 FamEduConnect Project Setup Automation');
console.log('=======================================');

// Create necessary directories if they don't exist
const ensureDirectoryExists = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ Created directory: ${dirPath}`);
  }
};

// Directories to ensure exist
[
  'mobile/assets',
  'mobile/src/components',
  'mobile/src/screens',
  'mobile/src/navigation',
  'mobile/src/services',
  'mobile/src/store',
  'mobile/src/store/slices',
  'admin/src/components',
  'admin/src/pages',
  'admin/src/services',
  'admin/src/store',
  'admin/src/store/slices',
  'backend/tests',
  'scripts',
  'docs'
].forEach(ensureDirectoryExists);

// Fix backend server.js to work without database for testing
console.log('🔧 Fixing backend server.js for testing...');
const serverJsPath = path.join(__dirname, 'backend', 'server.js');
if (fs.existsSync(serverJsPath)) {
  let serverContent = fs.readFileSync(serverJsPath, 'utf8');
  
  // Comment out database connection
  serverContent = serverContent.replace(
    /const\s*{\s*sequelize\s*}\s*=\s*require\(['"]\.\/models['"]\);/,
    '// const { sequelize } = require(\'./models\'); // Temporarily disabled'
  );
  
  // Comment out routes that depend on database
  serverContent = serverContent.replace(
    /\/\/ Routes([\s\S]*?)app\.use\(['"]\//g,
    '// Routes (temporarily disabled for testing)\n// app.use(\'//'
  );
  
  // Add test route
  if (!serverContent.includes('/api/test')) {
    serverContent = serverContent.replace(
      /\/\/ Routes[\s\S]*?;/m,
      `// Routes (temporarily disabled for testing)
// app.use('/api/auth', require('./routes/auth'));
// app.use('/api/messages', require('./routes/messages'));
// app.use('/api/users', require('./routes/users'));
// app.use('/api/students', require('./routes/students'));
// app.use('/api/classes', require('./routes/classes'));
// app.use('/api/webrtc', require('./routes/webrtc'));
// app.use('/api/admin', require('./routes/admin'));

// Test route
app.get('/api/test', (req, res) => {
  res.json({ message: 'Backend is working!', timestamp: new Date().toISOString() });
});`
    );
  }
  
  // Comment out socket handler
  serverContent = serverContent.replace(
    /\/\/ Socket\.IO[\s\S]*?socketHandler\(io\);/,
    `// Socket.IO for real-time communication (temporarily disabled)
// const socketHandler = require('./socket/socketHandler');
// socketHandler(io);

// Basic socket connection for testing
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});`
  );
  
  // Modify server start to skip database
  serverContent = serverContent.replace(
    /sequelize\.sync[\s\S]*?}\);/,
    `// Start server (database temporarily disabled)
server.listen(PORT, () => {
  console.log(\`FamEduConnect server running on port \${PORT}\`);
  console.log('Database connection skipped for testing');
});`
  );
  
  fs.writeFileSync(serverJsPath, serverContent);
  console.log('✅ Fixed backend server.js');
}

// Create environment files
console.log('📝 Creating environment files...');

// Root .env
fs.writeFileSync(path.join(__dirname, '.env'), `# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=fameduconnect
DB_USER=postgres
DB_PASSWORD=password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=8080
NODE_ENV=development

# Socket.IO Configuration
SOCKET_PORT=8081

# WebRTC Configuration
TURN_SERVER_URL=turn:your-turn-server.com:3478
TURN_USERNAME=your_turn_username
TURN_CREDENTIAL=your_turn_password

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Frontend URL
FRONTEND_URL=http://localhost:3000
ADMIN_URL=http://localhost:3001`);

// Frontend .env
fs.writeFileSync(path.join(__dirname, 'frontend', '.env'), `# API Configuration
REACT_APP_API_URL=http://localhost:8080
REACT_APP_SOCKET_URL=http://localhost:8080

# App Configuration
REACT_APP_NAME=FamEduConnect
REACT_APP_VERSION=1.0.0

# Feature Flags
REACT_APP_ENABLE_VOICE_MESSAGES=true
REACT_APP_ENABLE_TRANSLATION=true
REACT_APP_ENABLE_VIDEO_CALLS=true

# File Upload
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=image/*,application/pdf,text/*`);

console.log('✅ Created environment files');

// Create startup script
console.log('📝 Creating startup script...');
fs.writeFileSync(path.join(__dirname, 'start-project.js'), `const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting FamEduConnect Project');
console.log('===============================');

// Function to run a command in a specific directory
const runCommand = (command, dir = __dirname) => {
  try {
    console.log(\`Running: \${command} in \${dir}\`);
    execSync(command, { 
      cwd: dir, 
      stdio: 'inherit',
      shell: true
    });
    return true;
  } catch (error) {
    console.error(\`Failed to execute \${command}\`);
    return false;
  }
};

// Start backend
console.log('\\n📡 Starting Backend Server...');
const backendProcess = runCommand('start cmd.exe /k "cd backend && node server.js"');
if (backendProcess) {
  console.log('✅ Backend server started');
} else {
  console.error('❌ Failed to start backend server');
}

// Start frontend
console.log('\\n🖥️ Starting Frontend...');
const frontendProcess = runCommand('start cmd.exe /k "cd frontend && npm start"');
if (frontendProcess) {
  console.log('✅ Frontend started');
} else {
  console.error('❌ Failed to start frontend');
}

// Start admin
console.log('\\n👨‍💼 Starting Admin Dashboard...');
const adminProcess = runCommand('start cmd.exe /k "cd admin && npm start"');
if (adminProcess) {
  console.log('✅ Admin dashboard started');
} else {
  console.error('❌ Failed to start admin dashboard');
}

console.log('\\n🎉 FamEduConnect is now running!');
console.log('- Backend: http://localhost:8080');
console.log('- Frontend: http://localhost:3000');
console.log('- Admin: http://localhost:3001');
console.log('\\nPress Ctrl+C in each terminal window to stop the servers');`);

console.log('✅ Created startup script');

// Create README with instructions
console.log('📝 Creating README with instructions...');
fs.writeFileSync(path.join(__dirname, 'SETUP_INSTRUCTIONS.md'), `# FamEduConnect Setup Instructions

## Quick Start

1. **Install Dependencies**:
   \`\`\`
   npm install
   cd backend && npm install && cd ..
   cd frontend && npm install --legacy-peer-deps && cd ..
   cd admin && npm install --legacy-peer-deps && cd ..
   cd mobile && npm install --legacy-peer-deps && cd ..
   \`\`\`

2. **Start the Project**:
   \`\`\`
   node start-project.js
   \`\`\`

## Manual Setup

If you prefer to set up each component individually:

### Backend
\`\`\`
cd backend
npm install
node server.js
\`\`\`

### Frontend
\`\`\`
cd frontend
npm install --legacy-peer-deps
npm start
\`\`\`

### Admin Dashboard
\`\`\`
cd admin
npm install --legacy-peer-deps
npm start
\`\`\`

### Mobile App
\`\`\`
cd mobile
npm install --legacy-peer-deps
npm start
\`\`\`

## Database Setup

For full functionality, you'll need PostgreSQL:

1. Install PostgreSQL
2. Create a database named 'fameduconnect'
3. Update the .env file with your database credentials

## Troubleshooting

- **Port conflicts**: If you see EADDRINUSE errors, change the port in the respective .env file
- **Database connection issues**: Make sure PostgreSQL is running and accessible
- **Node version**: This project requires Node.js 18+

## Production Deployment

See the docs/DEPLOYMENT.md file for production deployment instructions.`);

console.log('✅ Created setup instructions');

console.log('\n🎉 Setup automation complete!');
console.log('To start the project:');
console.log('1. Run: node start-project.js');
console.log('2. Or follow the instructions in SETUP_INSTRUCTIONS.md');