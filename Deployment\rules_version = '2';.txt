rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions for security
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }
    
    function isTeacher() {
      return isAuthenticated() && getUserRole() == 'teacher';
    }
    
    function isParent() {
      return isAuthenticated() && getUserRole() == 'parent';
    }
    
    function belongsToSameSchool(schoolId) {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.schoolId == schoolId;
    }
    
    function isValidEmail(email) {
      return email.matches('.*@.*\\..*');
    }
    
    function isValidRole(role) {
      return role in ['parent', 'teacher', 'admin'];
    }
    
    // Users collection - strict access control
    match /users/{userId} {
      allow read: if isAuthenticated() && (isOwner(userId) || isAdmin() || isTeacher());
      allow write: if isAuthenticated() && isOwner(userId) && 
        isValidEmail(request.resource.data.email) && 
        isValidRole(request.resource.data.role);
      allow create: if isAuthenticated() && isOwner(userId) && 
        isValidEmail(request.resource.data.email) && 
        isValidRole(request.resource.data.role);
    }
    
    // Schools collection - admin and teacher access
    match /schools/{schoolId} {
      allow read: if isAuthenticated() && belongsToSameSchool(schoolId);
      allow write: if isAuthenticated() && isAdmin();
    }
    
    // Classes collection - role-based access
    match /classes/{classId} {
      allow read: if isAuthenticated() && (isAdmin() || isTeacher() || isParent());
      allow write: if isAuthenticated() && (isAdmin() || isTeacher());
    }
    
    // Students collection - strict parent/teacher access
    match /students/{studentId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || isTeacher() || 
         (isParent() && request.auth.uid in resource.data.parentIds));
      allow write: if isAuthenticated() && (isAdmin() || isTeacher());
    }
    
    // Messages collection - sender/receiver only with content validation
    match /messages/{messageId} {
      allow read: if isAuthenticated() && 
        (request.auth.uid == resource.data.senderId || 
         request.auth.uid == resource.data.receiverId);
      allow create: if isAuthenticated() && 
        request.auth.uid == request.resource.data.senderId &&
        request.resource.data.content.size() <= 1000 &&
        request.resource.data.type in ['text', 'image', 'file'];
      allow update: if isAuthenticated() && 
        request.auth.uid == resource.data.senderId;
    }
    
    // Grades collection - student's parents and teachers
    match /grades/{gradeId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || isTeacher() || 
         (isParent() && request.auth.uid in get(/databases/$(database)/documents/students/$(resource.data.studentId)).data.parentIds));
      allow write: if isAuthenticated() && (isAdmin() || isTeacher()) &&
        request.resource.data.score >= 0 && request.resource.data.score <= 100;
    }
    
    // Attendance collection - similar to grades
    match /attendance/{attendanceId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || isTeacher() || 
         (isParent() && request.auth.uid in get(/databases/$(database)/documents/students/$(resource.data.studentId)).data.parentIds));
      allow write: if isAuthenticated() && (isAdmin() || isTeacher()) &&
        request.resource.data.status in ['present', 'absent', 'late', 'excused'];
    }
    
    // Events collection - school community access
    match /events/{eventId} {
      allow read: if isAuthenticated() && belongsToSameSchool(resource.data.schoolId);
      allow write: if isAuthenticated() && (isAdmin() || isTeacher());
    }
    
    // Video calls collection - participants only
    match /videoCalls/{callId} {
      allow read, write: if isAuthenticated() && 
        (request.auth.uid == resource.data.initiatorId || 
         request.auth.uid in resource.data.participantIds);
    }
    
    // Notifications collection - user's own notifications
    match /notifications/{notificationId} {
      allow read, write: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
    }
  }
}