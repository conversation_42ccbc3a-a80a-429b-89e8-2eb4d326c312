@echo off
echo ========================================
echo 🚀 FamEduConnect - FINAL PERFECT START
echo ========================================
echo.

echo [1/7] Killing any existing processes...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
timeout /t 3 /nobreak >nul

echo [2/7] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies.
    pause
    exit /b 1
)

echo [3/7] Starting backend FIRST...
echo Starting backend on port 3002...
start "Backend" cmd /k "cd backend && node server.js"

echo [4/7] Waiting for backend to be ready...
timeout /t 25 /nobreak >nul

echo [5/7] Testing backend connection...
curl -s http://localhost:3002/api/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Backend not responding, waiting longer...
    timeout /t 20 /nobreak >nul
    curl -s http://localhost:3002/api/health >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Backend still not responding. Please check backend manually.
        echo Testing with curl: curl http://localhost:3002/api/health
        pause
        exit /b 1
    )
)

echo ✅ Backend is ready!

echo [6/7] Starting frontend...
echo Starting frontend on port 3000...
start "Frontend" cmd /k "cd frontend && npm start"

echo [7/7] Starting admin...
echo Starting admin on port 3001...
start "Admin" cmd /k "cd admin && npm start"

echo.
echo ========================================
echo 🎉 ALL SERVERS STARTING!
echo ========================================
echo.
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend:  http://localhost:3002/api
echo 👨‍💼 Admin:    http://localhost:3001
echo.
echo 📋 Test Credentials:
echo    Admin:    <EMAIL> / password123
echo    Teacher:  <EMAIL> / password123
echo    Parent:   <EMAIL> / password123
echo    Student:  <EMAIL> / password123
echo.
echo ⏳ Please wait 30-60 seconds for all servers to fully start...
echo.
echo 🔍 Connection Test:
echo    Backend Health: http://localhost:3002/api/health
echo    Backend Test:   http://localhost:3002/api/test
echo.
echo 🚀 If you get connection errors:
echo    1. Wait 60 seconds for all servers to start
echo    2. Try refreshing the browser
echo    3. Check the connection indicator in the top-right corner
echo.
pause 