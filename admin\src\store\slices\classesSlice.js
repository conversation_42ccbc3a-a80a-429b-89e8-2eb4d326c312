import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

export const fetchClasses = createAsyncThunk('classes/fetchClasses', async () => {
  const response = await api.get('/admin/classes');
  return response.data;
});

const classesSlice = createSlice({
  name: 'classes',
  initialState: {
    classes: [],
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchClasses.fulfilled, (state, action) => {
      state.classes = action.payload;
      state.isLoading = false;
    });
  },
});

export default classesSlice.reducer;