/**
 * Guardian AI Server - Backend Monitoring Implementation
 * FamEduConnect Production Monitoring System
 * 
 * @version 1.0.0
 * @copyright 2025 Joud Holdings, BidayaX, and Divitiae Good Doers Inc.
 */

const os = require('os');
const cluster = require('cluster');
const { performance } = require('perf_hooks');

class GuardianAIServer {
  /**
   * Initialize Guardian AI server
   * @param {Object} config - Configuration options
   * @param {string} config.apiKey - Guardian AI API key
   * @param {string} config.appId - Application identifier
   * @param {string} config.environment - Environment (production, staging, development)
   * @param {string} config.version - Application version
   */
  constructor(config) {
    this.config = {
      apiKey: config.apiKey,
      appId: config.appId || 'fameduconnect-backend',
      environment: config.environment || 'production',
      version: config.version || '1.0.0',
      endpoint: config.endpoint || 'https://api.guardian-ai.fameduconnect.xyz/v1',
      sampleRate: config.sampleRate || 1.0, // 100% by default
      logLevel: config.logLevel || 'error',
      maxBatchSize: config.maxBatchSize || 50,
      maxQueueSize: config.maxQueueSize || 1000,
      flushInterval: config.flushInterval || 10000, // 10 seconds
      enableMetrics: config.enableMetrics !== false,
      enableTracing: config.enableTracing !== false,
      enableAnomalyDetection: config.enableAnomalyDetection !== false,
      enableSecurityMonitoring: config.enableSecurityMonitoring !== false,
    };

    this.queue = [];
    this.flushTimer = null;
    this.initialized = false;
    this.startTime = Date.now();
    
    // System metrics
    this.systemMetrics = {
      cpu: {
        usage: 0,
        cores: os.cpus().length,
      },
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        rss: process.memoryUsage().rss,
        heapTotal: process.memoryUsage().heapTotal,
        heapUsed: process.memoryUsage().heapUsed,
      },
      uptime: process.uptime(),
      processId: process.pid,
      isMainProcess: !cluster.isWorker,
      hostname: os.hostname(),
      platform: process.platform,
      nodeVersion: process.version,
    };
    
    // API metrics
    this.apiMetrics = {
      requestCount: 0,
      errorCount: 0,
      totalResponseTime: 0,
      averageResponseTime: 0,
      requestsByEndpoint: {},
      statusCodes: {},
    };
    
    // Database metrics
    this.dbMetrics = {
      queryCount: 0,
      errorCount: 0,
      totalQueryTime: 0,
      averageQueryTime: 0,
      queriesByType: {},
      queriesByCollection: {},
    };
  }

  /**
   * Initialize the monitoring system
   */
  initialize() {
    if (this.initialized) {
      this.log('warn', 'Guardian AI: Already initialized');
      return;
    }

    // Check if we should sample this instance
    if (Math.random() > this.config.sampleRate) {
      this.log('info', 'Guardian AI: Instance not sampled');
      return;
    }

    this._setupProcessMonitoring();
    this._startMetricsCollection();
    this._startFlushTimer();

    // Send initialization event
    this.trackEvent('guardian_ai_initialized', {
      timestamp: new Date().toISOString(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      hostname: os.hostname(),
      cpuCores: os.cpus().length,
      totalMemory: os.totalmem(),
      processId: process.pid,
      isMainProcess: !cluster.isWorker,
    });

    this.initialized = true;
    this.log('info', `Guardian AI: Initialized (${this.config.environment})`);
  }

  /**
   * Create Express middleware for API monitoring
   * @returns {Function} Express middleware
   */
  monitorAPI() {
    return (req, res, next) => {
      if (!this.initialized) {
        return next();
      }

      const startTime = performance.now();
      const requestId = this._generateId();
      
      // Store request data
      req.guardianAI = {
        requestId,
        startTime,
        path: req.path,
        method: req.method,
        userAgent: req.get('user-agent'),
        ip: req.ip || req.connection.remoteAddress,
        userId: req.user?.id,
      };

      // Track request start
      this.trackEvent('api_request_start', {
        requestId,
        path: req.path,
        method: req.method,
        userAgent: req.get('user-agent'),
        ip: req.ip || req.connection.remoteAddress,
        userId: req.user?.id,
      });

      // Capture response
      const originalEnd = res.end;
      res.end = function(...args) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // Update API metrics
        this.apiMetrics.requestCount++;
        this.apiMetrics.totalResponseTime += duration;
        this.apiMetrics.averageResponseTime = this.apiMetrics.totalResponseTime / this.apiMetrics.requestCount;
        
        if (res.statusCode >= 400) {
          this.apiMetrics.errorCount++;
        }
        
        // Track by endpoint
        const endpoint = `${req.method} ${req.route?.path || req.path}`;
        if (!this.apiMetrics.requestsByEndpoint[endpoint]) {
          this.apiMetrics.requestsByEndpoint[endpoint] = {
            count: 0,
            errors: 0,
            totalTime: 0,
            averageTime: 0,
          };
        }
        
        this.apiMetrics.requestsByEndpoint[endpoint].count++;
        this.apiMetrics.requestsByEndpoint[endpoint].totalTime += duration;
        this.apiMetrics.requestsByEndpoint[endpoint].averageTime = 
          this.apiMetrics.requestsByEndpoint[endpoint].totalTime / this.apiMetrics.requestsByEndpoint[endpoint].count;
        
        if (res.statusCode >= 400) {
          this.apiMetrics.requestsByEndpoint[endpoint].errors++;
        }
        
        // Track by status code
        const statusCode = res.statusCode.toString();
        if (!this.apiMetrics.statusCodes[statusCode]) {
          this.apiMetrics.statusCodes[statusCode] = 0;
        }
        this.apiMetrics.statusCodes[statusCode]++;

        // Track request completion
        this.trackEvent('api_request_complete', {
          requestId,
          path: req.path,
          method: req.method,
          statusCode: res.statusCode,
          duration,
          userId: req.user?.id,
        });

        // Call original end
        return originalEnd.apply(res, args);
      }.bind(this);

      next();
    };
  }

  /**
   * Create database monitoring middleware
   * @returns {Object} Database monitoring methods
   */
  monitorDatabase() {
    return {
      /**
       * Monitor database query
       * @param {Function} originalFn - Original database function
       * @param {Object} options - Query options
       * @param {string} options.type - Query type (find, insert, update, delete, aggregate)
       * @param {string} options.collection - Collection/table name
       * @returns {Function} Wrapped function
       */
      wrapQuery: (originalFn, options) => {
        return async (...args) => {
          if (!this.initialized) {
            return originalFn(...args);
          }

          const startTime = performance.now();
          const queryId = this._generateId();
          
          try {
            // Execute original query
            const result = await originalFn(...args);
            
            // Calculate duration
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            // Update DB metrics
            this.dbMetrics.queryCount++;
            this.dbMetrics.totalQueryTime += duration;
            this.dbMetrics.averageQueryTime = this.dbMetrics.totalQueryTime / this.dbMetrics.queryCount;
            
            // Track by query type
            if (!this.dbMetrics.queriesByType[options.type]) {
              this.dbMetrics.queriesByType[options.type] = {
                count: 0,
                errors: 0,
                totalTime: 0,
                averageTime: 0,
              };
            }
            
            this.dbMetrics.queriesByType[options.type].count++;
            this.dbMetrics.queriesByType[options.type].totalTime += duration;
            this.dbMetrics.queriesByType[options.type].averageTime = 
              this.dbMetrics.queriesByType[options.type].totalTime / this.dbMetrics.queriesByType[options.type].count;
            
            // Track by collection
            if (!this.dbMetrics.queriesByCollection[options.collection]) {
              this.dbMetrics.queriesByCollection[options.collection] = {
                count: 0,
                errors: 0,
                totalTime: 0,
                averageTime: 0,
              };
            }
            
            this.dbMetrics.queriesByCollection[options.collection].count++;
            this.dbMetrics.queriesByCollection[options.collection].totalTime += duration;
            this.dbMetrics.queriesByCollection[options.collection].averageTime = 
              this.dbMetrics.queriesByCollection[options.collection].totalTime / 
              this.dbMetrics.queriesByCollection[options.collection].count;
            
            // Track query
            this.trackEvent('db_query', {
              queryId,
              type: options.type,
              collection: options.collection,
              duration,
              success: true,
            });
            
            return result;
          } catch (error) {
            // Calculate duration
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            // Update error metrics
            this.dbMetrics.errorCount++;
            
            if (this.dbMetrics.queriesByType[options.type]) {
              this.dbMetrics.queriesByType[options.type].errors++;
            }
            
            if (this.dbMetrics.queriesByCollection[options.collection]) {
              this.dbMetrics.queriesByCollection[options.collection].errors++;
            }
            
            // Track query error
            this.trackEvent('db_query_error', {
              queryId,
              type: options.type,
              collection: options.collection,
              duration,
              error: error.message,
              stack: error.stack,
            });
            
            throw error;
          }
        };
      },
      
      /**
       * Monitor MongoDB operations
       * @param {Object} db - MongoDB database instance
       * @returns {Object} Monitored database instance
       */
      mongodb: (db) => {
        if (!db || !this.initialized) {
          return db;
        }
        
        // Wrap collection methods
        const originalCollection = db.collection;
        db.collection = function(name) {
          const collection = originalCollection.call(this, name);
          
          // Wrap common methods
          const methods = ['find', 'findOne', 'insertOne', 'insertMany', 'updateOne', 
                          'updateMany', 'deleteOne', 'deleteMany', 'aggregate'];
          
          methods.forEach(method => {
            const originalMethod = collection[method];
            collection[method] = function(...args) {
              const startTime = performance.now();
              const queryId = this._generateId();
              
              try {
                const result = originalMethod.apply(this, args);
                
                // Handle promises
                if (result && typeof result.then === 'function') {
                  return result.then(
                    (data) => {
                      const endTime = performance.now();
                      const duration = endTime - startTime;
                      
                      this.trackEvent('db_query', {
                        queryId,
                        type: method,
                        collection: name,
                        duration,
                        success: true,
                      });
                      
                      return data;
                    },
                    (error) => {
                      const endTime = performance.now();
                      const duration = endTime - startTime;
                      
                      this.trackEvent('db_query_error', {
                        queryId,
                        type: method,
                        collection: name,
                        duration,
                        error: error.message,
                        stack: error.stack,
                      });
                      
                      throw error;
                    }
                  );
                }
                
                // Handle non-promises
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                this.trackEvent('db_query', {
                  queryId,
                  type: method,
                  collection: name,
                  duration,
                  success: true,
                });
                
                return result;
              } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                this.trackEvent('db_query_error', {
                  queryId,
                  type: method,
                  collection: name,
                  duration,
                  error: error.message,
                  stack: error.stack,
                });
                
                throw error;
              }
            }.bind(this);
          });
          
          return collection;
        }.bind(this);
        
        return db;
      },
      
      /**
       * Monitor Sequelize operations
       * @param {Object} sequelize - Sequelize instance
       * @returns {Object} Monitored Sequelize instance
       */
      sequelize: (sequelize) => {
        if (!sequelize || !this.initialized) {
          return sequelize;
        }
        
        // Add query hook
        sequelize.addHook('beforeQuery', (options) => {
          options.guardianAI = {
            startTime: performance.now(),
            queryId: this._generateId(),
          };
        });
        
        sequelize.addHook('afterQuery', (options, query) => {
          if (options.guardianAI) {
            const endTime = performance.now();
            const duration = endTime - options.guardianAI.startTime;
            
            this.trackEvent('db_query', {
              queryId: options.guardianAI.queryId,
              type: query.type,
              model: options.model?.name || 'unknown',
              duration,
              success: true,
            });
          }
        });
        
        sequelize.addHook('error', (error, options) => {
          if (options.guardianAI) {
            const endTime = performance.now();
            const duration = endTime - options.guardianAI.startTime;
            
            this.trackEvent('db_query_error', {
              queryId: options.guardianAI.queryId,
              type: options.type,
              model: options.model?.name || 'unknown',
              duration,
              error: error.message,
              stack: error.stack,
            });
          }
        });
        
        return sequelize;
      }
    };
  }

  /**
   * Track custom event
   * @param {string} eventName - Event name
   * @param {Object} properties - Event properties
   */
  trackEvent(eventName, properties = {}) {
    if (!this.initialized && eventName !== 'guardian_ai_initialized') {
      this.log('warn', `Guardian AI: Not initialized, event '${eventName}' not tracked`);
      return;
    }

    const event = {
      type: 'event',
      name: eventName,
      appId: this.config.appId,
      environment: this.config.environment,
      version: this.config.version,
      processId: process.pid,
      isMainProcess: !cluster.isWorker,
      timestamp: new Date().toISOString(),
      hostname: os.hostname(),
      properties: properties,
    };

    this._addToQueue(event);
  }

  /**
   * Track error
   * @param {Error|string} error - Error object or message
   * @param {Object} properties - Additional properties
   */
  trackError(error, properties = {}) {
    const errorProperties = {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : null,
      ...properties,
    };

    this.trackEvent('error', errorProperties);
  }

  /**
   * Track custom metric
   * @param {string} metricName - Metric name
   * @param {number} value - Metric value
   * @param {Object} properties - Additional properties
   */
  trackMetric(metricName, value, properties = {}) {
    const metricProperties = {
      metricName,
      value,
      ...properties,
    };

    this.trackEvent('metric', metricProperties);
  }

  /**
   * Manually flush the event queue
   * @returns {Promise} Promise that resolves when flush is complete
   */
  flush() {
    if (this.queue.length === 0) {
      return Promise.resolve();
    }

    const events = [...this.queue];
    this.queue = [];

    return this._sendEvents(events)
      .then(() => {
        this.log('debug', `Guardian AI: Flushed ${events.length} events`);
      })
      .catch(error => {
        this.log('error', 'Guardian AI: Error flushing events', error);
        // Put events back in queue for retry
        this.queue = [...events, ...this.queue].slice(0, this.config.maxQueueSize);
      });
  }

  /**
   * Log message based on configured log level
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {*} data - Additional data
   */
  log(level, message, data) {
    const levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
    };

    if (levels[level] <= levels[this.config.logLevel]) {
      if (data) {
        console[level](message, data);
      } else {
        console[level](message);
      }
    }
  }

  /**
   * Generate a unique ID
   * @private
   * @returns {string} Unique ID
   */
  _generateId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Add event to queue
   * @private
   * @param {Object} event - Event to add
   */
  _addToQueue(event) {
    this.queue.push(event);
    
    // Flush if queue is full
    if (this.queue.length >= this.config.maxBatchSize) {
      this.flush();
    }
  }

  /**
   * Send events to Guardian AI API
   * @private
   * @param {Array} events - Events to send
   * @returns {Promise} Promise that resolves when events are sent
   */
  _sendEvents(events) {
    return new Promise((resolve, reject) => {
      // In a real implementation, this would send data to the Guardian AI API
      // For this implementation, we'll simulate a successful API call
      
      this.log('debug', `Guardian AI: Sending ${events.length} events to ${this.config.endpoint}`);
      
      // Simulate API call
      setTimeout(() => {
        // Simulate 95% success rate
        if (Math.random() < 0.95) {
          resolve();
        } else {
          reject(new Error('Simulated API error'));
        }
      }, 100);
    });
  }

  /**
   * Start the flush timer
   * @private
   */
  _startFlushTimer() {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
    
    // Ensure clean shutdown
    process.on('beforeExit', () => {
      clearInterval(this.flushTimer);
      this.flush();
    });
  }

  /**
   * Set up process monitoring
   * @private
   */
  _setupProcessMonitoring() {
    // Monitor uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.trackError(error, {
        type: 'uncaught_exception',
      });
      
      // Allow normal exception handling to continue
      if (process.listenerCount('uncaughtException') <= 1) {
        console.error('Uncaught Exception:', error);
        process.exit(1);
      }
    });

    // Monitor unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      const error = reason instanceof Error ? reason : new Error(String(reason));
      this.trackError(error, {
        type: 'unhandled_rejection',
      });
    });
    
    // Monitor warnings
    process.on('warning', (warning) => {
      this.trackEvent('process_warning', {
        name: warning.name,
        message: warning.message,
        stack: warning.stack,
      });
    });
  }

  /**
   * Start collecting system metrics
   * @private
   */
  _startMetricsCollection() {
    if (!this.config.enableMetrics) {
      return;
    }

    // Collect metrics every minute
    setInterval(() => {
      this._collectSystemMetrics();
    }, 60000);
    
    // Initial collection
    this._collectSystemMetrics();
  }

  /**
   * Collect system metrics
   * @private
   */
  _collectSystemMetrics() {
    // Update memory metrics
    this.systemMetrics.memory = {
      total: os.totalmem(),
      free: os.freemem(),
      used: os.totalmem() - os.freemem(),
      rss: process.memoryUsage().rss,
      heapTotal: process.memoryUsage().heapTotal,
      heapUsed: process.memoryUsage().heapUsed,
    };
    
    // Update uptime
    this.systemMetrics.uptime = process.uptime();
    
    // Calculate CPU usage
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;
    
    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    }
    
    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const usage = 100 - (idle / total * 100);
    
    this.systemMetrics.cpu.usage = usage;
    
    // Track system metrics
    this.trackEvent('system_metrics', this.systemMetrics);
    
    // Track API metrics
    this.trackEvent('api_metrics', this.apiMetrics);
    
    // Track DB metrics
    this.trackEvent('db_metrics', this.dbMetrics);
  }
}

module.exports = GuardianAIServer;