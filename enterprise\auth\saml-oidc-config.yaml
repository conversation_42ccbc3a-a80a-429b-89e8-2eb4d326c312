apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-config
  namespace: fameduconnect
data:
  # SAML Configuration
  SAML_ENTITY_ID: "https://fameduconnect.xyz/saml/metadata"
  SAML_ACS_URL: "https://fameduconnect.xyz/saml/acs"
  SAML_SLO_URL: "https://fameduconnect.xyz/saml/slo"
  SAML_NAME_ID_FORMAT: "urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"
  SAML_SIGNATURE_ALGORITHM: "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"
  SAML_DIGEST_ALGORITHM: "http://www.w3.org/2001/04/xmlenc#sha256"
  SAML_ASSERTION_CONSUMER_SERVICE_BINDING: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
  SAML_SINGLE_LOGOUT_SERVICE_BINDING: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
  
  # OIDC Configuration
  OIDC_ISSUER: "https://fameduconnect.xyz"
  OIDC_AUTHORIZATION_ENDPOINT: "https://fameduconnect.xyz/oauth/authorize"
  OIDC_TOKEN_ENDPOINT: "https://fameduconnect.xyz/oauth/token"
  OIDC_USERINFO_ENDPOINT: "https://fameduconnect.xyz/oauth/userinfo"
  OIDC_JWKS_URI: "https://fameduconnect.xyz/.well-known/jwks.json"
  OIDC_END_SESSION_ENDPOINT: "https://fameduconnect.xyz/oauth/logout"
  OIDC_INTROSPECTION_ENDPOINT: "https://fameduconnect.xyz/oauth/introspect"
  OIDC_REVOCATION_ENDPOINT: "https://fameduconnect.xyz/oauth/revoke"
  
  # District SSO Configuration
  DISTRICT_SSO_ENABLED: "true"
  DISTRICT_SSO_PROVIDER: "azure-ad" # azure-ad, google-workspace, okta, onelogin
  DISTRICT_SSO_SYNC_ENABLED: "true"
  DISTRICT_SSO_SYNC_INTERVAL: "3600" # seconds
  DISTRICT_SSO_ROLE_MAPPING: |
    {
      "teacher": ["teacher", "educator", "instructor"],
      "student": ["student", "learner", "pupil"],
      "parent": ["parent", "guardian", "caregiver"],
      "admin": ["admin", "administrator", "superuser"],
      "principal": ["principal", "headteacher", "director"]
    }
  DISTRICT_SSO_ATTRIBUTE_MAPPING: |
    {
      "email": "email",
      "firstName": "givenName",
      "lastName": "surname",
      "displayName": "displayName",
      "employeeId": "employeeID",
      "studentId": "studentID",
      "grade": "grade",
      "school": "school",
      "district": "district",
      "department": "department",
      "title": "title",
      "phone": "telephoneNumber"
    }
---
apiVersion: v1
kind: Secret
metadata:
  name: auth-secrets
  namespace: fameduconnect
type: Opaque
data:
  # SAML Secrets
  SAML_PRIVATE_KEY: ${BASE64_ENCODED_SAML_PRIVATE_KEY}
  SAML_CERTIFICATE: ${BASE64_ENCODED_SAML_CERTIFICATE}
  SAML_IDP_CERTIFICATE: ${BASE64_ENCODED_SAML_IDP_CERTIFICATE}
  SAML_IDP_ENTITY_ID: ${BASE64_ENCODED_SAML_IDP_ENTITY_ID}
  SAML_IDP_SSO_URL: ${BASE64_ENCODED_SAML_IDP_SSO_URL}
  SAML_IDP_SLO_URL: ${BASE64_ENCODED_SAML_IDP_SLO_URL}
  
  # OIDC Secrets
  OIDC_CLIENT_ID: ${BASE64_ENCODED_OIDC_CLIENT_ID}
  OIDC_CLIENT_SECRET: ${BASE64_ENCODED_OIDC_CLIENT_SECRET}
  OIDC_SIGNING_KEY: ${BASE64_ENCODED_OIDC_SIGNING_KEY}
  OIDC_ENCRYPTION_KEY: ${BASE64_ENCODED_OIDC_ENCRYPTION_KEY}
  
  # District SSO Secrets
  DISTRICT_SSO_TENANT_ID: ${BASE64_ENCODED_DISTRICT_SSO_TENANT_ID}
  DISTRICT_SSO_CLIENT_ID: ${BASE64_ENCODED_DISTRICT_SSO_CLIENT_ID}
  DISTRICT_SSO_CLIENT_SECRET: ${BASE64_ENCODED_DISTRICT_SSO_CLIENT_SECRET}
  DISTRICT_SSO_GRAPH_API_TOKEN: ${BASE64_ENCODED_DISTRICT_SSO_GRAPH_API_TOKEN}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: fameduconnect
  labels:
    app: auth-service
    component: authentication
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
        component: authentication
      annotations:
        sidecar.istio.io/inject: "true"
    spec:
      serviceAccountName: fameduconnect-auth
      containers:
      - name: auth-service
        image: fameduconnect/auth-service:latest
        ports:
        - containerPort: 8080
          name: http
        envFrom:
        - configMapRef:
            name: auth-config
        - secretRef:
            name: auth-secrets
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8080"
        - name: LOG_LEVEL
          value: "info"
        - name: METRICS_ENABLED
          value: "true"
        - name: HEALTH_CHECK_ENABLED
          value: "true"
        - name: RATE_LIMIT_ENABLED
          value: "true"
        - name: RATE_LIMIT_WINDOW_MS
          value: "900000"
        - name: RATE_LIMIT_MAX_REQUESTS
          value: "1000"
        volumeMounts:
        - name: auth-certs
          mountPath: /etc/auth/certs
          readOnly: true
        - name: auth-config
          mountPath: /etc/auth/config
          readOnly: true
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: auth-certs
        secret:
          secretName: auth-certs
      - name: auth-config
        configMap:
          name: auth-config
      securityContext:
        fsGroup: 1000
        runAsNonRoot: true
        runAsUser: 1000
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: fameduconnect
  labels:
    app: auth-service
    component: authentication
spec:
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: auth-service
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fameduconnect-auth
  namespace: fameduconnect
  labels:
    app: auth-service
    component: authentication
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: fameduconnect-auth-role
  namespace: fameduconnect
rules:
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods", "services"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: fameduconnect-auth-rolebinding
  namespace: fameduconnect
subjects:
- kind: ServiceAccount
  name: fameduconnect-auth
  namespace: fameduconnect
roleRef:
  kind: Role
  name: fameduconnect-auth-role
  apiGroup: rbac.authorization.k8s.io 