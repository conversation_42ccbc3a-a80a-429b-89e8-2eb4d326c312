apiVersion: v1
kind: Secret
metadata:
  name: {{ include "famedu-backend.fullname" . }}-secrets
  labels:
    {{- include "famedu-backend.labels" . | nindent 4 }}
type: Opaque
data:
  DATABASE_URL: {{ .Values.secrets.DATABASE_URL | b64enc | quote }}
  JWT_SECRET: {{ .Values.secrets.JWT_SECRET | b64enc | quote }}
  REDIS_URL: {{ .Values.secrets.REDIS_URL | b64enc | quote }}
  RABBITMQ_URL: {{ .Values.secrets.RABBITMQ_URL | b64enc | quote }}
  SENTRY_DSN: {{ .Values.secrets.SENTRY_DSN | b64enc | quote }} 