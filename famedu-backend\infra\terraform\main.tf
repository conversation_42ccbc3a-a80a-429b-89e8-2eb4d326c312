# Terraform configuration is defined in local-dev.tf for local development
# This file contains AWS-specific resources for production deployment

# AWS provider - uncomment when ready for AWS deployment
# provider "aws" {
#   region = var.aws_region
#   
#   # For development, you can use a mock provider or configure AWS credentials
#   # Uncomment and configure when ready for AWS deployment
#   # access_key = var.aws_access_key
#   # secret_key = var.aws_secret_key
#   
#   default_tags {
#     tags = {
#       Project     = "FamEduConnect"
#       Environment = var.environment
#       ManagedBy   = "Terraform"
#     }
#   }
# }

# Local development provider is defined in local-dev.tf

# AWS Infrastructure Modules - uncomment when ready for AWS deployment
# VPC and Networking
# module "vpc" {
#   source = "./modules/vpc"
#   
#   environment = var.environment
#   vpc_cidr    = var.vpc_cidr
#   azs         = var.availability_zones
# }

# RDS PostgreSQL
# module "rds" {
#   source = "./modules/rds"
#   
#   environment     = var.environment
#   subnet_ids      = module.vpc.private_subnet_ids
#   security_groups = [module.vpc.rds_security_group_id]
#   
#   db_name     = var.database_name
#   db_username = var.database_username
#   db_password = var.database_password
#   db_instance_class = var.database_instance_class
# }

# ElastiCache Redis
# module "redis" {
#   source = "./modules/redis"
#   
#   environment     = var.environment
#   subnet_ids      = module.vpc.private_subnet_ids
#   security_groups = [module.vpc.redis_security_group_id]
#   
#   redis_node_type = var.redis_node_type
# }

# Amazon MQ (RabbitMQ)
# module "rabbitmq" {
#   source = "./modules/rabbitmq"
#   
#   environment     = var.environment
#   subnet_ids      = module.vpc.private_subnet_ids
#   security_groups = [module.vpc.rabbitmq_security_group_id]
#   
#   mq_username = var.rabbitmq_username
#   mq_password = var.rabbitmq_password
#   mq_instance_type = var.rabbitmq_instance_type
# }

# EKS Cluster
# module "eks" {
#   source = "./modules/eks"
#   
#   environment     = var.environment
#   subnet_ids      = module.vpc.private_subnet_ids
#   security_groups = [module.vpc.eks_security_group_id]
#   
#   cluster_name    = "famedu-${var.environment}"
#   cluster_version = "1.28"
#   node_groups = {
#     main = {
#       desired_capacity = var.eks_desired_capacity
#       max_capacity     = var.eks_max_capacity
#       min_capacity     = var.eks_min_capacity
#       instance_types   = var.eks_instance_types
#     }
#   }
# }

# Application Load Balancer
# module "alb" {
#   source = "./modules/alb"
#   
#   environment     = var.environment
#   subnet_ids      = module.vpc.public_subnet_ids
#   security_groups = [module.vpc.alb_security_group_id]
#   
#   domain_name = var.domain_name
#   certificate_arn = var.certificate_arn
# }

# AWS Outputs - uncomment when ready for AWS deployment
# output "rds_endpoint" {
#   description = "RDS PostgreSQL endpoint"
#   value       = module.rds.endpoint
# }

# output "redis_endpoint" {
#   description = "ElastiCache Redis endpoint"
#   value       = module.redis.endpoint
# }

# output "rabbitmq_endpoint" {
#   description = "Amazon MQ RabbitMQ endpoint"
#   value       = module.rabbitmq.endpoint
# }

# output "eks_cluster_name" {
#   description = "EKS cluster name"
#   value       = module.eks.cluster_name
# }

# output "alb_dns_name" {
#   description = "Application Load Balancer DNS name"
#   value       = module.alb.dns_name
# } 