const bcrypt = require('bcryptjs');
const crypto = require('crypto');

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [8, 128]
      }
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 50]
      }
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 50]
      }
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        is: /^[\+]?[1-9][\d]{0,15}$/
      }
    },
    role: {
      type: DataTypes.ENUM('parent', 'teacher', 'admin', 'student'),
      allowNull: false,
      defaultValue: 'parent'
    },
    preferredLanguage: {
      type: DataTypes.STRING,
      defaultValue: 'en',
      validate: {
        isIn: [['en', 'es', 'fr', 'de', 'zh', 'ar', 'hi', 'pt', 'ru', 'ja']]
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    isVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    lastLogin: {
      type: DataTypes.DATE
    },
    loginAttempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    lockUntil: {
      type: DataTypes.DATE
    },
    biometricEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    biometricHash: {
      type: DataTypes.STRING,
      allowNull: true
    },
    encryptionKey: {
      type: DataTypes.STRING,
      allowNull: true
    },
    profilePicture: {
      type: DataTypes.STRING,
      allowNull: true
    },
    timezone: {
      type: DataTypes.STRING,
      defaultValue: 'UTC'
    },
    address: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    emergencyContact: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    notificationSettings: {
      type: DataTypes.JSON,
      defaultValue: {
        email: true,
        sms: true,
        push: true,
        attendance: true,
        performance: true,
        messages: true,
        videoCall: true,
        emergency: true
      }
    },
    accessibilitySettings: {
      type: DataTypes.JSON,
      defaultValue: {
        highContrast: false,
        largeText: false,
        voiceNavigation: false,
        screenReader: false
      }
    },
    twoFactorEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    twoFactorSecret: {
      type: DataTypes.STRING,
      allowNull: true
    },
    hasCompletedOnboarding: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    hooks: {
      beforeCreate: async (user) => {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 12);
        }
        if (!user.encryptionKey) {
          user.encryptionKey = crypto.randomBytes(32).toString('hex');
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 12);
        }
      }
    },
    indexes: [
      { fields: ['email'] },
      { fields: ['role'] },
      { fields: ['isActive'] },
      { fields: ['preferredLanguage'] }
    ]
  });

  User.prototype.validatePassword = async function(password) {
    return bcrypt.compare(password, this.password);
  };

  User.prototype.isLocked = function() {
    return !!(this.lockUntil && this.lockUntil > Date.now());
  };

  User.prototype.incLoginAttempts = async function() {
    if (this.lockUntil && this.lockUntil < Date.now()) {
      return this.update({
        loginAttempts: 1,
        lockUntil: null
      });
    }
    
    const updates = { loginAttempts: this.loginAttempts + 1 };
    
    if (this.loginAttempts + 1 >= 5 && !this.isLocked()) {
      updates.lockUntil = Date.now() + 2 * 60 * 60 * 1000; // 2 hours
    }
    
    return this.update(updates);
  };

  User.prototype.resetLoginAttempts = async function() {
    return this.update({
      loginAttempts: 0,
      lockUntil: null
    });
  };

  User.associate = function(models) {
    User.hasMany(models.Student, { 
      foreignKey: 'parentId',
      as: 'children'
    });
    User.hasMany(models.Class, { 
      foreignKey: 'teacherId',
      as: 'teachingClasses'
    });
    User.hasMany(models.Message, { 
      foreignKey: 'senderId',
      as: 'sentMessages'
    });
    User.hasMany(models.VideoCall, { 
      foreignKey: 'hostId',
      as: 'hostedCalls'
    });
    User.hasMany(models.Notification, { 
      foreignKey: 'userId',
      as: 'notifications'
    });
    User.belongsToMany(models.VideoCall, {
      through: 'VideoCallParticipants',
      foreignKey: 'userId',
      as: 'participatedCalls'
    });
  };

  return User;
};