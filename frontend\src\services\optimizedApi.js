import axios from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3002';
const REQUEST_TIMEOUT = 10000; // 10 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// In-memory cache for API responses
class ApiCache {
  constructor() {
    this.cache = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes
  }

  set(key, data, ttl = this.defaultTTL) {
    const expiry = Date.now() + ttl;
    this.cache.set(key, { data, expiry });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  delete(key) {
    this.cache.delete(key);
  }

  clear() {
    this.cache.clear();
  }

  // Clean expired entries
  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }
}

const apiCache = new ApiCache();

// Cleanup cache every 5 minutes
setInterval(() => {
  apiCache.cleanup();
}, 5 * 60 * 1000);

// Create axios instance with optimized configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: REQUEST_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for authentication and caching
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add request timestamp for performance monitoring
    config.metadata = { startTime: Date.now() };

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and performance monitoring
api.interceptors.response.use(
  (response) => {
    // Calculate request duration
    const duration = Date.now() - response.config.metadata.startTime;
    console.log(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`);

    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      localStorage.removeItem('authToken');
      window.location.href = '/login';
      return Promise.reject(error);
    }

    // Handle network errors with retry logic
    if (!error.response && !originalRequest._retryCount) {
      return retryRequest(originalRequest);
    }

    return Promise.reject(error);
  }
);

// Retry logic for failed requests
async function retryRequest(config, retryCount = 0) {
  if (retryCount >= MAX_RETRIES) {
    throw new Error('Max retries exceeded');
  }

  config._retryCount = retryCount + 1;

  // Exponential backoff
  const delay = RETRY_DELAY * Math.pow(2, retryCount);
  await new Promise(resolve => setTimeout(resolve, delay));

  console.log(`Retrying request (${retryCount + 1}/${MAX_RETRIES}): ${config.url}`);

  try {
    return await api(config);
  } catch (error) {
    return retryRequest(config, retryCount + 1);
  }
}

// Optimized API methods with caching
export const optimizedApi = {
  // GET with caching
  async get(url, options = {}) {
    const { useCache = true, cacheTTL, ...axiosOptions } = options;
    const cacheKey = `GET:${url}:${JSON.stringify(axiosOptions.params || {})}`;

    // Check cache first
    if (useCache) {
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        console.log(`Cache hit: ${url}`);
        return { data: cachedData, fromCache: true };
      }
    }

    try {
      const response = await api.get(url, axiosOptions);
      
      // Cache successful responses
      if (useCache && response.status === 200) {
        apiCache.set(cacheKey, response.data, cacheTTL);
      }

      return response;
    } catch (error) {
      console.error(`API GET error: ${url}`, error);
      throw error;
    }
  },

  // POST without caching
  async post(url, data, options = {}) {
    try {
      const response = await api.post(url, data, options);
      
      // Invalidate related cache entries
      this.invalidateCache(url);
      
      return response;
    } catch (error) {
      console.error(`API POST error: ${url}`, error);
      throw error;
    }
  },

  // PUT without caching
  async put(url, data, options = {}) {
    try {
      const response = await api.put(url, data, options);
      
      // Invalidate related cache entries
      this.invalidateCache(url);
      
      return response;
    } catch (error) {
      console.error(`API PUT error: ${url}`, error);
      throw error;
    }
  },

  // DELETE without caching
  async delete(url, options = {}) {
    try {
      const response = await api.delete(url, options);
      
      // Invalidate related cache entries
      this.invalidateCache(url);
      
      return response;
    } catch (error) {
      console.error(`API DELETE error: ${url}`, error);
      throw error;
    }
  },

  // Invalidate cache entries related to a URL
  invalidateCache(url) {
    const urlPath = url.split('?')[0]; // Remove query params
    
    for (const key of apiCache.cache.keys()) {
      if (key.includes(urlPath)) {
        apiCache.delete(key);
      }
    }
  },

  // Clear all cache
  clearCache() {
    apiCache.clear();
  },

  // Health check with minimal caching
  async healthCheck() {
    return this.get('/api/health', { useCache: true, cacheTTL: 30000 }); // 30 seconds
  },

  // Batch requests for better performance
  async batch(requests) {
    try {
      const promises = requests.map(request => {
        const { method, url, data, options } = request;
        return this[method.toLowerCase()](url, data, options);
      });

      return await Promise.allSettled(promises);
    } catch (error) {
      console.error('Batch request error:', error);
      throw error;
    }
  },

  // Prefetch data for better UX
  async prefetch(urls) {
    const prefetchPromises = urls.map(url => {
      return this.get(url, { useCache: true }).catch(error => {
        console.warn(`Prefetch failed for ${url}:`, error);
        return null;
      });
    });

    return Promise.allSettled(prefetchPromises);
  }
};

// Performance monitoring
export const apiPerformance = {
  requests: [],
  
  addRequest(url, method, duration, success) {
    this.requests.push({
      url,
      method,
      duration,
      success,
      timestamp: Date.now()
    });

    // Keep only last 100 requests
    if (this.requests.length > 100) {
      this.requests.shift();
    }
  },

  getStats() {
    const total = this.requests.length;
    const successful = this.requests.filter(r => r.success).length;
    const failed = total - successful;
    const avgDuration = this.requests.reduce((sum, r) => sum + r.duration, 0) / total;

    return {
      total,
      successful,
      failed,
      successRate: (successful / total) * 100,
      avgDuration: Math.round(avgDuration)
    };
  },

  getSlowestRequests(limit = 10) {
    return [...this.requests]
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }
};

export default optimizedApi;
